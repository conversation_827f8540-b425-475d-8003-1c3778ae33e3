-- SuperUser Administration Schema
-- This creates a separate administrative layer for platform management

-- SuperUser accounts table (separate from tenant users)
CREATE TABLE superusers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    
    -- <PERSON><PERSON><PERSON> permissions
    permissions TEXT[] DEFAULT ARRAY[
        'platform:admin',
        'organizations:manage',
        'users:manage', 
        'system:monitor',
        'billing:manage',
        'support:access',
        'security:audit',
        'extensions:manage'
    ],
    
    -- Security
    mfa_enabled BOOLEAN DEFAULT false,
    mfa_secret VARCHAR(255),
    last_login_at TIMESTAMPTZ,
    last_login_ip INET,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    
    -- Metadata
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Platform statistics and monitoring
CREATE TABLE platform_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Time period
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('hour', 'day', 'week', 'month')),
    
    -- Organization metrics
    total_organizations INTEGER DEFAULT 0,
    active_organizations INTEGER DEFAULT 0,
    new_organizations INTEGER DEFAULT 0,
    churned_organizations INTEGER DEFAULT 0,
    
    -- User metrics
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    
    -- Usage metrics
    total_claims INTEGER DEFAULT 0,
    new_claims INTEGER DEFAULT 0,
    total_documents INTEGER DEFAULT 0,
    storage_used_gb DECIMAL(10,2) DEFAULT 0,
    
    -- Performance metrics
    avg_response_time_ms INTEGER DEFAULT 0,
    error_rate DECIMAL(5,2) DEFAULT 0,
    uptime_percentage DECIMAL(5,2) DEFAULT 100,
    
    -- Financial metrics (if applicable)
    total_revenue DECIMAL(12,2) DEFAULT 0,
    mrr DECIMAL(12,2) DEFAULT 0, -- Monthly Recurring Revenue
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(period_start, period_type)
);

-- System health monitoring
CREATE TABLE system_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Service status
    service_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('healthy', 'degraded', 'down', 'maintenance')),
    
    -- Metrics
    response_time_ms INTEGER,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    
    -- Database metrics
    db_connections INTEGER,
    db_query_time_ms INTEGER,
    
    -- Error tracking
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    
    -- Timestamps
    checked_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Index for time-series queries
    INDEX idx_system_health_service_time (service_name, checked_at DESC)
);

-- Organization management for SuperUser
CREATE TABLE organization_admin (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Subscription and billing
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_status VARCHAR(20) DEFAULT 'active' CHECK (subscription_status IN ('active', 'suspended', 'cancelled', 'trial')),
    billing_email VARCHAR(255),
    next_billing_date DATE,
    
    -- Usage limits and quotas
    user_limit INTEGER DEFAULT 10,
    storage_limit_gb INTEGER DEFAULT 10,
    api_calls_limit INTEGER DEFAULT 1000,
    
    -- Current usage
    current_users INTEGER DEFAULT 0,
    current_storage_gb DECIMAL(10,2) DEFAULT 0,
    current_api_calls INTEGER DEFAULT 0,
    
    -- Flags and controls
    is_suspended BOOLEAN DEFAULT false,
    suspension_reason TEXT,
    suspended_at TIMESTAMPTZ,
    suspended_by UUID REFERENCES superusers(id),
    
    -- Support and notes
    support_priority VARCHAR(20) DEFAULT 'standard' CHECK (support_priority IN ('low', 'standard', 'high', 'critical')),
    admin_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Security audit logs (SuperUser actions)
CREATE TABLE superuser_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Actor
    superuser_id UUID REFERENCES superusers(id) ON DELETE SET NULL,
    superuser_email VARCHAR(255),
    
    -- Action details
    action VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    
    -- Target
    target_type VARCHAR(50), -- 'organization', 'user', 'system', etc.
    target_id VARCHAR(255),
    target_name VARCHAR(255),
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    
    -- Changes
    old_values JSONB,
    new_values JSONB,
    
    -- Metadata
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System alerts and notifications
CREATE TABLE system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Alert details
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Source
    source_service VARCHAR(100),
    source_data JSONB DEFAULT '{}',
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved')),
    acknowledged_by UUID REFERENCES superusers(id),
    acknowledged_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    
    -- Auto-resolution
    auto_resolve_after INTERVAL,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Feature flags and extensions
CREATE TABLE feature_flags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Flag details
    flag_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Targeting
    is_global BOOLEAN DEFAULT false,
    target_organizations UUID[] DEFAULT '{}',
    target_plans TEXT[] DEFAULT '{}',
    
    -- Status
    is_enabled BOOLEAN DEFAULT false,
    rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    
    -- Configuration
    config JSONB DEFAULT '{}',
    
    -- Metadata
    created_by UUID REFERENCES superusers(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_platform_stats_period ON platform_stats(period_start DESC, period_type);
CREATE INDEX idx_organization_admin_org_id ON organization_admin(organization_id);
CREATE INDEX idx_organization_admin_status ON organization_admin(subscription_status);
CREATE INDEX idx_superuser_audit_logs_superuser ON superuser_audit_logs(superuser_id, created_at DESC);
CREATE INDEX idx_superuser_audit_logs_target ON superuser_audit_logs(target_type, target_id);
CREATE INDEX idx_system_alerts_status ON system_alerts(status, severity, created_at DESC);
CREATE INDEX idx_feature_flags_enabled ON feature_flags(is_enabled, is_global);

-- Functions for SuperUser operations
CREATE OR REPLACE FUNCTION log_superuser_action(
    p_superuser_id UUID,
    p_action VARCHAR(100),
    p_description TEXT,
    p_target_type VARCHAR(50) DEFAULT NULL,
    p_target_id VARCHAR(255) DEFAULT NULL,
    p_target_name VARCHAR(255) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_severity VARCHAR(20) DEFAULT 'info',
    p_ip_address INET DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_log_id UUID;
    v_superuser_email VARCHAR(255);
BEGIN
    -- Get superuser email
    SELECT email INTO v_superuser_email
    FROM superusers 
    WHERE id = p_superuser_id;
    
    -- Insert audit log
    INSERT INTO superuser_audit_logs (
        superuser_id,
        superuser_email,
        action,
        description,
        target_type,
        target_id,
        target_name,
        old_values,
        new_values,
        severity,
        ip_address
    ) VALUES (
        p_superuser_id,
        v_superuser_email,
        p_action,
        p_description,
        p_target_type,
        p_target_id,
        p_target_name,
        p_old_values,
        p_new_values,
        p_severity,
        p_ip_address
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to suspend organization
CREATE OR REPLACE FUNCTION suspend_organization(
    p_organization_id UUID,
    p_reason TEXT,
    p_suspended_by UUID
) RETURNS BOOLEAN AS $$
BEGIN
    -- Update organization admin record
    UPDATE organization_admin 
    SET 
        is_suspended = true,
        suspension_reason = p_reason,
        suspended_at = NOW(),
        suspended_by = p_suspended_by,
        updated_at = NOW()
    WHERE organization_id = p_organization_id;
    
    -- Deactivate all users in the organization
    UPDATE users 
    SET is_active = false, updated_at = NOW()
    WHERE organization_id = p_organization_id;
    
    -- Log the action
    PERFORM log_superuser_action(
        p_suspended_by,
        'organization_suspended',
        format('Organization suspended: %s', p_reason),
        'organization',
        p_organization_id::TEXT,
        (SELECT name FROM organizations WHERE id = p_organization_id),
        NULL,
        jsonb_build_object('suspended', true, 'reason', p_reason),
        'warning'
    );
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reactivate organization
CREATE OR REPLACE FUNCTION reactivate_organization(
    p_organization_id UUID,
    p_reactivated_by UUID
) RETURNS BOOLEAN AS $$
BEGIN
    -- Update organization admin record
    UPDATE organization_admin 
    SET 
        is_suspended = false,
        suspension_reason = NULL,
        suspended_at = NULL,
        suspended_by = NULL,
        updated_at = NOW()
    WHERE organization_id = p_organization_id;
    
    -- Reactivate all users in the organization
    UPDATE users 
    SET is_active = true, updated_at = NOW()
    WHERE organization_id = p_organization_id;
    
    -- Log the action
    PERFORM log_superuser_action(
        p_reactivated_by,
        'organization_reactivated',
        'Organization reactivated',
        'organization',
        p_organization_id::TEXT,
        (SELECT name FROM organizations WHERE id = p_organization_id),
        NULL,
        jsonb_build_object('suspended', false),
        'info'
    );
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default SuperUser (you'll need to update the password hash)
INSERT INTO superusers (
    email,
    password_hash,
    name,
    permissions
) VALUES (
    '<EMAIL>',
    '$2b$12$placeholder_hash_update_this', -- Update with actual hash
    'Platform Administrator',
    ARRAY[
        'platform:admin',
        'organizations:manage',
        'users:manage',
        'system:monitor',
        'billing:manage',
        'support:access',
        'security:audit',
        'extensions:manage'
    ]
) ON CONFLICT (email) DO NOTHING;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON superusers TO authenticated;
GRANT SELECT ON platform_stats TO authenticated;
GRANT SELECT ON system_health TO authenticated;
GRANT SELECT, INSERT, UPDATE ON organization_admin TO authenticated;
GRANT SELECT, INSERT ON superuser_audit_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON system_alerts TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON feature_flags TO authenticated;

-- RLS Policies (SuperUser only)
ALTER TABLE superusers ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_health ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE superuser_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;

-- Only SuperUsers can access these tables
CREATE POLICY "SuperUsers only" ON superusers FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON platform_stats FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON system_health FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON organization_admin FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON superuser_audit_logs FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON system_alerts FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));

CREATE POLICY "SuperUsers only" ON feature_flags FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' IN (SELECT email FROM superusers WHERE is_active = true));
