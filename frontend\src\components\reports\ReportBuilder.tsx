import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  Save, 
  Play, 
  Calendar, 
  Mail, 
  Download, 
  Filter, 
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Table,
  Settings,
  Clock,
  Users,
  DollarSign
} from 'lucide-react'

interface ReportField {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'currency' | 'percentage'
  category: 'claims' | 'users' | 'financial' | 'performance'
}

interface ReportFilter {
  field: string
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between' | 'in'
  value: any
}

interface ReportConfig {
  id?: string
  name: string
  description: string
  fields: string[]
  filters: ReportFilter[]
  groupBy?: string
  sortBy?: string
  sortOrder: 'asc' | 'desc'
  chartType: 'table' | 'bar' | 'line' | 'pie' | 'donut'
  schedule?: {
    enabled: boolean
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
    time: string
  }
}

export const ReportBuilder: React.FC = () => {
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    fields: [],
    filters: [],
    sortOrder: 'desc',
    chartType: 'table'
  })
  
  const [availableFields] = useState<ReportField[]>([
    // Claims fields
    { id: 'claim_number', name: 'Claim Number', type: 'text', category: 'claims' },
    { id: 'claimant_name', name: 'Claimant Name', type: 'text', category: 'claims' },
    { id: 'amount', name: 'Claim Amount', type: 'currency', category: 'claims' },
    { id: 'status', name: 'Status', type: 'text', category: 'claims' },
    { id: 'created_at', name: 'Created Date', type: 'date', category: 'claims' },
    { id: 'completed_at', name: 'Completed Date', type: 'date', category: 'claims' },
    { id: 'recovery_amount', name: 'Recovery Amount', type: 'currency', category: 'claims' },
    
    // User fields
    { id: 'agent_name', name: 'Agent Name', type: 'text', category: 'users' },
    { id: 'agent_email', name: 'Agent Email', type: 'text', category: 'users' },
    { id: 'user_role', name: 'User Role', type: 'text', category: 'users' },
    
    // Financial fields
    { id: 'total_recovered', name: 'Total Recovered', type: 'currency', category: 'financial' },
    { id: 'commission', name: 'Commission', type: 'currency', category: 'financial' },
    { id: 'success_rate', name: 'Success Rate', type: 'percentage', category: 'performance' },
    { id: 'avg_resolution_time', name: 'Avg Resolution Time', type: 'number', category: 'performance' }
  ])

  const [savedReports, setSavedReports] = useState<ReportConfig[]>([])
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [previewData, setPreviewData] = useState<any[]>([])

  const fieldCategories = {
    claims: { label: 'Claims', icon: BarChart3, color: 'blue' },
    users: { label: 'Users', icon: Users, color: 'green' },
    financial: { label: 'Financial', icon: DollarSign, color: 'purple' },
    performance: { label: 'Performance', icon: LineChart, color: 'orange' }
  }

  const addField = (fieldId: string) => {
    if (!reportConfig.fields.includes(fieldId)) {
      setReportConfig(prev => ({
        ...prev,
        fields: [...prev.fields, fieldId]
      }))
    }
  }

  const removeField = (fieldId: string) => {
    setReportConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(f => f !== fieldId)
    }))
  }

  const addFilter = () => {
    setReportConfig(prev => ({
      ...prev,
      filters: [...prev.filters, { field: '', operator: 'equals', value: '' }]
    }))
  }

  const updateFilter = (index: number, updates: Partial<ReportFilter>) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.map((filter, i) => 
        i === index ? { ...filter, ...updates } : filter
      )
    }))
  }

  const removeFilter = (index: number) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.filter((_, i) => i !== index)
    }))
  }

  const generatePreview = async () => {
    setIsPreviewMode(true)
    
    // Mock data generation based on selected fields
    const mockData = Array.from({ length: 10 }, (_, i) => {
      const row: any = {}
      reportConfig.fields.forEach(fieldId => {
        const field = availableFields.find(f => f.id === fieldId)
        if (field) {
          switch (field.type) {
            case 'text':
              row[fieldId] = `Sample ${field.name} ${i + 1}`
              break
            case 'number':
              row[fieldId] = Math.floor(Math.random() * 100) + 1
              break
            case 'currency':
              row[fieldId] = Math.floor(Math.random() * 50000) + 1000
              break
            case 'percentage':
              row[fieldId] = Math.floor(Math.random() * 100)
              break
            case 'date':
              row[fieldId] = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
              break
          }
        }
      })
      return row
    })
    
    setPreviewData(mockData)
  }

  const saveReport = async () => {
    const newReport = {
      ...reportConfig,
      id: Date.now().toString()
    }
    
    setSavedReports(prev => [...prev, newReport])
    
    // In real implementation, save to database
    console.log('Saving report:', newReport)
  }

  const formatValue = (value: any, type: string) => {
    switch (type) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value)
      case 'percentage':
        return `${value}%`
      case 'date':
        return new Date(value).toLocaleDateString()
      default:
        return value
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Report Builder
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Create custom reports and schedule automated delivery
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={generatePreview}
            disabled={reportConfig.fields.length === 0}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
          >
            <Play className="h-4 w-4 mr-2" />
            Preview
          </button>
          
          <button
            onClick={saveReport}
            disabled={!reportConfig.name || reportConfig.fields.length === 0}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Report
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Report Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Report Details
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Report Name *
                </label>
                <input
                  type="text"
                  value={reportConfig.name}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="Enter report name..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={reportConfig.description}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  placeholder="Describe what this report shows..."
                />
              </div>
            </div>
          </div>

          {/* Field Selection */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Select Fields
            </h3>
            
            {Object.entries(fieldCategories).map(([category, config]) => {
              const Icon = config.icon
              const categoryFields = availableFields.filter(f => f.category === category)
              
              return (
                <div key={category} className="mb-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <Icon className={`h-4 w-4 text-${config.color}-600`} />
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {config.label}
                    </h4>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    {categoryFields.map(field => (
                      <button
                        key={field.id}
                        onClick={() => 
                          reportConfig.fields.includes(field.id) 
                            ? removeField(field.id)
                            : addField(field.id)
                        }
                        className={`text-left px-3 py-2 rounded-md text-sm transition-colors ${
                          reportConfig.fields.includes(field.id)
                            ? `bg-${config.color}-100 text-${config.color}-800 dark:bg-${config.color}-900/20 dark:text-${config.color}-400`
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        {field.name}
                      </button>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Filters
              </h3>
              <button
                onClick={addFilter}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Filter
              </button>
            </div>
            
            {reportConfig.filters.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No filters added. Click "Add Filter" to filter your data.
              </p>
            ) : (
              <div className="space-y-3">
                {reportConfig.filters.map((filter, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <select
                      value={filter.field}
                      onChange={(e) => updateFilter(index, { field: e.target.value })}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm"
                    >
                      <option value="">Select field...</option>
                      {availableFields.map(field => (
                        <option key={field.id} value={field.id}>{field.name}</option>
                      ))}
                    </select>
                    
                    <select
                      value={filter.operator}
                      onChange={(e) => updateFilter(index, { operator: e.target.value as any })}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm"
                    >
                      <option value="equals">Equals</option>
                      <option value="contains">Contains</option>
                      <option value="greater_than">Greater than</option>
                      <option value="less_than">Less than</option>
                      <option value="between">Between</option>
                    </select>
                    
                    <input
                      type="text"
                      value={filter.value}
                      onChange={(e) => updateFilter(index, { value: e.target.value })}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm"
                      placeholder="Enter value..."
                    />
                    
                    <button
                      onClick={() => removeFilter(index)}
                      className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Chart Type & Sorting */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Display Options
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Chart Type
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { value: 'table', label: 'Table', icon: Table },
                    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
                    { value: 'line', label: 'Line Chart', icon: LineChart },
                    { value: 'pie', label: 'Pie Chart', icon: PieChart }
                  ].map(type => {
                    const Icon = type.icon
                    return (
                      <button
                        key={type.value}
                        onClick={() => setReportConfig(prev => ({ ...prev, chartType: type.value as any }))}
                        className={`flex items-center justify-center px-3 py-2 rounded-md text-sm transition-colors ${
                          reportConfig.chartType === type.value
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        {type.label}
                      </button>
                    )
                  })}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Sort Order
                </label>
                <select
                  value={reportConfig.sortOrder}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, sortOrder: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="asc">Ascending</option>
                  <option value="desc">Descending</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Preview & Saved Reports */}
        <div className="space-y-6">
          {/* Preview */}
          {isPreviewMode && (
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Preview
              </h3>
              
              {reportConfig.chartType === 'table' ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        {reportConfig.fields.map(fieldId => {
                          const field = availableFields.find(f => f.id === fieldId)
                          return (
                            <th key={fieldId} className="text-left py-2 px-3 font-medium text-gray-900 dark:text-gray-100">
                              {field?.name}
                            </th>
                          )
                        })}
                      </tr>
                    </thead>
                    <tbody>
                      {previewData.slice(0, 5).map((row, index) => (
                        <tr key={index} className="border-b border-gray-100 dark:border-gray-700">
                          {reportConfig.fields.map(fieldId => {
                            const field = availableFields.find(f => f.id === fieldId)
                            return (
                              <td key={fieldId} className="py-2 px-3 text-gray-700 dark:text-gray-300">
                                {formatValue(row[fieldId], field?.type || 'text')}
                              </td>
                            )
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="h-48 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {reportConfig.chartType.charAt(0).toUpperCase() + reportConfig.chartType.slice(1)} chart preview
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Saved Reports */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Saved Reports
            </h3>
            
            {savedReports.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No saved reports yet. Create and save your first report.
              </p>
            ) : (
              <div className="space-y-3">
                {savedReports.map(report => (
                  <div key={report.id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {report.name}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {report.fields.length} fields • {report.chartType}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                          <Download className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                          <Settings className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Schedule Options */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Schedule Delivery
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={reportConfig.schedule?.enabled || false}
                  onChange={(e) => setReportConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, enabled: e.target.checked, frequency: 'weekly', recipients: [], time: '09:00' }
                  }))}
                  className="rounded border-gray-300 dark:border-gray-600"
                />
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enable scheduled delivery
                </label>
              </div>
              
              {reportConfig.schedule?.enabled && (
                <div className="space-y-3 pl-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Frequency
                    </label>
                    <select
                      value={reportConfig.schedule.frequency}
                      onChange={(e) => setReportConfig(prev => ({
                        ...prev,
                        schedule: { ...prev.schedule!, frequency: e.target.value as any }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Time
                    </label>
                    <input
                      type="time"
                      value={reportConfig.schedule.time}
                      onChange={(e) => setReportConfig(prev => ({
                        ...prev,
                        schedule: { ...prev.schedule!, time: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
