import React, { useState, useEffect } from 'react'
import { 
  Clock, 
  User, 
  FileText, 
  Mail, 
  Shield, 
  Download, 
  Upload, 
  Edit, 
  Trash2,
  AlertTriangle,
  CheckCircle,
  Info,
  Filter,
  Calendar,
  Search
} from 'lucide-react'
import { supabase } from '../../lib/supabase'

interface ActivityLog {
  id: string
  activity_type: string
  title: string
  description?: string
  user_email?: string
  user_role?: string
  target_type?: string
  target_name?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  changes_summary?: string
  metadata?: Record<string, any>
  severity: 'info' | 'warning' | 'error' | 'critical'
  created_at: string
}

interface ActivityTimelineProps {
  targetType?: string
  targetId?: string
  showFilters?: boolean
  maxItems?: number
}

export const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  targetType,
  targetId,
  showFilters = true,
  maxItems = 50
}) => {
  const [activities, setActivities] = useState<ActivityLog[]>([])
  const [filteredActivities, setFilteredActivities] = useState<ActivityLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState({
    activityType: '',
    severity: '',
    user: '',
    dateRange: '7d'
  })
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    loadActivities()
  }, [targetType, targetId])

  useEffect(() => {
    applyFilters()
  }, [activities, filters, searchQuery])

  const loadActivities = async () => {
    setIsLoading(true)
    try {
      let query = supabase
        .from('activity_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(maxItems)

      // Filter by target if specified
      if (targetType && targetId) {
        query = query
          .eq('target_type', targetType)
          .eq('target_id', targetId)
      }

      const { data, error } = await query

      if (error) throw error

      // Mock data for demonstration
      const mockActivities: ActivityLog[] = [
        {
          id: '1',
          activity_type: 'claim_created',
          title: 'Claim Created',
          description: 'New claim #12345 created for John Doe',
          user_email: '<EMAIL>',
          user_role: 'senior_agent',
          target_type: 'claim',
          target_name: 'Claim #12345',
          metadata: { amount: 15000, state: 'CA' },
          severity: 'info',
          created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
        },
        {
          id: '2',
          activity_type: 'document_uploaded',
          title: 'Document Uploaded',
          description: 'Power of Attorney document uploaded',
          user_email: '<EMAIL>',
          user_role: 'senior_agent',
          target_type: 'document',
          target_name: 'POA_12345.pdf',
          metadata: { file_size: '2.4 MB', file_type: 'application/pdf' },
          severity: 'info',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
        },
        {
          id: '3',
          activity_type: 'status_changed',
          title: 'Status Updated',
          description: 'Claim status changed from pending to active',
          user_email: '<EMAIL>',
          user_role: 'admin',
          target_type: 'claim',
          target_name: 'Claim #12345',
          old_values: { status: 'pending' },
          new_values: { status: 'active' },
          changes_summary: 'status: "pending" → "active"',
          severity: 'info',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString() // 4 hours ago
        },
        {
          id: '4',
          activity_type: 'email_sent',
          title: 'Email Sent',
          description: 'Initial contact email sent to claimant',
          user_email: '<EMAIL>',
          user_role: 'senior_agent',
          target_type: 'claim',
          target_name: 'Claim #12345',
          metadata: { template: 'initial_contact', recipient: '<EMAIL>' },
          severity: 'info',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString() // 6 hours ago
        },
        {
          id: '5',
          activity_type: 'security_event',
          title: 'Failed Login Attempt',
          description: 'Multiple failed login attempts detected',
          user_email: '<EMAIL>',
          target_type: 'user',
          target_name: 'Security System',
          metadata: { ip_address: '*************', attempts: 5 },
          severity: 'warning',
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString() // 8 hours ago
        }
      ]

      setActivities(data || mockActivities)
    } catch (error) {
      console.error('Error loading activities:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...activities]

    // Apply activity type filter
    if (filters.activityType) {
      filtered = filtered.filter(activity => 
        activity.activity_type === filters.activityType
      )
    }

    // Apply severity filter
    if (filters.severity) {
      filtered = filtered.filter(activity => 
        activity.severity === filters.severity
      )
    }

    // Apply user filter
    if (filters.user) {
      filtered = filtered.filter(activity => 
        activity.user_email?.toLowerCase().includes(filters.user.toLowerCase())
      )
    }

    // Apply date range filter
    if (filters.dateRange) {
      const now = new Date()
      let cutoffDate = new Date()
      
      switch (filters.dateRange) {
        case '1h':
          cutoffDate.setHours(now.getHours() - 1)
          break
        case '24h':
          cutoffDate.setDate(now.getDate() - 1)
          break
        case '7d':
          cutoffDate.setDate(now.getDate() - 7)
          break
        case '30d':
          cutoffDate.setDate(now.getDate() - 30)
          break
        default:
          cutoffDate = new Date(0) // No filter
      }

      filtered = filtered.filter(activity => 
        new Date(activity.created_at) >= cutoffDate
      )
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(activity =>
        activity.title.toLowerCase().includes(query) ||
        activity.description?.toLowerCase().includes(query) ||
        activity.user_email?.toLowerCase().includes(query) ||
        activity.target_name?.toLowerCase().includes(query)
      )
    }

    setFilteredActivities(filtered)
  }

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'user_login':
      case 'user_logout':
        return User
      case 'claim_created':
      case 'claim_updated':
      case 'claim_assigned':
        return FileText
      case 'document_uploaded':
      case 'document_downloaded':
        return Upload
      case 'document_deleted':
        return Trash2
      case 'email_sent':
        return Mail
      case 'status_changed':
      case 'claim_completed':
        return Edit
      case 'security_event':
        return Shield
      case 'data_export':
        return Download
      default:
        return Clock
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'error':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
      default:
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'error':
        return AlertTriangle
      case 'warning':
        return AlertTriangle
      default:
        return Info
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    
    return date.toLocaleDateString()
  }

  const activityTypes = [
    { value: '', label: 'All Activities' },
    { value: 'claim_created', label: 'Claim Created' },
    { value: 'claim_updated', label: 'Claim Updated' },
    { value: 'document_uploaded', label: 'Document Uploaded' },
    { value: 'email_sent', label: 'Email Sent' },
    { value: 'status_changed', label: 'Status Changed' },
    { value: 'security_event', label: 'Security Event' }
  ]

  const severityLevels = [
    { value: '', label: 'All Severities' },
    { value: 'info', label: 'Info' },
    { value: 'warning', label: 'Warning' },
    { value: 'error', label: 'Error' },
    { value: 'critical', label: 'Critical' }
  ]

  const dateRanges = [
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '', label: 'All Time' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Activity Timeline
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {filteredActivities.length} activities found
          </p>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Activity Type
              </label>
              <select
                value={filters.activityType}
                onChange={(e) => setFilters({ ...filters, activityType: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {activityTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Severity
              </label>
              <select
                value={filters.severity}
                onChange={(e) => setFilters({ ...filters, severity: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {severityLevels.map(level => (
                  <option key={level.value} value={level.value}>{level.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Time Range
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {dateRanges.map(range => (
                  <option key={range.value} value={range.value}>{range.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                User
              </label>
              <input
                type="text"
                placeholder="Filter by user email..."
                value={filters.user}
                onChange={(e) => setFilters({ ...filters, user: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>

          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search activities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        </div>
      )}

      {/* Timeline */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading activities...</p>
          </div>
        ) : filteredActivities.length === 0 ? (
          <div className="p-8 text-center">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No activities found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your filters or search terms
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredActivities.map((activity, index) => {
              const Icon = getActivityIcon(activity.activity_type)
              const SeverityIcon = getSeverityIcon(activity.severity)
              
              return (
                <div key={activity.id} className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Icon */}
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${getSeverityColor(activity.severity)}`}>
                      <Icon className="h-5 w-5" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {activity.title}
                          </h3>
                          {activity.severity !== 'info' && (
                            <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(activity.severity)}`}>
                              <SeverityIcon className="h-3 w-3 mr-1" />
                              {activity.severity}
                            </div>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimeAgo(activity.created_at)}
                        </span>
                      </div>

                      {activity.description && (
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                          {activity.description}
                        </p>
                      )}

                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        {activity.user_email && (
                          <span>by {activity.user_email}</span>
                        )}
                        {activity.target_name && (
                          <span>→ {activity.target_name}</span>
                        )}
                      </div>

                      {activity.changes_summary && (
                        <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                          <span className="font-medium">Changes: </span>
                          {activity.changes_summary}
                        </div>
                      )}

                      {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                        <div className="mt-2">
                          <details className="text-xs">
                            <summary className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                              View metadata
                            </summary>
                            <pre className="mt-1 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs overflow-x-auto">
                              {JSON.stringify(activity.metadata, null, 2)}
                            </pre>
                          </details>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
