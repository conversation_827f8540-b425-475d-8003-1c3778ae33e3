// ===================================================================
// COMPREHENSIVE DATABASE TESTING EXECUTION SCRIPT
// AssetHunterPro - Complete Database Optimization & Testing Suite
// ===================================================================

import { runDatabaseOptimizationTests } from './database-optimization-test.js';
import { runDatabaseStressTest } from './database-stress-test.js';
import fs from 'fs';
import path from 'path';

// Test execution configuration
const TEST_CONFIG = {
  runOptimizationTests: true,
  runStressTests: true,
  runSQLHealthCheck: true,
  generateReport: true,
  saveResults: true
};

// Combined results storage
let combinedResults = {
  timestamp: new Date().toISOString(),
  testSuites: {
    optimization: null,
    stress: null,
    sqlHealth: null
  },
  summary: {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    warningTests: 0,
    overallHealth: 'UNKNOWN',
    productionReady: false
  },
  recommendations: []
};

// Utility functions
function logSection(title) {
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 ${title}`);
  console.log('='.repeat(60));
}

function logResult(message, type = 'info') {
  const icons = {
    info: '📊',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    recommendation: '💡'
  };
  console.log(`${icons[type] || '📊'} ${message}`);
}

// ===================================================================
// SQL HEALTH CHECK EXECUTION
// ===================================================================

async function runSQLHealthCheck() {
  logSection('SQL HEALTH CHECK');
  
  try {
    // Read the SQL health check script
    const sqlScript = fs.readFileSync('./database/comprehensive-optimization-check.sql', 'utf8');
    
    logResult('SQL health check script loaded successfully');
    logResult('⚠️  Please run the following SQL script in your Supabase SQL Editor:', 'warning');
    logResult('   File: ./database/comprehensive-optimization-check.sql', 'info');
    logResult('   This will provide detailed database statistics and recommendations', 'info');
    
    return {
      executed: false,
      message: 'SQL script prepared for manual execution',
      scriptPath: './database/comprehensive-optimization-check.sql'
    };
  } catch (error) {
    logResult(`Failed to load SQL health check script: ${error.message}`, 'error');
    return {
      executed: false,
      error: error.message
    };
  }
}

// ===================================================================
// COMPREHENSIVE ANALYSIS
// ===================================================================

function analyzeResults() {
  logSection('COMPREHENSIVE ANALYSIS');
  
  const { optimization, stress } = combinedResults.testSuites;
  
  // Calculate overall statistics
  if (optimization) {
    combinedResults.summary.totalTests += optimization.summary.total;
    combinedResults.summary.passedTests += optimization.summary.passed;
    combinedResults.summary.failedTests += optimization.summary.failed;
    combinedResults.summary.warningTests += optimization.summary.warnings;
  }
  
  if (stress) {
    combinedResults.summary.totalTests += stress.totalQueries;
    combinedResults.summary.passedTests += stress.successfulQueries;
    combinedResults.summary.failedTests += stress.failedQueries;
  }
  
  // Determine overall health
  const failureRate = combinedResults.summary.failedTests / combinedResults.summary.totalTests;
  const warningRate = combinedResults.summary.warningTests / combinedResults.summary.totalTests;
  
  if (failureRate === 0 && warningRate < 0.1) {
    combinedResults.summary.overallHealth = 'EXCELLENT';
  } else if (failureRate < 0.05 && warningRate < 0.2) {
    combinedResults.summary.overallHealth = 'GOOD';
  } else if (failureRate < 0.1 && warningRate < 0.3) {
    combinedResults.summary.overallHealth = 'NEEDS_ATTENTION';
  } else {
    combinedResults.summary.overallHealth = 'CRITICAL';
  }
  
  // Production readiness assessment
  const productionCriteria = {
    noFailures: combinedResults.summary.failedTests === 0,
    lowWarnings: warningRate < 0.15,
    goodPerformance: optimization?.performance?.averageQueryTime < 1000,
    goodStressTest: stress ? (stress.successfulQueries / stress.totalQueries) > 0.95 : true
  };
  
  combinedResults.summary.productionReady = Object.values(productionCriteria).every(Boolean);
  
  // Generate recommendations
  const recommendations = [];
  
  if (combinedResults.summary.failedTests > 0) {
    recommendations.push({
      priority: 'CRITICAL',
      category: 'Reliability',
      issue: `${combinedResults.summary.failedTests} test failures detected`,
      action: 'Address all test failures before production deployment'
    });
  }
  
  if (optimization?.performance?.slowQueries?.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Performance',
      issue: `${optimization.performance.slowQueries.length} slow queries detected`,
      action: 'Optimize slow queries by adding indexes or refactoring'
    });
  }
  
  if (optimization?.performance?.averageQueryTime > 1000) {
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Performance',
      issue: `Average query time is ${optimization.performance.averageQueryTime.toFixed(2)}ms`,
      action: 'Consider database optimization, caching, or connection pooling'
    });
  }
  
  if (stress && (stress.successfulQueries / stress.totalQueries) < 0.95) {
    recommendations.push({
      priority: 'HIGH',
      category: 'Reliability',
      issue: `Stress test success rate is ${((stress.successfulQueries / stress.totalQueries) * 100).toFixed(2)}%`,
      action: 'Investigate and fix reliability issues under load'
    });
  }
  
  combinedResults.recommendations = recommendations;
  
  // Display analysis results
  logResult(`Overall Health: ${combinedResults.summary.overallHealth}`);
  logResult(`Production Ready: ${combinedResults.summary.productionReady ? 'YES' : 'NO'}`);
  logResult(`Total Tests: ${combinedResults.summary.totalTests}`);
  logResult(`Success Rate: ${((combinedResults.summary.passedTests / combinedResults.summary.totalTests) * 100).toFixed(2)}%`);
  
  if (recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}`);
      console.log(`   Issue: ${rec.issue}`);
      console.log(`   Action: ${rec.action}`);
    });
  }
}

// ===================================================================
// REPORT GENERATION
// ===================================================================

function generateReport() {
  logSection('GENERATING COMPREHENSIVE REPORT');
  
  const reportData = {
    ...combinedResults,
    metadata: {
      testDate: new Date().toISOString(),
      testDuration: Date.now() - new Date(combinedResults.timestamp).getTime(),
      environment: 'production-testing',
      database: 'Supabase PostgreSQL'
    }
  };
  
  // Save JSON report
  const jsonReportPath = `database-comprehensive-report-${Date.now()}.json`;
  fs.writeFileSync(jsonReportPath, JSON.stringify(reportData, null, 2));
  
  // Generate HTML report
  const htmlReport = generateHTMLReport(reportData);
  const htmlReportPath = `database-comprehensive-report-${Date.now()}.html`;
  fs.writeFileSync(htmlReportPath, htmlReport);
  
  logResult(`JSON report saved: ${jsonReportPath}`, 'success');
  logResult(`HTML report saved: ${htmlReportPath}`, 'success');
  
  return { jsonReportPath, htmlReportPath };
}

function generateHTMLReport(data) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AssetHunterPro Database Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .excellent { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .good { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .critical { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-width: 150px; text-align: center; }
        .recommendations { margin-top: 20px; }
        .recommendation { margin: 10px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .priority-critical { border-left-color: #dc3545; }
        .priority-high { border-left-color: #fd7e14; }
        .priority-medium { border-left-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AssetHunterPro Database Test Report</h1>
            <p>Generated: ${data.metadata.testDate}</p>
        </div>
        
        <div class="status ${data.summary.overallHealth.toLowerCase()}">
            <h2>Overall Health: ${data.summary.overallHealth}</h2>
            <p>Production Ready: ${data.summary.productionReady ? '✅ YES' : '❌ NO'}</p>
        </div>
        
        <div class="metrics">
            <div class="metric">
                <h3>Total Tests</h3>
                <p>${data.summary.totalTests}</p>
            </div>
            <div class="metric">
                <h3>Passed</h3>
                <p>${data.summary.passedTests}</p>
            </div>
            <div class="metric">
                <h3>Failed</h3>
                <p>${data.summary.failedTests}</p>
            </div>
            <div class="metric">
                <h3>Warnings</h3>
                <p>${data.summary.warningTests}</p>
            </div>
            <div class="metric">
                <h3>Success Rate</h3>
                <p>${((data.summary.passedTests / data.summary.totalTests) * 100).toFixed(2)}%</p>
            </div>
        </div>
        
        ${data.recommendations.length > 0 ? `
        <div class="recommendations">
            <h2>🎯 Recommendations</h2>
            ${data.recommendations.map(rec => `
                <div class="recommendation priority-${rec.priority.toLowerCase()}">
                    <h4>[${rec.priority}] ${rec.category}</h4>
                    <p><strong>Issue:</strong> ${rec.issue}</p>
                    <p><strong>Action:</strong> ${rec.action}</p>
                </div>
            `).join('')}
        </div>
        ` : ''}
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>Report generated by AssetHunterPro Database Testing Suite</p>
        </div>
    </div>
</body>
</html>
  `;
}

// ===================================================================
// MAIN EXECUTION
// ===================================================================

async function runComprehensiveDatabaseTests() {
  console.log('🚀 STARTING COMPREHENSIVE DATABASE TESTING SUITE');
  console.log('==================================================');
  console.log(`Timestamp: ${combinedResults.timestamp}`);
  console.log('');
  
  const overallStartTime = Date.now();
  
  try {
    // Run optimization tests
    if (TEST_CONFIG.runOptimizationTests) {
      logSection('OPTIMIZATION TESTS');
      combinedResults.testSuites.optimization = await runDatabaseOptimizationTests();
      logResult('Optimization tests completed', 'success');
    }
    
    // Run stress tests
    if (TEST_CONFIG.runStressTests) {
      logSection('STRESS TESTS');
      combinedResults.testSuites.stress = await runDatabaseStressTest();
      logResult('Stress tests completed', 'success');
    }
    
    // Run SQL health check
    if (TEST_CONFIG.runSQLHealthCheck) {
      combinedResults.testSuites.sqlHealth = await runSQLHealthCheck();
    }
    
    // Analyze results
    analyzeResults();
    
    // Generate report
    if (TEST_CONFIG.generateReport) {
      const reportPaths = generateReport();
      logResult('Comprehensive report generated', 'success');
    }
    
    const overallDuration = Date.now() - overallStartTime;
    
    logSection('FINAL SUMMARY');
    logResult(`Total execution time: ${overallDuration}ms`);
    logResult(`Database health: ${combinedResults.summary.overallHealth}`);
    logResult(`Production ready: ${combinedResults.summary.productionReady ? 'YES' : 'NO'}`);
    
    if (!combinedResults.summary.productionReady) {
      logResult('⚠️  Address the recommendations above before production deployment', 'warning');
    } else {
      logResult('🎉 Database is optimized and ready for production!', 'success');
    }
    
  } catch (error) {
    logResult(`Test execution failed: ${error.message}`, 'error');
    console.error(error);
  }
  
  return combinedResults;
}

// Export for use in other scripts
export { runComprehensiveDatabaseTests, combinedResults };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runComprehensiveDatabaseTests().catch(console.error);
}
