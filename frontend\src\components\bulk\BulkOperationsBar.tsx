import React, { useState } from 'react'
import { 
  X, 
  Check, 
  Trash2, 
  Archive, 
  Mail, 
  UserPlus, 
  Download, 
  Edit3,
  AlertTriangle,
  ChevronDown
} from 'lucide-react'
import type { BulkOperation } from '../../hooks/useBulkOperations'

interface BulkOperationsBarProps {
  selectedCount: number
  totalCount: number
  operations: BulkOperation[]
  onClearSelection: () => void
  onExecuteOperation: (operation: BulkOperation) => Promise<void>
  isOperationInProgress: boolean
}

export const BulkOperationsBar: React.FC<BulkOperationsBarProps> = ({
  selectedCount,
  totalCount,
  operations,
  onClearSelection,
  onExecuteOperation,
  isOperationInProgress
}) => {
  const [showConfirmation, setShowConfirmation] = useState<BulkOperation | null>(null)
  const [showDropdown, setShowDropdown] = useState(false)

  if (selectedCount === 0) return null

  const handleOperationClick = async (operation: BulkOperation) => {
    if (operation.requiresConfirmation) {
      setShowConfirmation(operation)
    } else {
      await onExecuteOperation(operation)
    }
  }

  const handleConfirmOperation = async () => {
    if (showConfirmation) {
      await onExecuteOperation(showConfirmation)
      setShowConfirmation(null)
    }
  }

  const primaryOperations = operations.slice(0, 3)
  const secondaryOperations = operations.slice(3)

  return (
    <>
      {/* Bulk Operations Bar */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center space-x-4">
            {/* Selection Info */}
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded flex items-center justify-center">
                <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {selectedCount} of {totalCount} selected
              </span>
            </div>

            {/* Divider */}
            <div className="h-6 w-px bg-gray-200 dark:bg-gray-600" />

            {/* Primary Operations */}
            <div className="flex items-center space-x-2">
              {primaryOperations.map((operation) => {
                const Icon = operation.icon
                const variantClasses = {
                  default: 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                  danger: 'text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20',
                  warning: 'text-yellow-700 dark:text-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/20'
                }

                return (
                  <button
                    key={operation.id}
                    onClick={() => handleOperationClick(operation)}
                    disabled={isOperationInProgress}
                    className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                      variantClasses[operation.variant || 'default']
                    }`}
                    title={operation.label}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {operation.label}
                  </button>
                )
              })}

              {/* More Operations Dropdown */}
              {secondaryOperations.length > 0 && (
                <div className="relative">
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    More
                    <ChevronDown className="h-4 w-4 ml-1" />
                  </button>

                  {showDropdown && (
                    <div className="absolute bottom-full mb-2 right-0 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1">
                      {secondaryOperations.map((operation) => {
                        const Icon = operation.icon
                        return (
                          <button
                            key={operation.id}
                            onClick={() => {
                              handleOperationClick(operation)
                              setShowDropdown(false)
                            }}
                            disabled={isOperationInProgress}
                            className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <Icon className="h-4 w-4 mr-3" />
                            {operation.label}
                          </button>
                        )
                      })}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Divider */}
            <div className="h-6 w-px bg-gray-200 dark:bg-gray-600" />

            {/* Clear Selection */}
            <button
              onClick={onClearSelection}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded"
              title="Clear selection"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {/* Progress Indicator */}
          {isOperationInProgress && (
            <div className="mt-2">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div className="bg-blue-600 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Confirm {showConfirmation.label}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  This action affects {selectedCount} items
                </p>
              </div>
            </div>

            <p className="text-sm text-gray-700 dark:text-gray-300 mb-6">
              {showConfirmation.confirmationMessage || 
               `Are you sure you want to ${showConfirmation.label.toLowerCase()} ${selectedCount} items? This action cannot be undone.`}
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmation(null)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmOperation}
                disabled={isOperationInProgress}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed ${
                  showConfirmation.variant === 'danger'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isOperationInProgress ? 'Processing...' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </>
  )
}

// Common bulk operations for claims
export const claimsBulkOperations: BulkOperation[] = [
  {
    id: 'assign',
    label: 'Assign Agent',
    icon: UserPlus,
    action: async (selectedIds: string[]) => {
      // Implementation for assigning agent
      console.log('Assigning agent to claims:', selectedIds)
    }
  },
  {
    id: 'update-status',
    label: 'Update Status',
    icon: Edit3,
    action: async (selectedIds: string[]) => {
      // Implementation for updating status
      console.log('Updating status for claims:', selectedIds)
    }
  },
  {
    id: 'send-email',
    label: 'Send Email',
    icon: Mail,
    action: async (selectedIds: string[]) => {
      // Implementation for sending bulk emails
      console.log('Sending emails for claims:', selectedIds)
    }
  },
  {
    id: 'export',
    label: 'Export',
    icon: Download,
    action: async (selectedIds: string[]) => {
      // Implementation for exporting selected claims
      console.log('Exporting claims:', selectedIds)
    }
  },
  {
    id: 'archive',
    label: 'Archive',
    icon: Archive,
    action: async (selectedIds: string[]) => {
      // Implementation for archiving claims
      console.log('Archiving claims:', selectedIds)
    },
    requiresConfirmation: true,
    variant: 'warning'
  },
  {
    id: 'delete',
    label: 'Delete',
    icon: Trash2,
    action: async (selectedIds: string[]) => {
      // Implementation for deleting claims
      console.log('Deleting claims:', selectedIds)
    },
    requiresConfirmation: true,
    confirmationMessage: 'This will permanently delete the selected claims and all associated data.',
    variant: 'danger'
  }
]
