// Comprehensive Database Testing Suite
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://hhjfltgvnkeugftabzjl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoamZsdGd2bmtldWdmdGFiempsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTQ2NTQsImV4cCI6MjA2Mzg5MDY1NH0.i7s3ValZ_I9ncz70AT4QmOCh7S-lGbtrKY7dFs16Q_Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test results tracking
let testResults = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: { total: 0, passed: 0, failed: 0, warnings: 0 },
  performance: { connectionTime: 0, queryTimes: [], slowQueries: [] }
};

function logTest(name, status, details = '', duration = 0) {
  const test = { name, status, details, duration, timestamp: new Date().toISOString() };
  testResults.tests.push(test);
  testResults.summary.total++;
  
  if (status === 'PASS') {
    testResults.summary.passed++;
    console.log(`✅ ${name} - ${details} (${duration}ms)`);
  } else if (status === 'FAIL') {
    testResults.summary.failed++;
    console.log(`❌ ${name} - ${details} (${duration}ms)`);
  } else if (status === 'WARN') {
    testResults.summary.warnings++;
    console.log(`⚠️  ${name} - ${details} (${duration}ms)`);
  }
}

async function testDatabaseConnection() {
  console.log('\n🔍 TESTING DATABASE CONNECTION...');
  console.log('=====================================');
  
  const startTime = Date.now();
  
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    const connectionTime = Date.now() - startTime;
    testResults.performance.connectionTime = connectionTime;
    
    if (error) {
      logTest('Database Connection', 'FAIL', `Connection failed: ${error.message}`, connectionTime);
      return false;
    }
    
    logTest('Database Connection', 'PASS', 'Successfully connected to Supabase', connectionTime);
    return true;
  } catch (error) {
    const connectionTime = Date.now() - startTime;
    logTest('Database Connection', 'FAIL', `Connection error: ${error.message}`, connectionTime);
    return false;
  }
}

async function testTableExistence() {
  console.log('\n📋 TESTING TABLE EXISTENCE...');
  console.log('===============================');
  
  const expectedTables = [
    'users', 'claims', 'teams', 'import_batches', 'batch_records',
    'claim_activities', 'claim_contacts', 'claim_documents', 
    'state_mapping_templates', 'standard_fields', 'upload_sessions',
    'permissions', 'business_rules', 'performance_metrics'
  ];
  
  for (const table of expectedTables) {
    const startTime = Date.now();
    
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      const duration = Date.now() - startTime;
      
      if (error) {
        logTest(`Table: ${table}`, 'FAIL', `Table not accessible: ${error.message}`, duration);
      } else {
        logTest(`Table: ${table}`, 'PASS', 'Table exists and accessible', duration);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Table: ${table}`, 'FAIL', `Error checking table: ${error.message}`, duration);
    }
  }
}

async function testQueryPerformance() {
  console.log('\n⚡ TESTING QUERY PERFORMANCE...');
  console.log('================================');
  
  const performanceTests = [
    {
      name: 'Simple User Count',
      query: () => supabase.from('users').select('count'),
      threshold: 500
    },
    {
      name: 'Claims List Query',
      query: () => supabase.from('claims').select('*').limit(10),
      threshold: 1000
    },
    {
      name: 'Teams with Members',
      query: () => supabase.from('teams').select('*').limit(5),
      threshold: 800
    },
    {
      name: 'Recent Activities',
      query: () => supabase.from('claim_activities').select('*').order('created_at', { ascending: false }).limit(20),
      threshold: 1200
    }
  ];
  
  for (const test of performanceTests) {
    const startTime = Date.now();
    
    try {
      const { data, error } = await test.query();
      const duration = Date.now() - startTime;
      testResults.performance.queryTimes.push({ name: test.name, duration });
      
      if (error) {
        logTest(`Performance: ${test.name}`, 'FAIL', `Query failed: ${error.message}`, duration);
      } else if (duration > test.threshold) {
        testResults.performance.slowQueries.push({ name: test.name, duration });
        logTest(`Performance: ${test.name}`, 'WARN', `Query slow: ${duration}ms (threshold: ${test.threshold}ms)`, duration);
      } else {
        logTest(`Performance: ${test.name}`, 'PASS', `Query fast: ${duration}ms`, duration);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Performance: ${test.name}`, 'FAIL', `Query error: ${error.message}`, duration);
    }
  }
}

async function testDataIntegrity() {
  console.log('\n🔒 TESTING DATA INTEGRITY...');
  console.log('==============================');
  
  try {
    // Test 1: Check for users
    const startTime1 = Date.now();
    const { data: users, error: usersError } = await supabase.from('users').select('id, email').limit(5);
    const duration1 = Date.now() - startTime1;
    
    if (usersError) {
      logTest('Data Integrity: Users', 'FAIL', `Users query failed: ${usersError.message}`, duration1);
    } else {
      logTest('Data Integrity: Users', 'PASS', `Found ${users?.length || 0} user records`, duration1);
    }
    
    // Test 2: Check for claims
    const startTime2 = Date.now();
    const { data: claims, error: claimsError } = await supabase.from('claims').select('id, claim_number').limit(5);
    const duration2 = Date.now() - startTime2;
    
    if (claimsError) {
      logTest('Data Integrity: Claims', 'FAIL', `Claims query failed: ${claimsError.message}`, duration2);
    } else {
      logTest('Data Integrity: Claims', 'PASS', `Found ${claims?.length || 0} claim records`, duration2);
    }
    
    // Test 3: Check for teams
    const startTime3 = Date.now();
    const { data: teams, error: teamsError } = await supabase.from('teams').select('id, name').limit(5);
    const duration3 = Date.now() - startTime3;
    
    if (teamsError) {
      logTest('Data Integrity: Teams', 'FAIL', `Teams query failed: ${teamsError.message}`, duration3);
    } else {
      logTest('Data Integrity: Teams', 'PASS', `Found ${teams?.length || 0} team records`, duration3);
    }
    
  } catch (error) {
    logTest('Data Integrity: General', 'FAIL', `Integrity test error: ${error.message}`, 0);
  }
}

async function testConcurrentQueries() {
  console.log('\n🏋️ TESTING CONCURRENT QUERIES...');
  console.log('==================================');
  
  const startTime = Date.now();
  
  try {
    // Run 5 concurrent queries
    const concurrentQueries = [
      supabase.from('users').select('id').limit(3),
      supabase.from('claims').select('id').limit(3),
      supabase.from('teams').select('id').limit(3),
      supabase.from('permissions').select('id').limit(3),
      supabase.from('claim_activities').select('id').limit(3)
    ];
    
    const results = await Promise.all(concurrentQueries);
    const duration = Date.now() - startTime;
    
    const failures = results.filter(r => r.error);
    
    if (failures.length > 0) {
      logTest('Concurrent Queries', 'FAIL', `${failures.length} out of 5 queries failed`, duration);
    } else if (duration > 3000) {
      logTest('Concurrent Queries', 'WARN', `All queries succeeded but slow: ${duration}ms`, duration);
    } else {
      logTest('Concurrent Queries', 'PASS', `All 5 concurrent queries succeeded in ${duration}ms`, duration);
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    logTest('Concurrent Queries', 'FAIL', `Concurrent test error: ${error.message}`, duration);
  }
}

async function runComprehensiveTests() {
  console.log('🚀 STARTING COMPREHENSIVE DATABASE TESTS');
  console.log('==========================================');
  console.log(`Timestamp: ${testResults.timestamp}`);
  console.log(`Target Database: ${supabaseUrl}`);
  
  const overallStartTime = Date.now();
  
  // Run all test suites
  const connected = await testDatabaseConnection();
  if (!connected) {
    console.log('\n❌ Database connection failed. Aborting tests.');
    return;
  }
  
  await testTableExistence();
  await testQueryPerformance();
  await testDataIntegrity();
  await testConcurrentQueries();
  
  // Calculate summary statistics
  const overallDuration = Date.now() - overallStartTime;
  const avgQueryTime = testResults.performance.queryTimes.length > 0 
    ? testResults.performance.queryTimes.reduce((sum, q) => sum + q.duration, 0) / testResults.performance.queryTimes.length 
    : 0;
  
  // Generate final report
  console.log('\n📊 COMPREHENSIVE TEST SUMMARY');
  console.log('==============================');
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`✅ Passed: ${testResults.summary.passed}`);
  console.log(`❌ Failed: ${testResults.summary.failed}`);
  console.log(`⚠️  Warnings: ${testResults.summary.warnings}`);
  console.log(`⏱️  Overall Duration: ${overallDuration}ms`);
  console.log(`📈 Average Query Time: ${avgQueryTime.toFixed(2)}ms`);
  console.log(`🔌 Connection Time: ${testResults.performance.connectionTime}ms`);
  
  // Success rate calculation
  const successRate = ((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1);
  console.log(`🎯 Success Rate: ${successRate}%`);
  
  if (testResults.performance.slowQueries.length > 0) {
    console.log(`\n⚠️  Slow Queries Detected: ${testResults.performance.slowQueries.length}`);
    testResults.performance.slowQueries.forEach(q => {
      console.log(`   - ${q.name}: ${q.duration}ms`);
    });
  }
  
  // Overall health assessment
  let healthStatus = 'EXCELLENT';
  if (testResults.summary.failed > 0) {
    healthStatus = 'CRITICAL';
  } else if (testResults.summary.warnings > 3 || testResults.performance.slowQueries.length > 2) {
    healthStatus = 'NEEDS_ATTENTION';
  } else if (testResults.summary.warnings > 0 || testResults.performance.slowQueries.length > 0) {
    healthStatus = 'GOOD';
  }
  
  console.log(`\n🏥 Database Health: ${healthStatus}`);
  
  // Production readiness assessment
  const productionReady = testResults.summary.failed === 0 && 
                         testResults.performance.slowQueries.length < 3 &&
                         avgQueryTime < 1000;
  
  console.log(`🚀 Production Ready: ${productionReady ? 'YES' : 'NO'}`);
  
  if (!productionReady) {
    console.log('\n⚠️  PRODUCTION READINESS ISSUES:');
    if (testResults.summary.failed > 0) {
      console.log(`   - ${testResults.summary.failed} critical test failures`);
    }
    if (testResults.performance.slowQueries.length >= 3) {
      console.log(`   - ${testResults.performance.slowQueries.length} slow queries need optimization`);
    }
    if (avgQueryTime >= 1000) {
      console.log(`   - Average query time too high: ${avgQueryTime.toFixed(2)}ms`);
    }
  } else {
    console.log('\n🎉 Database is optimized and ready for production!');
  }
  
  // Recommendations
  console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
  if (testResults.summary.failed > 0) {
    console.log('   🔴 CRITICAL: Address all test failures before production');
  }
  if (testResults.performance.slowQueries.length > 0) {
    console.log('   🟡 PERFORMANCE: Optimize slow queries with indexes');
  }
  if (avgQueryTime > 500) {
    console.log('   🟡 PERFORMANCE: Consider connection pooling or caching');
  }
  if (testResults.performance.connectionTime > 1000) {
    console.log('   🟡 NETWORK: Consider geographic proximity optimization');
  }
  if (testResults.summary.failed === 0 && testResults.summary.warnings === 0) {
    console.log('   ✅ No immediate optimizations needed - excellent performance!');
  }
  
  return testResults;
}

// Run the comprehensive tests
runComprehensiveTests().catch(console.error);
