import React, { useState, useEffect } from 'react'
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Clock, 
  Target,
  Download,
  Filter,
  Calendar,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Activity
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalClaims: number
    activeClaims: number
    completedClaims: number
    totalRecovered: number
    averageRecoveryTime: number
    successRate: number
    monthlyGrowth: number
  }
  trends: {
    claimsByMonth: Array<{ month: string; claims: number; recovered: number }>
    recoveryByAgent: Array<{ agent: string; recovered: number; claims: number }>
    claimsByStatus: Array<{ status: string; count: number; percentage: number }>
    performanceMetrics: Array<{ metric: string; current: number; target: number; trend: number }>
  }
  insights: {
    topPerformers: Array<{ name: string; metric: string; value: number }>
    bottlenecks: Array<{ area: string; impact: string; suggestion: string }>
    opportunities: Array<{ type: string; potential: number; effort: string }>
  }
}

export const AdvancedAnalytics: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [dateRange, setDateRange] = useState('30d')
  const [selectedMetric, setSelectedMetric] = useState('recovery')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAnalyticsData()
  }, [dateRange, selectedMetric])

  const loadAnalyticsData = async () => {
    setIsLoading(true)
    try {
      // Mock data - in real implementation, this would come from your analytics service
      const mockData: AnalyticsData = {
        overview: {
          totalClaims: 1247,
          activeClaims: 342,
          completedClaims: 905,
          totalRecovered: 2450000,
          averageRecoveryTime: 45,
          successRate: 72.6,
          monthlyGrowth: 12.5
        },
        trends: {
          claimsByMonth: [
            { month: 'Jan', claims: 89, recovered: 145000 },
            { month: 'Feb', claims: 95, recovered: 167000 },
            { month: 'Mar', claims: 112, recovered: 198000 },
            { month: 'Apr', claims: 108, recovered: 189000 },
            { month: 'May', claims: 125, recovered: 234000 },
            { month: 'Jun', claims: 134, recovered: 267000 }
          ],
          recoveryByAgent: [
            { agent: 'Sarah Johnson', recovered: 456000, claims: 67 },
            { agent: 'Mike Chen', recovered: 398000, claims: 52 },
            { agent: 'Lisa Rodriguez', recovered: 367000, claims: 48 },
            { agent: 'David Kim', recovered: 334000, claims: 45 },
            { agent: 'Emma Wilson', recovered: 298000, claims: 41 }
          ],
          claimsByStatus: [
            { status: 'Completed', count: 905, percentage: 72.6 },
            { status: 'Active', count: 342, percentage: 27.4 },
            { status: 'Pending', count: 156, percentage: 12.5 },
            { status: 'Under Review', count: 89, percentage: 7.1 }
          ],
          performanceMetrics: [
            { metric: 'Recovery Rate', current: 72.6, target: 75, trend: 2.3 },
            { metric: 'Avg. Resolution Time', current: 45, target: 40, trend: -3.2 },
            { metric: 'Client Satisfaction', current: 4.7, target: 4.8, trend: 0.1 },
            { metric: 'Cost per Claim', current: 125, target: 120, trend: -2.1 }
          ]
        },
        insights: {
          topPerformers: [
            { name: 'Sarah Johnson', metric: 'Recovery Amount', value: 456000 },
            { name: 'Q2 2024', metric: 'Best Quarter', value: 234000 },
            { name: 'California', metric: 'Top State', value: 189000 }
          ],
          bottlenecks: [
            { area: 'Document Verification', impact: 'High', suggestion: 'Implement automated verification' },
            { area: 'Client Communication', impact: 'Medium', suggestion: 'Add automated follow-up system' },
            { area: 'Payment Processing', impact: 'Low', suggestion: 'Streamline payment workflows' }
          ],
          opportunities: [
            { type: 'Process Automation', potential: 150000, effort: 'Medium' },
            { type: 'Agent Training', potential: 89000, effort: 'Low' },
            { type: 'Technology Upgrade', potential: 234000, effort: 'High' }
          ]
        }
      }

      setData(mockData)
    } catch (error) {
      console.error('Error loading analytics data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (isLoading || !data) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Advanced Analytics
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive business intelligence and performance insights
          </p>
        </div>

        <div className="flex items-center space-x-4 mt-4 lg:mt-0">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>

          <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Recovered</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatCurrency(data.overview.totalRecovered)}
              </p>
              <p className="text-sm text-green-600 dark:text-green-400">
                +{formatPercentage(data.overview.monthlyGrowth)} this month
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatPercentage(data.overview.successRate)}
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                {data.overview.completedClaims} completed claims
              </p>
            </div>
            <Target className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Recovery Time</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {data.overview.averageRecoveryTime} days
              </p>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                Industry benchmark: 60 days
              </p>
            </div>
            <Clock className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Claims</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {data.overview.activeClaims}
              </p>
              <p className="text-sm text-orange-600 dark:text-orange-400">
                {data.overview.totalClaims} total claims
              </p>
            </div>
            <Activity className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recovery Trends */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Recovery Trends
            </h3>
            <LineChart className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {data.trends.claimsByMonth.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {item.month}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {formatCurrency(item.recovered)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {item.claims} claims
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performers */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Top Performing Agents
            </h3>
            <Users className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {data.trends.recoveryByAgent.map((agent, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {agent.agent.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {agent.agent}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {agent.claims} claims
                    </div>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {formatCurrency(agent.recovered)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
          Performance Metrics vs. Targets
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {data.trends.performanceMetrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {metric.metric}
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {metric.metric.includes('Rate') || metric.metric.includes('Satisfaction') 
                  ? formatPercentage(metric.current)
                  : metric.current
                }
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Target: {metric.metric.includes('Rate') || metric.metric.includes('Satisfaction')
                  ? formatPercentage(metric.target)
                  : metric.target
                }
              </div>
              <div className={`text-xs font-medium ${
                metric.trend > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
              }`}>
                {metric.trend > 0 ? '+' : ''}{metric.trend}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Business Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Top Performers */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            🏆 Top Performers
          </h3>
          <div className="space-y-3">
            {data.insights.topPerformers.map((performer, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {performer.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {performer.metric}
                  </div>
                </div>
                <div className="text-sm font-bold text-green-600 dark:text-green-400">
                  {typeof performer.value === 'number' && performer.value > 1000 
                    ? formatCurrency(performer.value)
                    : performer.value
                  }
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottlenecks */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            ⚠️ Bottlenecks
          </h3>
          <div className="space-y-3">
            {data.insights.bottlenecks.map((bottleneck, index) => (
              <div key={index} className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {bottleneck.area}
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    bottleneck.impact === 'High' 
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      : bottleneck.impact === 'Medium'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  }`}>
                    {bottleneck.impact}
                  </span>
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {bottleneck.suggestion}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Opportunities */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            💡 Opportunities
          </h3>
          <div className="space-y-3">
            {data.insights.opportunities.map((opportunity, index) => (
              <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {opportunity.type}
                  </div>
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded-full">
                    {opportunity.effort}
                  </span>
                </div>
                <div className="text-sm font-bold text-blue-600 dark:text-blue-400">
                  {formatCurrency(opportunity.potential)} potential
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
