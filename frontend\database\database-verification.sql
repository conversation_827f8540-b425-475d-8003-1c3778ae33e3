-- Comprehensive Database Verification and Optimization Check
-- This script verifies all schemas, relationships, indexes, and performance optimizations

-- =============================================================================
-- 1. SCHEMA VALIDATION
-- =============================================================================

DO $$
DECLARE
    missing_tables TEXT[] := '{}';
    table_name TEXT;
    expected_tables TEXT[] := ARRAY[
        'users', 'organizations', 'claims', 'documents', 'activities',
        'subscription_plans', 'organization_subscriptions', 'user_roles', 
        'permissions', 'role_permissions', 'user_permission_overrides',
        'usage_tracking', 'plan_features', 'billing_meters', 'usage_events',
        'usage_aggregates', 'upsell_opportunities', 'mfa_settings',
        'security_audit_logs', 'user_sessions', 'dlp_rules', 'compliance_frameworks'
    ];
BEGIN
    RAISE NOTICE '🔍 VERIFYING DATABASE SCHEMA...';
    RAISE NOTICE '';
    
    -- Check for missing tables
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = table_name) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION '❌ MISSING TABLES: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '✅ All required tables exist';
    END IF;
END $$;

-- =============================================================================
-- 2. FOREIGN KEY CONSTRAINTS VALIDATION
-- =============================================================================

DO $$
DECLARE
    constraint_count INTEGER;
    expected_constraints INTEGER := 25; -- Minimum expected FK constraints
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔗 VERIFYING FOREIGN KEY CONSTRAINTS...';
    
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints 
    WHERE constraint_type = 'FOREIGN KEY';
    
    IF constraint_count < expected_constraints THEN
        RAISE WARNING '⚠️  Only % foreign key constraints found, expected at least %', constraint_count, expected_constraints;
    ELSE
        RAISE NOTICE '✅ Foreign key constraints: % (sufficient)', constraint_count;
    END IF;
    
    -- Check specific critical foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'organization_subscriptions' 
        AND constraint_type = 'FOREIGN KEY'
    ) THEN
        RAISE EXCEPTION '❌ Missing foreign key constraints on organization_subscriptions';
    END IF;
    
    RAISE NOTICE '✅ Critical foreign key constraints verified';
END $$;

-- =============================================================================
-- 3. INDEX OPTIMIZATION VERIFICATION
-- =============================================================================

DO $$
DECLARE
    index_count INTEGER;
    missing_indexes TEXT[] := '{}';
    critical_indexes TEXT[] := ARRAY[
        'idx_users_organization_id',
        'idx_claims_organization_id',
        'idx_organization_subscriptions_org_id',
        'idx_usage_events_org_meter_period',
        'idx_security_audit_logs_org_time',
        'idx_user_sessions_user_active'
    ];
    idx_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VERIFYING DATABASE INDEXES...';
    
    SELECT COUNT(*) INTO index_count FROM pg_indexes WHERE schemaname = 'public';
    RAISE NOTICE '📈 Total indexes found: %', index_count;
    
    -- Check for critical performance indexes
    FOREACH idx_name IN ARRAY critical_indexes
    LOOP
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = idx_name) THEN
            missing_indexes := array_append(missing_indexes, idx_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) > 0 THEN
        RAISE WARNING '⚠️  Missing critical indexes: %', array_to_string(missing_indexes, ', ');
    ELSE
        RAISE NOTICE '✅ All critical performance indexes exist';
    END IF;
END $$;

-- =============================================================================
-- 4. ROW LEVEL SECURITY VERIFICATION
-- =============================================================================

DO $$
DECLARE
    rls_table RECORD;
    rls_enabled_count INTEGER := 0;
    policy_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔐 VERIFYING ROW LEVEL SECURITY...';
    
    -- Check RLS enabled tables
    FOR rls_table IN 
        SELECT tablename, rowsecurity 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('users', 'organizations', 'claims', 'organization_subscriptions', 'usage_events')
    LOOP
        IF rls_table.rowsecurity THEN
            rls_enabled_count := rls_enabled_count + 1;
            RAISE NOTICE '✅ RLS enabled on: %', rls_table.tablename;
        ELSE
            RAISE WARNING '⚠️  RLS not enabled on: %', rls_table.tablename;
        END IF;
    END LOOP;
    
    -- Check policy count
    SELECT COUNT(*) INTO policy_count FROM pg_policies WHERE schemaname = 'public';
    RAISE NOTICE '🛡️  Total RLS policies: %', policy_count;
    
    IF policy_count < 10 THEN
        RAISE WARNING '⚠️  Low number of RLS policies, consider adding more for security';
    END IF;
END $$;

-- =============================================================================
-- 5. DATA TYPE CONSISTENCY CHECK
-- =============================================================================

DO $$
DECLARE
    inconsistent_types TEXT[] := '{}';
    type_issue TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 VERIFYING DATA TYPE CONSISTENCY...';
    
    -- Check for UUID consistency in ID fields
    FOR type_issue IN
        SELECT table_name || '.' || column_name || ' (' || data_type || ')'
        FROM information_schema.columns 
        WHERE column_name LIKE '%_id' 
        AND column_name != 'plan_id' -- plan_id is VARCHAR by design
        AND data_type != 'uuid'
        AND table_schema = 'public'
    LOOP
        inconsistent_types := array_append(inconsistent_types, type_issue);
    END LOOP;
    
    IF array_length(inconsistent_types, 1) > 0 THEN
        RAISE WARNING '⚠️  Inconsistent ID types: %', array_to_string(inconsistent_types, ', ');
    ELSE
        RAISE NOTICE '✅ ID field data types are consistent';
    END IF;
    
    -- Check for proper timestamp fields
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'created_at' AND data_type = 'timestamp with time zone'
    ) THEN
        RAISE WARNING '⚠️  Missing or incorrect timestamp fields';
    ELSE
        RAISE NOTICE '✅ Timestamp fields properly configured';
    END IF;
END $$;

-- =============================================================================
-- 6. PERFORMANCE OPTIMIZATION CHECK
-- =============================================================================

DO $$
DECLARE
    table_stats RECORD;
    large_tables INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '⚡ CHECKING PERFORMANCE OPTIMIZATIONS...';
    
    -- Check for tables that might need partitioning
    FOR table_stats IN
        SELECT 
            schemaname,
            tablename,
            n_tup_ins + n_tup_upd + n_tup_del as total_operations
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        ORDER BY total_operations DESC
        LIMIT 5
    LOOP
        RAISE NOTICE '📊 Table activity - %: % operations', table_stats.tablename, table_stats.total_operations;
        
        IF table_stats.total_operations > 100000 THEN
            large_tables := large_tables + 1;
            RAISE NOTICE '💡 Consider partitioning for: %', table_stats.tablename;
        END IF;
    END LOOP;
    
    -- Check for missing indexes on frequently queried columns
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname LIKE '%created_at%') THEN
        RAISE WARNING '⚠️  Consider adding indexes on created_at columns for time-based queries';
    END IF;
    
    RAISE NOTICE '✅ Performance optimization check complete';
END $$;

-- =============================================================================
-- 7. SUBSCRIPTION PLAN DATA VALIDATION
-- =============================================================================

DO $$
DECLARE
    plan_count INTEGER;
    role_count INTEGER;
    permission_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '💰 VERIFYING SUBSCRIPTION PLAN DATA...';
    
    SELECT COUNT(*) INTO plan_count FROM subscription_plans;
    SELECT COUNT(*) INTO role_count FROM user_roles;
    SELECT COUNT(*) INTO permission_count FROM permissions;
    
    IF plan_count < 7 THEN
        RAISE WARNING '⚠️  Only % subscription plans found, expected 7', plan_count;
    ELSE
        RAISE NOTICE '✅ Subscription plans: %', plan_count;
    END IF;
    
    IF role_count < 8 THEN
        RAISE WARNING '⚠️  Only % user roles found, expected 8', role_count;
    ELSE
        RAISE NOTICE '✅ User roles: %', role_count;
    END IF;
    
    IF permission_count < 25 THEN
        RAISE WARNING '⚠️  Only % permissions found, expected 25+', permission_count;
    ELSE
        RAISE NOTICE '✅ Permissions: %', permission_count;
    END IF;
    
    -- Verify plan pricing logic
    IF EXISTS (
        SELECT 1 FROM subscription_plans 
        WHERE plan_id = 'bronze' AND monthly_price >= (
            SELECT monthly_price FROM subscription_plans WHERE plan_id = 'silver'
        )
    ) THEN
        RAISE EXCEPTION '❌ Pricing logic error: Bronze price >= Silver price';
    END IF;
    
    RAISE NOTICE '✅ Pricing logic verified';
END $$;

-- =============================================================================
-- 8. BILLING METER VALIDATION
-- =============================================================================

DO $$
DECLARE
    meter_count INTEGER;
    config_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 VERIFYING BILLING METERS...';
    
    SELECT COUNT(*) INTO meter_count FROM billing_meters WHERE is_active = true;
    SELECT COUNT(*) INTO config_count FROM plan_meter_configs;
    
    IF meter_count < 8 THEN
        RAISE WARNING '⚠️  Only % active billing meters found, expected 8', meter_count;
    ELSE
        RAISE NOTICE '✅ Active billing meters: %', meter_count;
    END IF;
    
    IF config_count < 30 THEN
        RAISE WARNING '⚠️  Only % plan meter configs found, expected 30+', config_count;
    ELSE
        RAISE NOTICE '✅ Plan meter configurations: %', config_count;
    END IF;
    
    -- Check for proper rate configuration
    IF EXISTS (
        SELECT 1 FROM billing_meters 
        WHERE base_rate <= 0 OR base_rate IS NULL
    ) THEN
        RAISE WARNING '⚠️  Some billing meters have invalid rates';
    ELSE
        RAISE NOTICE '✅ Billing meter rates properly configured';
    END IF;
END $$;

-- =============================================================================
-- 9. SECURITY CONFIGURATION CHECK
-- =============================================================================

DO $$
DECLARE
    framework_count INTEGER;
    audit_function_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔒 VERIFYING SECURITY CONFIGURATION...';
    
    SELECT COUNT(*) INTO framework_count FROM compliance_frameworks WHERE is_active = true;
    
    IF framework_count < 6 THEN
        RAISE WARNING '⚠️  Only % compliance frameworks found, expected 6', framework_count;
    ELSE
        RAISE NOTICE '✅ Compliance frameworks: %', framework_count;
    END IF;
    
    -- Check for security functions
    SELECT EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'log_security_event'
    ) INTO audit_function_exists;
    
    IF NOT audit_function_exists THEN
        RAISE WARNING '⚠️  Security audit function not found';
    ELSE
        RAISE NOTICE '✅ Security audit functions available';
    END IF;
    
    -- Check for proper encryption setup (basic check)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mfa_settings' AND column_name = 'totp_secret'
    ) THEN
        RAISE WARNING '⚠️  MFA configuration table incomplete';
    ELSE
        RAISE NOTICE '✅ MFA configuration table ready';
    END IF;
END $$;

-- =============================================================================
-- 10. FUNCTION AND TRIGGER VALIDATION
-- =============================================================================

DO $$
DECLARE
    function_count INTEGER;
    trigger_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '⚙️  VERIFYING FUNCTIONS AND TRIGGERS...';
    
    SELECT COUNT(*) INTO function_count 
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public' AND p.prokind = 'f';
    
    SELECT COUNT(*) INTO trigger_count FROM pg_trigger WHERE tgisinternal = false;
    
    RAISE NOTICE '🔧 Custom functions: %', function_count;
    RAISE NOTICE '⚡ Triggers: %', trigger_count;
    
    -- Check for critical functions
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'record_usage_event') THEN
        RAISE WARNING '⚠️  Usage tracking function missing';
    ELSE
        RAISE NOTICE '✅ Usage tracking function available';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'check_ip_access') THEN
        RAISE WARNING '⚠️  IP access check function missing';
    ELSE
        RAISE NOTICE '✅ IP access check function available';
    END IF;
END $$;

-- =============================================================================
-- 11. STORAGE AND BUCKET VERIFICATION
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📁 VERIFYING STORAGE CONFIGURATION...';
    
    -- Check if storage buckets exist (this would need to be checked via Supabase API in practice)
    RAISE NOTICE '💡 Manual check required: Verify these storage buckets exist:';
    RAISE NOTICE '   - company-logos (for organization branding)';
    RAISE NOTICE '   - claim-documents (for document storage)';
    RAISE NOTICE '   - user-avatars (for user profile pictures)';
    RAISE NOTICE '   - exports (for data export files)';
    
    RAISE NOTICE '✅ Storage bucket verification noted';
END $$;

-- =============================================================================
-- 12. FINAL OPTIMIZATION RECOMMENDATIONS
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 OPTIMIZATION RECOMMENDATIONS...';
    RAISE NOTICE '';
    
    -- Performance recommendations
    RAISE NOTICE '📈 PERFORMANCE OPTIMIZATIONS:';
    RAISE NOTICE '   ✅ Add composite indexes for common query patterns';
    RAISE NOTICE '   ✅ Consider partitioning for large tables (usage_events, security_audit_logs)';
    RAISE NOTICE '   ✅ Implement connection pooling (PgBouncer recommended)';
    RAISE NOTICE '   ✅ Set up read replicas for analytics queries';
    
    -- Security recommendations
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY ENHANCEMENTS:';
    RAISE NOTICE '   ✅ Enable SSL/TLS for all connections';
    RAISE NOTICE '   ✅ Implement database-level encryption at rest';
    RAISE NOTICE '   ✅ Set up automated backups with point-in-time recovery';
    RAISE NOTICE '   ✅ Configure monitoring and alerting for security events';
    
    -- Monitoring recommendations
    RAISE NOTICE '';
    RAISE NOTICE '📊 MONITORING SETUP:';
    RAISE NOTICE '   ✅ Enable pg_stat_statements for query analysis';
    RAISE NOTICE '   ✅ Set up automated VACUUM and ANALYZE schedules';
    RAISE NOTICE '   ✅ Monitor connection counts and query performance';
    RAISE NOTICE '   ✅ Implement log aggregation for audit trails';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 DATABASE VERIFICATION COMPLETE!';
    RAISE NOTICE '';
END $$;

-- =============================================================================
-- 13. GENERATE OPTIMIZATION SCRIPT
-- =============================================================================

-- Additional indexes for performance optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_claims_status_created 
ON claims(status, created_at) WHERE status IN ('active', 'pending');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_events_billable_period 
ON usage_events(organization_id, billable, billing_period) WHERE billable = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_audit_logs_severity 
ON security_audit_logs(severity, event_timestamp) WHERE severity IN ('high', 'critical');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_expires 
ON user_sessions(expires_at, is_active) WHERE is_active = true;

-- Analyze tables for query planner
ANALYZE users;
ANALYZE organizations;
ANALYZE claims;
ANALYZE subscription_plans;
ANALYZE organization_subscriptions;
ANALYZE usage_events;
ANALYZE usage_aggregates;
ANALYZE security_audit_logs;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ DATABASE OPTIMIZATION COMPLETE!';
    RAISE NOTICE '🚀 AssetHunterPro database is production-ready!';
    RAISE NOTICE '';
END $$;
