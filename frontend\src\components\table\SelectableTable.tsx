import React from 'react'
import { Check, Minus } from 'lucide-react'

interface Column<T> {
  key: keyof T | string
  label: string
  render?: (item: T) => React.ReactNode
  sortable?: boolean
  width?: string
}

interface SelectableTableProps<T> {
  data: T[]
  columns: Column<T>[]
  selectedItems: string[]
  onToggleItem: (id: string) => void
  onToggleAll: (allIds: string[]) => void
  isAllSelected: boolean
  isIndeterminate: boolean
  getItemId: (item: T) => string
  onRowClick?: (item: T) => void
  loading?: boolean
  emptyMessage?: string
}

export function SelectableTable<T>({
  data,
  columns,
  selectedItems,
  onToggleItem,
  onToggleAll,
  isAllSelected,
  isIndeterminate,
  getItemId,
  onRowClick,
  loading = false,
  emptyMessage = 'No data available'
}: SelectableTableProps<T>) {
  const allIds = data.map(getItemId)

  const handleSelectAllChange = () => {
    onToggleAll(allIds)
  }

  const handleRowSelect = (e: React.MouseEvent, id: string) => {
    e.stopPropagation()
    onToggleItem(id)
  }

  const isSelected = (id: string) => selectedItems.includes(id)

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-8 text-center">
          <p className="text-gray-600 dark:text-gray-400">{emptyMessage}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {/* Select All Checkbox */}
              <th className="w-12 px-6 py-3">
                <div className="flex items-center">
                  <button
                    onClick={handleSelectAllChange}
                    className="relative w-4 h-4 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <div
                      className={`absolute inset-0 rounded transition-colors ${
                        isAllSelected
                          ? 'bg-blue-600 border-blue-600'
                          : isIndeterminate
                          ? 'bg-blue-600 border-blue-600'
                          : 'bg-white dark:bg-gray-800'
                      }`}
                    >
                      {isAllSelected && (
                        <Check className="h-3 w-3 text-white absolute top-0.5 left-0.5" />
                      )}
                      {isIndeterminate && !isAllSelected && (
                        <Minus className="h-3 w-3 text-white absolute top-0.5 left-0.5" />
                      )}
                    </div>
                  </button>
                </div>
              </th>

              {/* Column Headers */}
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  style={{ width: column.width }}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>

          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item) => {
              const id = getItemId(item)
              const selected = isSelected(id)

              return (
                <tr
                  key={id}
                  className={`transition-colors ${
                    selected
                      ? 'bg-blue-50 dark:bg-blue-900/20'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  } ${onRowClick ? 'cursor-pointer' : ''}`}
                  onClick={() => onRowClick?.(item)}
                >
                  {/* Row Checkbox */}
                  <td className="w-12 px-6 py-4">
                    <div className="flex items-center">
                      <button
                        onClick={(e) => handleRowSelect(e, id)}
                        className="relative w-4 h-4 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        <div
                          className={`absolute inset-0 rounded transition-colors ${
                            selected
                              ? 'bg-blue-600 border-blue-600'
                              : 'bg-white dark:bg-gray-800'
                          }`}
                        >
                          {selected && (
                            <Check className="h-3 w-3 text-white absolute top-0.5 left-0.5" />
                          )}
                        </div>
                      </button>
                    </div>
                  </td>

                  {/* Row Data */}
                  {columns.map((column) => (
                    <td
                      key={String(column.key)}
                      className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                    >
                      {column.render
                        ? column.render(item)
                        : String((item as any)[column.key] || '')}
                    </td>
                  ))}
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* Selection Summary */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800 px-6 py-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedItems.length} of {data.length} items selected
            </span>
            <button
              onClick={() => onToggleAll([])}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            >
              Clear selection
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// Example usage component for claims
export const ClaimsTable: React.FC<{
  claims: any[]
  bulkOperations: any
}> = ({ claims, bulkOperations }) => {
  const columns = [
    {
      key: 'claim_number',
      label: 'Claim #',
      render: (claim: any) => (
        <span className="font-medium text-blue-600 dark:text-blue-400">
          #{claim.claim_number}
        </span>
      )
    },
    {
      key: 'claimant_name',
      label: 'Claimant',
      render: (claim: any) => (
        <div>
          <div className="font-medium">{claim.claimant_name}</div>
          <div className="text-xs text-gray-500">{claim.claimant_email}</div>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      render: (claim: any) => (
        <span className="font-medium">
          ${claim.amount?.toLocaleString() || '0'}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (claim: any) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          claim.status === 'active' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : claim.status === 'pending'
            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
        }`}>
          {claim.status}
        </span>
      )
    },
    {
      key: 'assigned_agent',
      label: 'Agent',
      render: (claim: any) => claim.assigned_agent || 'Unassigned'
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (claim: any) => new Date(claim.created_at).toLocaleDateString()
    }
  ]

  return (
    <SelectableTable
      data={claims}
      columns={columns}
      selectedItems={bulkOperations.selectedItems}
      onToggleItem={bulkOperations.toggleItem}
      onToggleAll={bulkOperations.toggleSelectAll}
      isAllSelected={bulkOperations.isAllSelected}
      isIndeterminate={bulkOperations.isIndeterminate}
      getItemId={(claim) => claim.id}
      onRowClick={(claim) => {
        // Navigate to claim detail
        console.log('Navigate to claim:', claim.id)
      }}
      emptyMessage="No claims found"
    />
  )
}
