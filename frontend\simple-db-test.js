// Simple Database Connection Test
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://hhjfltgvnkeugftabzjl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoamZsdGd2bmtldWdmdGFiempsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTQ2NTQsImV4cCI6MjA2Mzg5MDY1NH0.i7s3ValZ_I9ncz70AT4QmOCh7S-lGbtrKY7dFs16Q_Q';

console.log('🔍 Testing Supabase Connection...');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('📡 Attempting connection...');
    
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful!');
    console.log('📊 Data received:', data);
    return true;
    
  } catch (err) {
    console.log('❌ Error:', err.message);
    return false;
  }
}

testConnection().then(success => {
  console.log(success ? '🎉 Database is accessible!' : '⚠️ Database connection failed');
  process.exit(success ? 0 : 1);
});
