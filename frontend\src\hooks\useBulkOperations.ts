import { useState, useCallback } from 'react'

export interface BulkOperation {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: (selectedIds: string[]) => Promise<void>
  requiresConfirmation?: boolean
  confirmationMessage?: string
  variant?: 'default' | 'danger' | 'warning'
  permission?: string
}

export interface BulkOperationsState {
  selectedItems: Set<string>
  isAllSelected: boolean
  isIndeterminate: boolean
  totalItems: number
}

export function useBulkOperations(totalItems: number = 0) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isOperationInProgress, setIsOperationInProgress] = useState(false)

  const isAllSelected = selectedItems.size === totalItems && totalItems > 0
  const isIndeterminate = selectedItems.size > 0 && selectedItems.size < totalItems

  const selectItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      newSet.add(id)
      return newSet
    })
  }, [])

  const deselectItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      newSet.delete(id)
      return newSet
    })
  }, [])

  const toggleItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }, [])

  const selectAll = useCallback((allIds: string[]) => {
    setSelectedItems(new Set(allIds))
  }, [])

  const deselectAll = useCallback(() => {
    setSelectedItems(new Set())
  }, [])

  const toggleSelectAll = useCallback((allIds: string[]) => {
    if (isAllSelected) {
      deselectAll()
    } else {
      selectAll(allIds)
    }
  }, [isAllSelected, selectAll, deselectAll])

  const executeOperation = useCallback(async (operation: BulkOperation) => {
    if (selectedItems.size === 0) return

    setIsOperationInProgress(true)
    try {
      await operation.action(Array.from(selectedItems))
      // Clear selection after successful operation
      deselectAll()
    } catch (error) {
      console.error('Bulk operation failed:', error)
      throw error
    } finally {
      setIsOperationInProgress(false)
    }
  }, [selectedItems, deselectAll])

  return {
    selectedItems: Array.from(selectedItems),
    selectedCount: selectedItems.size,
    isAllSelected,
    isIndeterminate,
    isOperationInProgress,
    selectItem,
    deselectItem,
    toggleItem,
    selectAll,
    deselectAll,
    toggleSelectAll,
    executeOperation,
    hasSelection: selectedItems.size > 0
  }
}
