import { supabase } from '../lib/supabase'

export interface SuperUser {
  id: string
  email: string
  name: string
  permissions: string[]
  mfa_enabled: boolean
  last_login_at?: string
  is_active: boolean
  created_at: string
}

export interface PlatformStats {
  period_start: string
  period_end: string
  period_type: 'hour' | 'day' | 'week' | 'month'
  total_organizations: number
  active_organizations: number
  new_organizations: number
  total_users: number
  active_users: number
  new_users: number
  total_claims: number
  new_claims: number
  storage_used_gb: number
  avg_response_time_ms: number
  error_rate: number
  uptime_percentage: number
  total_revenue?: number
  mrr?: number
}

export interface OrganizationAdmin {
  id: string
  organization_id: string
  organization_name?: string
  subscription_plan: string
  subscription_status: 'active' | 'suspended' | 'cancelled' | 'trial'
  billing_email?: string
  next_billing_date?: string
  user_limit: number
  storage_limit_gb: number
  current_users: number
  current_storage_gb: number
  is_suspended: boolean
  suspension_reason?: string
  suspended_at?: string
  support_priority: 'low' | 'standard' | 'high' | 'critical'
  admin_notes?: string
  created_at: string
}

export interface SystemHealth {
  service_name: string
  status: 'healthy' | 'degraded' | 'down' | 'maintenance'
  response_time_ms?: number
  cpu_usage?: number
  memory_usage?: number
  disk_usage?: number
  error_count: number
  last_error?: string
  checked_at: string
}

export interface SystemAlert {
  id: string
  alert_type: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  title: string
  message: string
  source_service?: string
  status: 'active' | 'acknowledged' | 'resolved'
  acknowledged_by?: string
  acknowledged_at?: string
  resolved_at?: string
  created_at: string
}

export interface FeatureFlag {
  id: string
  flag_name: string
  display_name: string
  description?: string
  is_global: boolean
  target_organizations: string[]
  target_plans: string[]
  is_enabled: boolean
  rollout_percentage: number
  config: Record<string, any>
  created_at: string
}

class SuperUserService {
  /**
   * Check if current user is a SuperUser
   */
  async isSuperUser(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return false

      const { data, error } = await supabase
        .from('superusers')
        .select('id')
        .eq('email', user.email)
        .eq('is_active', true)
        .single()

      return !error && !!data
    } catch (error) {
      console.error('Error checking SuperUser status:', error)
      return false
    }
  }

  /**
   * Get current SuperUser profile
   */
  async getCurrentSuperUser(): Promise<SuperUser | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      const { data, error } = await supabase
        .from('superusers')
        .select('*')
        .eq('email', user.email)
        .eq('is_active', true)
        .single()

      if (error) return null
      return data
    } catch (error) {
      console.error('Error getting SuperUser profile:', error)
      return null
    }
  }

  /**
   * Get platform statistics
   */
  async getPlatformStats(
    periodType: 'hour' | 'day' | 'week' | 'month' = 'day',
    limit: number = 30
  ): Promise<PlatformStats[]> {
    try {
      const { data, error } = await supabase
        .from('platform_stats')
        .select('*')
        .eq('period_type', periodType)
        .order('period_start', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting platform stats:', error)
      return []
    }
  }

  /**
   * Get all organizations with admin data
   */
  async getOrganizations(): Promise<OrganizationAdmin[]> {
    try {
      const { data, error } = await supabase
        .from('organization_admin')
        .select(`
          *,
          organizations!inner(name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return (data || []).map(item => ({
        ...item,
        organization_name: item.organizations?.name
      }))
    } catch (error) {
      console.error('Error getting organizations:', error)
      return []
    }
  }

  /**
   * Suspend an organization
   */
  async suspendOrganization(
    organizationId: string,
    reason: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const superUser = await this.getCurrentSuperUser()
      if (!superUser) {
        return { success: false, error: 'Not authorized' }
      }

      const { error } = await supabase.rpc('suspend_organization', {
        p_organization_id: organizationId,
        p_reason: reason,
        p_suspended_by: superUser.id
      })

      if (error) throw error
      return { success: true }
    } catch (error) {
      console.error('Error suspending organization:', error)
      return { success: false, error: 'Failed to suspend organization' }
    }
  }

  /**
   * Reactivate an organization
   */
  async reactivateOrganization(
    organizationId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const superUser = await this.getCurrentSuperUser()
      if (!superUser) {
        return { success: false, error: 'Not authorized' }
      }

      const { error } = await supabase.rpc('reactivate_organization', {
        p_organization_id: organizationId,
        p_reactivated_by: superUser.id
      })

      if (error) throw error
      return { success: true }
    } catch (error) {
      console.error('Error reactivating organization:', error)
      return { success: false, error: 'Failed to reactivate organization' }
    }
  }

  /**
   * Update organization limits and settings
   */
  async updateOrganizationLimits(
    organizationId: string,
    updates: {
      user_limit?: number
      storage_limit_gb?: number
      api_calls_limit?: number
      subscription_plan?: string
      support_priority?: 'low' | 'standard' | 'high' | 'critical'
      admin_notes?: string
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('organization_admin')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('organization_id', organizationId)

      if (error) throw error

      // Log the action
      const superUser = await this.getCurrentSuperUser()
      if (superUser) {
        await this.logSuperUserAction(
          'organization_updated',
          `Organization limits updated`,
          'organization',
          organizationId,
          updates
        )
      }

      return { success: true }
    } catch (error) {
      console.error('Error updating organization limits:', error)
      return { success: false, error: 'Failed to update organization' }
    }
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<SystemHealth[]> {
    try {
      // Get latest health check for each service
      const { data, error } = await supabase
        .from('system_health')
        .select('*')
        .order('checked_at', { ascending: false })

      if (error) throw error

      // Group by service and get latest
      const latestByService = new Map<string, SystemHealth>()
      data?.forEach(health => {
        if (!latestByService.has(health.service_name)) {
          latestByService.set(health.service_name, health)
        }
      })

      return Array.from(latestByService.values())
    } catch (error) {
      console.error('Error getting system health:', error)
      return []
    }
  }

  /**
   * Get system alerts
   */
  async getSystemAlerts(
    status?: 'active' | 'acknowledged' | 'resolved'
  ): Promise<SystemAlert[]> {
    try {
      let query = supabase
        .from('system_alerts')
        .select('*')
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      const { data, error } = await query
      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting system alerts:', error)
      return []
    }
  }

  /**
   * Acknowledge system alert
   */
  async acknowledgeAlert(alertId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const superUser = await this.getCurrentSuperUser()
      if (!superUser) {
        return { success: false, error: 'Not authorized' }
      }

      const { error } = await supabase
        .from('system_alerts')
        .update({
          status: 'acknowledged',
          acknowledged_by: superUser.id,
          acknowledged_at: new Date().toISOString()
        })
        .eq('id', alertId)

      if (error) throw error
      return { success: true }
    } catch (error) {
      console.error('Error acknowledging alert:', error)
      return { success: false, error: 'Failed to acknowledge alert' }
    }
  }

  /**
   * Get feature flags
   */
  async getFeatureFlags(): Promise<FeatureFlag[]> {
    try {
      const { data, error } = await supabase
        .from('feature_flags')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting feature flags:', error)
      return []
    }
  }

  /**
   * Update feature flag
   */
  async updateFeatureFlag(
    flagId: string,
    updates: Partial<FeatureFlag>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('feature_flags')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', flagId)

      if (error) throw error

      // Log the action
      await this.logSuperUserAction(
        'feature_flag_updated',
        `Feature flag updated: ${updates.flag_name || flagId}`,
        'feature_flag',
        flagId,
        updates
      )

      return { success: true }
    } catch (error) {
      console.error('Error updating feature flag:', error)
      return { success: false, error: 'Failed to update feature flag' }
    }
  }

  /**
   * Log SuperUser action
   */
  async logSuperUserAction(
    action: string,
    description: string,
    targetType?: string,
    targetId?: string,
    metadata?: any
  ): Promise<void> {
    try {
      const superUser = await this.getCurrentSuperUser()
      if (!superUser) return

      await supabase.rpc('log_superuser_action', {
        p_superuser_id: superUser.id,
        p_action: action,
        p_description: description,
        p_target_type: targetType,
        p_target_id: targetId,
        p_metadata: metadata ? JSON.stringify(metadata) : null
      })
    } catch (error) {
      console.error('Error logging SuperUser action:', error)
    }
  }

  /**
   * Get SuperUser audit logs
   */
  async getAuditLogs(limit: number = 100): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('superuser_audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting audit logs:', error)
      return []
    }
  }
}

export const superUserService = new SuperUserService()
