// ===================================================================
// ASSET RECOVERY AI SEARCH TEST
// Optimized for Maximum Contact Success & Asset Discovery
// ===================================================================

console.log('🎯 ASSET RECOVERY AI SEARCH TEST');
console.log('=================================');

// Mock the enhanced asset recovery search service
class AssetRecoveryAISearchTester {
  constructor() {
    this.CALIFORNIA_COUNTIES = [
      'Los Angeles', 'San Diego', 'Orange', 'Riverside', 'San Bernardino',
      'Santa Clara', 'Alameda', 'Sacramento', 'Contra Costa', 'Fresno'
    ];
  }

  async searchForAssetRecovery(query) {
    console.log(`\n🎯 ASSET RECOVERY SEARCH: ${query.firstName} ${query.lastName}`);
    console.log(`📍 Location: ${query.state || 'Unknown'}`);
    console.log(`🎯 Purpose: ${query.searchPurpose}`);
    
    const searchStartTime = Date.now();
    
    try {
      // Phase 1: Multi-source identity verification
      console.log('\n🔍 Phase 1: Identity Verification');
      const identityResults = await this.verifyIdentity(query);
      
      // Phase 2: Comprehensive asset discovery
      console.log('\n💰 Phase 2: Asset Discovery');
      const assetResults = await this.discoverAssets(query, identityResults);
      
      // Phase 3: Contact information gathering
      console.log('\n📞 Phase 3: Contact Information Gathering');
      const contactResults = await this.gatherContactInformation(query, identityResults);
      
      // Phase 4: Generate contact strategy
      console.log('\n📋 Phase 4: Contact Strategy Generation');
      const contactStrategy = this.generateContactStrategy(identityResults, assetResults, contactResults);
      
      // Compile comprehensive result
      const result = this.compileAssetRecoveryResult(
        query,
        identityResults,
        assetResults,
        contactResults,
        contactStrategy,
        Date.now() - searchStartTime
      );
      
      return [result];
      
    } catch (error) {
      console.error('❌ Asset recovery search failed:', error);
      throw error;
    }
  }

  async verifyIdentity(query) {
    const nameVariations = this.generateNameVariations(query.firstName, query.lastName);
    console.log(`   📝 Generated ${nameVariations.length} name variations`);
    
    const searches = [
      this.searchVoterRegistration(query, nameVariations),
      this.searchGovernmentRecords(query, nameVariations),
      this.searchSocialMediaProfiles(query, nameVariations),
      this.searchDirectoryServices(query, nameVariations)
    ];
    
    const results = await Promise.all(searches);
    
    const identityConfidence = this.calculateIdentityConfidence(results);
    console.log(`   ✅ Identity confidence: ${(identityConfidence * 100).toFixed(1)}%`);
    
    return {
      nameVariations,
      voterRecord: results[0],
      governmentRecords: results[1],
      socialProfiles: results[2],
      directoryListings: results[3],
      identityConfidence
    };
  }

  async discoverAssets(query, identityData) {
    const assetSearches = [
      this.searchRealEstateAssets(query, identityData),
      this.searchBusinessAssets(query, identityData),
      this.searchFinancialAssets(query, identityData)
    ];
    
    const assetResults = await Promise.all(assetSearches);
    const totalValue = this.calculateTotalAssetValue(assetResults);
    
    console.log(`   🏠 Real Estate: ${assetResults[0].length} properties found`);
    console.log(`   🏢 Business Interests: ${assetResults[1].length} businesses found`);
    console.log(`   💰 Total Estimated Value: $${totalValue.toLocaleString()}`);
    
    return {
      realEstate: assetResults[0],
      businessInterests: assetResults[1],
      financialAssets: assetResults[2],
      totalEstimatedValue: totalValue
    };
  }

  async gatherContactInformation(query, identityData) {
    const contactSearches = [
      this.findCurrentAddress(query, identityData),
      this.findPhoneNumbers(query, identityData),
      this.findEmailAddresses(query, identityData),
      this.findSocialMediaContacts(query, identityData)
    ];
    
    const contactResults = await Promise.all(contactSearches);
    
    console.log(`   📮 Addresses: ${contactResults[0].length} found`);
    console.log(`   📞 Phone Numbers: ${contactResults[1].length} found`);
    console.log(`   📧 Email Addresses: ${contactResults[2].length} found`);
    console.log(`   📱 Social Contacts: ${contactResults[3].length} found`);
    
    return {
      addresses: contactResults[0],
      phoneNumbers: contactResults[1],
      emailAddresses: contactResults[2],
      socialContacts: contactResults[3]
    };
  }

  generateContactStrategy(identityData, assetData, contactData) {
    let recommendedMethod = 'phone';
    let messagingAngle = 'general_asset_recovery';
    let successProbability = 0.6;
    const personalizations = [];
    
    // Determine best contact method
    if (contactData.addresses.length > 0) {
      recommendedMethod = 'mail';
      successProbability += 0.2;
      personalizations.push('current address available');
    }
    
    if (contactData.socialContacts.some(c => c.platform === 'LinkedIn')) {
      recommendedMethod = 'linkedin';
      messagingAngle = 'professional_asset_recovery';
      successProbability += 0.1;
      personalizations.push('professional LinkedIn profile');
    }
    
    // Customize messaging based on assets
    if (assetData.realEstate.length > 0) {
      messagingAngle = 'property_asset_recovery';
      personalizations.push(`property owner in ${assetData.realEstate[0].city}`);
      successProbability += 0.15;
    }
    
    if (assetData.businessInterests.length > 0) {
      messagingAngle = 'business_asset_recovery';
      personalizations.push(`business owner of ${assetData.businessInterests[0].businessName}`);
      successProbability += 0.2;
    }
    
    successProbability = Math.min(0.95, successProbability);
    
    console.log(`   📋 Recommended Method: ${recommendedMethod}`);
    console.log(`   💬 Messaging Angle: ${messagingAngle}`);
    console.log(`   📈 Success Probability: ${(successProbability * 100).toFixed(1)}%`);
    
    return {
      recommendedMethod,
      messagingAngle,
      personalizations,
      bestContactTimes: this.getBestContactTimes(messagingAngle),
      successProbability
    };
  }

  generateNameVariations(firstName, lastName) {
    const variations = new Set();
    
    // Basic variations
    variations.add(`${firstName} ${lastName}`);
    variations.add(`${lastName}, ${firstName}`);
    
    // Nickname variations for Tyjon
    if (firstName === 'Tyjon') {
      ['Ty', 'T.J.', 'TJ'].forEach(nick => {
        variations.add(`${nick} ${lastName}`);
        variations.add(`${lastName}, ${nick}`);
      });
    }
    
    // Initial variations
    variations.add(`${firstName.charAt(0)} ${lastName}`);
    variations.add(`${firstName} ${lastName.charAt(0)}`);
    
    // Suffix variations
    ['Jr', 'Sr', 'II', 'III'].forEach(suffix => {
      variations.add(`${firstName} ${lastName} ${suffix}`);
    });
    
    return Array.from(variations);
  }

  async searchRealEstateAssets(query, identityData) {
    console.log('   🏠 Searching Real Estate Assets...');
    const properties = [];
    
    // Search California counties for Tyjon Hunter
    if (query.state === 'CA') {
      for (const county of this.CALIFORNIA_COUNTIES.slice(0, 3)) { // Search top 3 counties
        await this.delay(300);
        if (Math.random() > 0.7) { // 30% chance per county
          properties.push({
            address: `${Math.floor(Math.random() * 9999)} ${county} Avenue`,
            city: county === 'Los Angeles' ? 'Los Angeles' : county,
            state: 'CA',
            estimatedValue: Math.floor(Math.random() * 500000) + 400000,
            ownershipType: 'sole',
            purchaseDate: '2021-03-15',
            equityEstimate: Math.floor(Math.random() * 200000) + 150000
          });
          console.log(`      ✅ Property found in ${county} County`);
        }
      }
    }
    
    return properties;
  }

  async searchBusinessAssets(query, identityData) {
    console.log('   🏢 Searching Business Assets...');
    const businesses = [];
    
    await this.delay(600);
    
    // Higher chance for business owner
    if (Math.random() > 0.4) {
      businesses.push({
        businessName: 'Hunter Asset Recovery LLC',
        entityType: 'Limited Liability Company',
        status: 'active',
        ownershipPercentage: 100,
        estimatedValue: Math.floor(Math.random() * 200000) + 100000,
        address: '1234 Business Center Dr, Los Angeles, CA'
      });
      console.log('      ✅ Business entity found: Hunter Asset Recovery LLC');
    }
    
    if (Math.random() > 0.6) {
      businesses.push({
        businessName: 'AssetHunterPro',
        entityType: 'Corporation',
        status: 'active',
        ownershipPercentage: 75,
        estimatedValue: Math.floor(Math.random() * 500000) + 250000,
        address: '5678 Innovation Way, Los Angeles, CA'
      });
      console.log('      ✅ Business entity found: AssetHunterPro');
    }
    
    return businesses;
  }

  async searchFinancialAssets(query, identityData) {
    console.log('   💰 Searching Financial Assets...');
    await this.delay(400);
    
    const financialAssets = [];
    
    if (Math.random() > 0.7) {
      financialAssets.push({
        type: 'investment',
        institution: 'Fidelity Investments',
        estimatedValue: Math.floor(Math.random() * 100000) + 50000,
        confidence: 0.75
      });
    }
    
    return financialAssets;
  }

  async findCurrentAddress(query, identityData) {
    console.log('   📮 Finding Current Address...');
    await this.delay(400);
    
    if (Math.random() > 0.3) {
      return [{
        address: `${Math.floor(Math.random() * 9999)} Hunter Street`,
        city: 'Los Angeles',
        state: 'CA',
        zip: '90210',
        type: 'current',
        confidence: 0.9,
        lastVerified: new Date()
      }];
    }
    
    return [];
  }

  async findPhoneNumbers(query, identityData) {
    console.log('   📞 Finding Phone Numbers...');
    await this.delay(300);
    
    if (Math.random() > 0.5) {
      return [{
        number: `(${Math.floor(Math.random() * 800) + 200}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        type: 'mobile',
        confidence: 0.8,
        isActive: true
      }];
    }
    
    return [];
  }

  async findEmailAddresses(query, identityData) {
    console.log('   📧 Finding Email Addresses...');
    await this.delay(300);
    
    if (Math.random() > 0.6) {
      return [{
        email: `${query.firstName.toLowerCase()}.${query.lastName.toLowerCase()}@gmail.com`,
        confidence: 0.7,
        isActive: true
      }];
    }
    
    return [];
  }

  async findSocialMediaContacts(query, identityData) {
    console.log('   📱 Finding Social Media Contacts...');
    await this.delay(400);
    
    const socialContacts = [];
    
    if (Math.random() > 0.4) {
      socialContacts.push({
        platform: 'LinkedIn',
        profileUrl: `https://linkedin.com/in/${query.firstName.toLowerCase()}-${query.lastName.toLowerCase()}`,
        isActive: true,
        confidence: 0.85,
        professionalInfo: {
          currentPosition: 'CEO & Founder',
          company: 'AssetHunterPro',
          industry: 'Financial Services - Asset Recovery'
        }
      });
    }
    
    return socialContacts;
  }

  compileAssetRecoveryResult(query, identityData, assetData, contactData, contactStrategy, searchDuration) {
    const confidence = this.calculateOverallConfidence(identityData, assetData, contactData);
    const contactPriority = this.determineContactPriority(assetData.totalEstimatedValue, confidence);
    
    return {
      id: `asset_recovery_${Date.now()}`,
      confidence,
      contactPriority,
      fullName: `${query.firstName} ${query.lastName}`,
      nameVariations: identityData.nameVariations,
      identityConfidence: identityData.identityConfidence,
      primaryContact: this.determinePrimaryContact(contactData, contactStrategy),
      alternateContacts: this.compileAlternateContacts(contactData),
      discoveredAssets: {
        realEstate: assetData.realEstate,
        businessInterests: assetData.businessInterests,
        financialAssets: assetData.financialAssets,
        estimatedTotalValue: assetData.totalEstimatedValue
      },
      contactStrategy,
      searchMetadata: {
        dataSourcesUsed: [
          'California Voter Registration',
          'Multi-County Property Records',
          'Secretary of State Business Records',
          'Social Media Intelligence',
          'Directory Services',
          'Professional License Database'
        ],
        searchCost: 0.75,
        searchDuration,
        lastUpdated: new Date()
      }
    };
  }

  // Utility methods
  calculateIdentityConfidence(results) {
    const foundCount = results.filter(r => r && r.found).length;
    return Math.min(0.95, 0.3 + (foundCount * 0.15));
  }

  calculateTotalAssetValue(assetResults) {
    let total = 0;
    assetResults.forEach(assets => {
      if (Array.isArray(assets)) {
        assets.forEach(asset => {
          total += asset.estimatedValue || asset.equityEstimate || 0;
        });
      }
    });
    return total;
  }

  calculateOverallConfidence(identityData, assetData, contactData) {
    let confidence = identityData.identityConfidence * 0.4;
    
    if (assetData.realEstate.length > 0) confidence += 0.25;
    if (assetData.businessInterests.length > 0) confidence += 0.2;
    if (contactData.addresses.length > 0) confidence += 0.15;
    
    return Math.min(0.95, confidence);
  }

  determineContactPriority(assetValue, confidence) {
    if (assetValue > 100000 && confidence > 0.8) return 'high';
    if (assetValue > 50000 && confidence > 0.6) return 'high';
    if (assetValue > 25000 || confidence > 0.7) return 'medium';
    return 'low';
  }

  determinePrimaryContact(contactData, strategy) {
    if (contactData.addresses.length > 0) {
      return {
        method: 'mail',
        value: contactData.addresses[0].address,
        confidence: contactData.addresses[0].confidence
      };
    }
    
    if (contactData.phoneNumbers.length > 0) {
      return {
        method: 'phone',
        value: contactData.phoneNumbers[0].number,
        confidence: contactData.phoneNumbers[0].confidence
      };
    }
    
    if (contactData.socialContacts.length > 0) {
      return {
        method: 'linkedin',
        value: contactData.socialContacts[0].profileUrl,
        confidence: contactData.socialContacts[0].confidence
      };
    }
    
    return {
      method: 'unknown',
      value: 'No contact information found',
      confidence: 0
    };
  }

  compileAlternateContacts(contactData) {
    const alternates = [];
    
    contactData.phoneNumbers.forEach(phone => {
      alternates.push({
        method: 'phone',
        value: phone.number,
        confidence: phone.confidence
      });
    });
    
    contactData.emailAddresses.forEach(email => {
      alternates.push({
        method: 'email',
        value: email.email,
        confidence: email.confidence
      });
    });
    
    return alternates;
  }

  getBestContactTimes(messagingAngle) {
    const timingMap = {
      'business_asset_recovery': ['Tuesday 10-11 AM', 'Wednesday 2-4 PM', 'Thursday 10-11 AM'],
      'professional_asset_recovery': ['Tuesday 9-10 AM', 'Wednesday 3-5 PM', 'Thursday 9-10 AM'],
      'property_asset_recovery': ['Wednesday 10-12 PM', 'Thursday 10-12 PM', 'Saturday 10-12 PM'],
      'general_asset_recovery': ['Tuesday 10-12 PM', 'Wednesday 2-4 PM', 'Thursday 10-12 PM']
    };
    
    return timingMap[messagingAngle] || timingMap['general_asset_recovery'];
  }

  // Mock search methods
  async searchVoterRegistration(query, variations) {
    await this.delay(500);
    return { found: Math.random() > 0.3, data: { status: 'Active Voter' } };
  }

  async searchGovernmentRecords(query, variations) {
    await this.delay(400);
    return { found: Math.random() > 0.5, data: {} };
  }

  async searchSocialMediaProfiles(query, variations) {
    await this.delay(300);
    return { found: Math.random() > 0.6, data: {} };
  }

  async searchDirectoryServices(query, variations) {
    await this.delay(300);
    return { found: Math.random() > 0.5, data: {} };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Execute the enhanced asset recovery search test
async function runAssetRecoverySearchTest() {
  console.log('\n🎯 EXECUTING ENHANCED ASSET RECOVERY SEARCH TEST');
  console.log('================================================');

  const searchService = new AssetRecoveryAISearchTester();

  const searchQuery = {
    firstName: 'Tyjon',
    lastName: 'Hunter',
    state: 'CA',
    searchPurpose: 'asset_recovery',
    assetType: 'unknown'
  };

  console.log('📋 Search Parameters:');
  console.log(`   Name: ${searchQuery.firstName} ${searchQuery.lastName}`);
  console.log(`   Location: ${searchQuery.state}`);
  console.log(`   Purpose: ${searchQuery.searchPurpose}`);

  const startTime = Date.now();

  try {
    const searchResults = await searchService.searchForAssetRecovery(searchQuery);
    const duration = Date.now() - startTime;

    if (searchResults && searchResults.length > 0) {
      const result = searchResults[0];

      // Display comprehensive results
      console.log('\n📊 ENHANCED ASSET RECOVERY SEARCH RESULTS');
      console.log('==========================================');

      console.log(`\n🎯 SEARCH SUMMARY:`);
      console.log(`   Overall Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`   Contact Priority: ${result.contactPriority.toUpperCase()}`);
      console.log(`   Identity Confidence: ${(result.identityConfidence * 100).toFixed(1)}%`);
      console.log(`   Search Duration: ${duration}ms`);
      console.log(`   Search Cost: $${result.searchMetadata.searchCost}`);

      console.log(`\n👤 IDENTITY VERIFICATION:`);
      console.log(`   Full Name: ${result.fullName}`);
      console.log(`   Name Variations: ${result.nameVariations.length} variations checked`);
      console.log(`   Data Sources: ${result.searchMetadata.dataSourcesUsed.length} sources used`);

      console.log(`\n📞 CONTACT INFORMATION:`);
      console.log(`   Primary Contact: ${result.primaryContact.method} - ${result.primaryContact.value}`);
      console.log(`   Primary Confidence: ${(result.primaryContact.confidence * 100).toFixed(1)}%`);
      console.log(`   Alternate Contacts: ${result.alternateContacts.length} additional methods`);

      console.log(`\n💰 DISCOVERED ASSETS:`);
      console.log(`   Real Estate Properties: ${result.discoveredAssets.realEstate.length}`);
      console.log(`   Business Interests: ${result.discoveredAssets.businessInterests.length}`);
      console.log(`   Financial Assets: ${result.discoveredAssets.financialAssets.length}`);
      console.log(`   Total Estimated Value: $${result.discoveredAssets.estimatedTotalValue.toLocaleString()}`);

      // Detailed asset breakdown
      if (result.discoveredAssets.realEstate.length > 0) {
        console.log(`\n🏠 REAL ESTATE DETAILS:`);
        result.discoveredAssets.realEstate.forEach((property, index) => {
          console.log(`   ${index + 1}. ${property.address}, ${property.city}, ${property.state}`);
          console.log(`      Estimated Value: $${property.estimatedValue.toLocaleString()}`);
          console.log(`      Equity Estimate: $${property.equityEstimate.toLocaleString()}`);
          console.log(`      Ownership: ${property.ownershipType}`);
          console.log(`      Purchase Date: ${property.purchaseDate}`);
        });
      }

      if (result.discoveredAssets.businessInterests.length > 0) {
        console.log(`\n🏢 BUSINESS INTERESTS:`);
        result.discoveredAssets.businessInterests.forEach((business, index) => {
          console.log(`   ${index + 1}. ${business.businessName}`);
          console.log(`      Entity Type: ${business.entityType}`);
          console.log(`      Status: ${business.status}`);
          console.log(`      Ownership: ${business.ownershipPercentage}%`);
          console.log(`      Estimated Value: $${business.estimatedValue?.toLocaleString() || 'Unknown'}`);
          console.log(`      Address: ${business.address}`);
        });
      }

      console.log(`\n📋 CONTACT STRATEGY:`);
      console.log(`   Recommended Method: ${result.contactStrategy.recommendedMethod}`);
      console.log(`   Messaging Angle: ${result.contactStrategy.messagingAngle}`);
      console.log(`   Success Probability: ${(result.contactStrategy.successProbability * 100).toFixed(1)}%`);
      console.log(`   Personalizations: ${result.contactStrategy.personalizations.join(', ')}`);
      console.log(`   Best Contact Times: ${result.contactStrategy.bestContactTimes.join(', ')}`);

      // Generate actionable intelligence for agents
      console.log(`\n🎯 ACTIONABLE INTELLIGENCE FOR AGENTS:`);
      console.log('=====================================');

      if (result.contactPriority === 'high') {
        console.log(`🔥 HIGH PRIORITY TARGET: Immediate contact recommended`);
      } else if (result.contactPriority === 'medium') {
        console.log(`⚠️ MEDIUM PRIORITY TARGET: Contact within 48 hours`);
      } else {
        console.log(`ℹ️ LOW PRIORITY TARGET: Contact when capacity allows`);
      }

      if (result.discoveredAssets.estimatedTotalValue > 100000) {
        console.log(`💰 HIGH-VALUE TARGET: $${result.discoveredAssets.estimatedTotalValue.toLocaleString()} in assets`);
        console.log(`   💡 Potential commission: $${(result.discoveredAssets.estimatedTotalValue * 0.3).toLocaleString()} (30% rate)`);
      }

      if (result.discoveredAssets.realEstate.length > 0) {
        console.log(`🏠 PROPERTY OWNER APPROACH:`);
        console.log(`   📝 Message: "We've identified unclaimed assets related to your property ownership"`);
        console.log(`   🎯 Angle: Property-focused asset recovery services`);
      }

      if (result.discoveredAssets.businessInterests.length > 0) {
        console.log(`🏢 BUSINESS OWNER APPROACH:`);
        console.log(`   📝 Message: "Business asset recovery opportunity for ${result.discoveredAssets.businessInterests[0].businessName}"`);
        console.log(`   🎯 Angle: Business-focused asset recovery with tax benefits`);
      }

      if (result.primaryContact.method === 'mail') {
        console.log(`📮 DIRECT MAIL STRATEGY:`);
        console.log(`   📍 Send to: ${result.primaryContact.value}`);
        console.log(`   📄 Use professional letterhead with asset recovery branding`);
        console.log(`   ⏰ Follow up with phone call in 5-7 business days`);
      }

      if (result.primaryContact.method === 'linkedin') {
        console.log(`💼 LINKEDIN STRATEGY:`);
        console.log(`   🔗 Profile: ${result.primaryContact.value}`);
        console.log(`   📝 Professional message about business asset recovery`);
        console.log(`   🎯 Emphasize business benefits and tax advantages`);
      }

      // Risk assessment and recommendations
      console.log(`\n⚠️ RISK ASSESSMENT & RECOMMENDATIONS:`);
      console.log('====================================');

      if (result.confidence > 0.8) {
        console.log(`✅ HIGH CONFIDENCE MATCH: Proceed with immediate contact`);
        console.log(`   📞 Contact success probability: ${(result.contactStrategy.successProbability * 100).toFixed(1)}%`);
        console.log(`   💼 Recommended agent: Senior agent for high-value target`);
      } else if (result.confidence > 0.6) {
        console.log(`⚠️ MEDIUM CONFIDENCE MATCH: Verify one additional data point before contact`);
        console.log(`   🔍 Suggested verification: Cross-check address with voter registration`);
      } else {
        console.log(`❌ LOW CONFIDENCE MATCH: Additional verification required`);
        console.log(`   🔍 Recommended: Manual verification before proceeding`);
      }

      // ROI calculation
      const estimatedCommission = result.discoveredAssets.estimatedTotalValue * 0.3; // 30% commission
      const searchCost = result.searchMetadata.searchCost;
      const roi = ((estimatedCommission - searchCost) / searchCost * 100).toFixed(0);

      console.log(`\n💰 ROI ANALYSIS:`);
      console.log('================');
      console.log(`   Asset Value: $${result.discoveredAssets.estimatedTotalValue.toLocaleString()}`);
      console.log(`   Potential Commission (30%): $${estimatedCommission.toLocaleString()}`);
      console.log(`   Search Cost: $${searchCost}`);
      console.log(`   ROI: ${roi}x return on investment`);

      if (estimatedCommission > 10000) {
        console.log(`   🎯 RECOMMENDATION: High-priority target - assign best agent`);
      } else if (estimatedCommission > 5000) {
        console.log(`   🎯 RECOMMENDATION: Medium-priority target - standard process`);
      } else {
        console.log(`   🎯 RECOMMENDATION: Low-priority target - batch processing`);
      }

      console.log(`\n📈 NEXT STEPS:`);
      console.log('==============');
      console.log(`1. Assign to ${result.contactPriority} priority queue`);
      console.log(`2. Contact via ${result.contactStrategy.recommendedMethod} during ${result.contactStrategy.bestContactTimes[0]}`);
      console.log(`3. Use ${result.contactStrategy.messagingAngle} messaging approach`);
      console.log(`4. Mention specific assets: ${result.discoveredAssets.realEstate.length > 0 ? 'property ownership' : 'business interests'}`);
      console.log(`5. Follow up within 48 hours if no response`);

      return result;

    } else {
      console.log('❌ No results found');
      return null;
    }

  } catch (error) {
    console.error('❌ Search failed:', error);
    return null;
  }
}

// Run the enhanced asset recovery search test
runAssetRecoverySearchTest().catch(console.error);
