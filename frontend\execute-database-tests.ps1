#!/usr/bin/env pwsh

Write-Host "===================================================================" -ForegroundColor Cyan
Write-Host "ASSETHUNTERPRO DATABASE OPTIMIZATION TESTING SUITE" -ForegroundColor Cyan
Write-Host "===================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will run comprehensive database tests including:" -ForegroundColor Yellow
Write-Host "- Connection and health checks" -ForegroundColor White
Write-Host "- Performance optimization analysis" -ForegroundColor White  
Write-Host "- Data integrity validation" -ForegroundColor White
Write-Host "- Load/stress testing" -ForegroundColor White
Write-Host "- Security assessment" -ForegroundColor White
Write-Host ""
Write-Host "Make sure you have Node.js installed and are in the frontend directory" -ForegroundColor Yellow
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js detected: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "❌ package.json not found. Please run this script from the frontend directory." -ForegroundColor Red
    exit 1
}

Write-Host ""
$continue = Read-Host "Press Enter to continue or Ctrl+C to cancel"

Write-Host ""
Write-Host "🚀 Starting database tests..." -ForegroundColor Green
Write-Host ""

try {
    # Run the comprehensive database test suite
    node run-database-tests.js
    
    Write-Host ""
    Write-Host "===================================================================" -ForegroundColor Cyan
    Write-Host "DATABASE TESTING COMPLETE" -ForegroundColor Cyan
    Write-Host "===================================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Check the generated reports for detailed results:" -ForegroundColor Yellow
    Write-Host "- JSON report: database-comprehensive-report-*.json" -ForegroundColor White
    Write-Host "- HTML report: database-comprehensive-report-*.html" -ForegroundColor White
    Write-Host ""
    Write-Host "Also run the SQL health check manually in Supabase:" -ForegroundColor Yellow
    Write-Host "- File: database/comprehensive-optimization-check.sql" -ForegroundColor White
    Write-Host ""
    
    # List generated report files
    $reportFiles = Get-ChildItem -Name "database-*-report-*.json", "database-*-report-*.html" | Sort-Object
    if ($reportFiles.Count -gt 0) {
        Write-Host "📄 Generated report files:" -ForegroundColor Green
        foreach ($file in $reportFiles) {
            Write-Host "   - $file" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ Error running database tests: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure you're in the frontend directory" -ForegroundColor White
    Write-Host "2. Run 'npm install' to install dependencies" -ForegroundColor White
    Write-Host "3. Check your .env file has correct Supabase credentials" -ForegroundColor White
    Write-Host "4. Verify your internet connection" -ForegroundColor White
    exit 1
}

Write-Host ""
Read-Host "Press Enter to exit"
