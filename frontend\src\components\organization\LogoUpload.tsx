import React, { useState, useRef } from 'react'
import { Upload, X, Image, AlertCircle, Check } from 'lucide-react'
import { organizationService } from '../../services/organizationService'
import type { Organization } from '../../types/database'

interface LogoUploadProps {
  organization: Organization
  onLogoUpdated: (logoUrl: string | null) => void
  canManage: boolean
}

export const LogoUpload: React.FC<LogoUploadProps> = ({
  organization,
  onLogoUpdated,
  canManage
}) => {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setError(null)
    setSuccess(null)
    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 100)

      const result = await organizationService.uploadLogo(organization.id, file)

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (result.success && result.logo_url) {
        setSuccess('Logo uploaded successfully!')
        onLogoUpdated(result.logo_url)
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(result.error || 'Failed to upload logo')
      }
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('Logo upload error:', error)
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveLogo = async () => {
    if (!confirm('Are you sure you want to remove the current logo?')) return

    setError(null)
    setSuccess(null)
    setIsUploading(true)

    try {
      const result = await organizationService.removeLogo(organization.id)
      
      if (result.success) {
        setSuccess('Logo removed successfully!')
        onLogoUpdated(null)
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(result.error || 'Failed to remove logo')
      }
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('Logo removal error:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Company Logo
        </h3>
        {canManage && (
          <div className="flex space-x-2">
            <button
              onClick={triggerFileSelect}
              disabled={isUploading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Upload className="h-4 w-4 mr-2" />
              {organization.logo_url ? 'Change Logo' : 'Upload Logo'}
            </button>
            
            {organization.logo_url && (
              <button
                onClick={handleRemoveLogo}
                disabled={isUploading}
                className="inline-flex items-center px-3 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-200 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <X className="h-4 w-4 mr-2" />
                Remove
              </button>
            )}
          </div>
        )}
      </div>

      {/* Logo Preview */}
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          <div className="w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-gray-800">
            {organization.logo_url ? (
              <img
                src={organization.logo_url}
                alt={`${organization.name} logo`}
                className="w-full h-full object-contain rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  target.nextElementSibling?.classList.remove('hidden')
                }}
              />
            ) : (
              <Image className="h-8 w-8 text-gray-400" />
            )}
            {organization.logo_url && (
              <div className="hidden">
                <Image className="h-8 w-8 text-gray-400" />
              </div>
            )}
          </div>
        </div>
        
        <div className="flex-1">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {organization.logo_url 
              ? 'Current logo is displayed above' 
              : 'No logo uploaded yet'
            }
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
            Supported formats: JPEG, PNG, GIF, WebP, SVG (max 5MB)
          </p>
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              {uploadProgress < 100 ? 'Uploading...' : 'Processing...'}
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {uploadProgress}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="flex items-center space-x-2 text-green-600 dark:text-green-400 text-sm">
          <Check className="h-4 w-4" />
          <span>{success}</span>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml"
        onChange={handleFileSelect}
        className="hidden"
        disabled={isUploading || !canManage}
      />
    </div>
  )
}
