/**
 * PUBLIC RECORDS INTEGRATION SERVICE
 * Handles state-specific public records parsing and address verification
 * 
 * Designed to handle real unclaimed property data with proper address matching
 * for accurate person identification in asset recovery
 */

export interface PublicRecordResult {
  recordId: string;
  recordType: 'unclaimed_property' | 'property_tax' | 'voter_registration' | 'business_filing';
  state: string;
  confidence: number;
  
  // Person Information
  ownerName: string;
  nameVariations: string[];
  
  // Address Information (Critical for matching)
  currentAddress: {
    street1: string;
    street2?: string;
    street3?: string;
    city: string;
    state: string;
    zip: string;
    countryCode?: string;
    addressType: 'current' | 'last_known' | 'mailing';
    confidence: number;
  };
  
  // Asset Information
  assetDetails: {
    cashBalance?: number;
    numberOfClaims?: number;
    paidClaims?: number;
    holderName?: string;
    holderAddress?: {
      street1: string;
      street2?: string;
      street3?: string;
      city: string;
      state: string;
      zip: string;
    };
    assetType?: string;
    reportDate?: string;
  };
  
  // Matching Metadata
  matchingFactors: {
    nameMatch: number; // 0-1 confidence
    addressMatch: number; // 0-1 confidence
    locationMatch: number; // 0-1 confidence
    overallMatch: number; // 0-1 confidence
  };
}

export interface StateRecordFormat {
  state: string;
  headers: string[];
  fieldMapping: {
    ownerName: string[];
    ownerStreet1: string;
    ownerStreet2?: string;
    ownerStreet3?: string;
    ownerCity: string;
    ownerState: string;
    ownerZip: string;
    ownerCountry?: string;
    cashBalance?: string;
    pendingClaims?: string;
    paidClaims?: string;
    holderName?: string;
    holderStreet1?: string;
    holderStreet2?: string;
    holderStreet3?: string;
    holderCity?: string;
    holderState?: string;
    holderZip?: string;
  };
}

export class PublicRecordsIntegrationService {
  private stateFormats: Map<string, StateRecordFormat> = new Map();
  
  constructor() {
    this.initializeStateFormats();
  }

  /**
   * Initialize state-specific record formats
   */
  private initializeStateFormats() {
    // California Format (from your example)
    this.stateFormats.set('CA', {
      state: 'CA',
      headers: [
        'OWNER_STREET_1', 'OWNER_STREET_2', 'OWNER_STREET_3', 'OWNER_CITY', 
        'OWNER_STATE', 'OWNER_ZIP', 'OWNER_COUNTRY_CODE', 'CURRENT_CASH_BALANCE',
        'NUMBER_OF_PENDING_CLAIMS', 'NUMBER_OF_PAID_CLAIMS', 'HOLDER_NAME',
        'HOLDER_STREET_1', 'HOLDER_STREET_2', 'HOLDER_STREET_3', 'HOLDER_CITY',
        'HOLDER_STATE', 'HOLDER_ZIP'
      ],
      fieldMapping: {
        ownerName: ['OWNER_NAME', 'PROPERTY_OWNER', 'CLAIMANT_NAME'], // Multiple possible fields
        ownerStreet1: 'OWNER_STREET_1',
        ownerStreet2: 'OWNER_STREET_2',
        ownerStreet3: 'OWNER_STREET_3',
        ownerCity: 'OWNER_CITY',
        ownerState: 'OWNER_STATE',
        ownerZip: 'OWNER_ZIP',
        ownerCountry: 'OWNER_COUNTRY_CODE',
        cashBalance: 'CURRENT_CASH_BALANCE',
        pendingClaims: 'NUMBER_OF_PENDING_CLAIMS',
        paidClaims: 'NUMBER_OF_PAID_CLAIMS',
        holderName: 'HOLDER_NAME',
        holderStreet1: 'HOLDER_STREET_1',
        holderStreet2: 'HOLDER_STREET_2',
        holderStreet3: 'HOLDER_STREET_3',
        holderCity: 'HOLDER_CITY',
        holderState: 'HOLDER_STATE',
        holderZip: 'HOLDER_ZIP'
      }
    });

    // Texas Format (different structure)
    this.stateFormats.set('TX', {
      state: 'TX',
      headers: [
        'OWNER_LAST_NAME', 'OWNER_FIRST_NAME', 'OWNER_ADDRESS_LINE_1',
        'OWNER_ADDRESS_LINE_2', 'OWNER_CITY', 'OWNER_STATE', 'OWNER_ZIP_CODE',
        'PROPERTY_VALUE', 'HOLDER_COMPANY_NAME', 'HOLDER_ADDRESS'
      ],
      fieldMapping: {
        ownerName: ['OWNER_LAST_NAME', 'OWNER_FIRST_NAME'],
        ownerStreet1: 'OWNER_ADDRESS_LINE_1',
        ownerStreet2: 'OWNER_ADDRESS_LINE_2',
        ownerCity: 'OWNER_CITY',
        ownerState: 'OWNER_STATE',
        ownerZip: 'OWNER_ZIP_CODE',
        cashBalance: 'PROPERTY_VALUE',
        holderName: 'HOLDER_COMPANY_NAME'
      }
    });

    // New York Format (another variation)
    this.stateFormats.set('NY', {
      state: 'NY',
      headers: [
        'CLAIMANT_NAME', 'LAST_KNOWN_ADDRESS', 'CITY', 'STATE', 'ZIP',
        'AMOUNT', 'HOLDER_NAME', 'REPORT_YEAR'
      ],
      fieldMapping: {
        ownerName: ['CLAIMANT_NAME'],
        ownerStreet1: 'LAST_KNOWN_ADDRESS',
        ownerCity: 'CITY',
        ownerState: 'STATE',
        ownerZip: 'ZIP',
        cashBalance: 'AMOUNT',
        holderName: 'HOLDER_NAME'
      }
    });
  }

  /**
   * Search public records across multiple states with address verification
   */
  async searchPublicRecords(
    firstName: string,
    lastName: string,
    knownAddresses: string[] = [],
    targetStates: string[] = ['CA', 'TX', 'NY', 'FL', 'IL']
  ): Promise<PublicRecordResult[]> {
    console.log(`🔍 Searching public records for ${firstName} ${lastName}`);
    console.log(`📍 Target states: ${targetStates.join(', ')}`);
    console.log(`🏠 Known addresses for verification: ${knownAddresses.length}`);

    const allResults: PublicRecordResult[] = [];

    for (const state of targetStates) {
      console.log(`\n📋 Searching ${state} public records...`);
      
      try {
        const stateResults = await this.searchStateRecords(
          firstName,
          lastName,
          state,
          knownAddresses
        );
        
        if (stateResults.length > 0) {
          console.log(`   ✅ Found ${stateResults.length} potential matches in ${state}`);
          allResults.push(...stateResults);
        } else {
          console.log(`   ℹ️ No matches found in ${state}`);
        }
      } catch (error) {
        console.log(`   ❌ Error searching ${state}: ${error}`);
      }
    }

    // Sort by confidence and return best matches
    const sortedResults = allResults.sort((a, b) => b.confidence - a.confidence);
    
    console.log(`\n📊 Public Records Search Summary:`);
    console.log(`   Total Records Found: ${sortedResults.length}`);
    console.log(`   High Confidence (>80%): ${sortedResults.filter(r => r.confidence > 0.8).length}`);
    console.log(`   Medium Confidence (60-80%): ${sortedResults.filter(r => r.confidence >= 0.6 && r.confidence <= 0.8).length}`);

    return sortedResults;
  }

  /**
   * Search records for a specific state
   */
  private async searchStateRecords(
    firstName: string,
    lastName: string,
    state: string,
    knownAddresses: string[]
  ): Promise<PublicRecordResult[]> {
    const stateFormat = this.stateFormats.get(state);
    if (!stateFormat) {
      console.log(`   ⚠️ No format defined for state: ${state}`);
      return [];
    }

    // Simulate real public records search
    await this.delay(500);

    // Generate realistic test data based on your California example
    const mockRecords = this.generateMockRecords(firstName, lastName, state, stateFormat);
    
    const results: PublicRecordResult[] = [];

    for (const record of mockRecords) {
      const parsedRecord = this.parseStateRecord(record, stateFormat);
      if (parsedRecord) {
        // Perform address verification
        const addressMatch = this.verifyAddressMatch(parsedRecord, knownAddresses);
        parsedRecord.matchingFactors.addressMatch = addressMatch;
        
        // Calculate overall confidence
        parsedRecord.confidence = this.calculateOverallConfidence(parsedRecord);
        
        // Only include records with reasonable confidence
        if (parsedRecord.confidence > 0.4) {
          results.push(parsedRecord);
        }
      }
    }

    return results;
  }

  /**
   * Parse a state record according to state-specific format
   */
  private parseStateRecord(record: any, format: StateRecordFormat): PublicRecordResult | null {
    try {
      // Extract owner name (handle multiple possible fields)
      let ownerName = '';
      for (const nameField of format.fieldMapping.ownerName) {
        if (record[nameField]) {
          if (nameField.includes('LAST') && nameField.includes('FIRST')) {
            // Handle separate first/last name fields
            ownerName = `${record[format.fieldMapping.ownerName.find(f => f.includes('FIRST')) || '']} ${record[nameField]}`.trim();
          } else {
            ownerName = record[nameField];
          }
          break;
        }
      }

      if (!ownerName) {
        return null;
      }

      // Build address
      const address = {
        street1: record[format.fieldMapping.ownerStreet1] || '',
        street2: record[format.fieldMapping.ownerStreet2] || '',
        street3: record[format.fieldMapping.ownerStreet3] || '',
        city: record[format.fieldMapping.ownerCity] || '',
        state: record[format.fieldMapping.ownerState] || format.state,
        zip: record[format.fieldMapping.ownerZip] || '',
        countryCode: record[format.fieldMapping.ownerCountry] || 'US',
        addressType: 'last_known' as const,
        confidence: 0.8
      };

      // Extract asset details
      const assetDetails = {
        cashBalance: parseFloat(record[format.fieldMapping.cashBalance || ''] || '0'),
        numberOfClaims: parseInt(record[format.fieldMapping.pendingClaims || ''] || '0'),
        paidClaims: parseInt(record[format.fieldMapping.paidClaims || ''] || '0'),
        holderName: record[format.fieldMapping.holderName || ''] || '',
        holderAddress: format.fieldMapping.holderStreet1 ? {
          street1: record[format.fieldMapping.holderStreet1] || '',
          street2: record[format.fieldMapping.holderStreet2 || ''] || '',
          street3: record[format.fieldMapping.holderStreet3 || ''] || '',
          city: record[format.fieldMapping.holderCity || ''] || '',
          state: record[format.fieldMapping.holderState || ''] || '',
          zip: record[format.fieldMapping.holderZip || ''] || ''
        } : undefined,
        assetType: 'unclaimed_property',
        reportDate: new Date().toISOString().split('T')[0]
      };

      // Calculate name matching confidence
      const nameMatch = this.calculateNameMatch(ownerName, record.searchFirstName, record.searchLastName);

      const result: PublicRecordResult = {
        recordId: `${format.state}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        recordType: 'unclaimed_property',
        state: format.state,
        confidence: 0, // Will be calculated later
        ownerName,
        nameVariations: this.generateNameVariations(ownerName),
        currentAddress: address,
        assetDetails,
        matchingFactors: {
          nameMatch,
          addressMatch: 0, // Will be calculated later
          locationMatch: this.calculateLocationMatch(address.city, address.state),
          overallMatch: 0 // Will be calculated later
        }
      };

      return result;

    } catch (error) {
      console.error('Error parsing state record:', error);
      return null;
    }
  }

  /**
   * Generate mock records for testing (replace with real API calls)
   */
  private generateMockRecords(firstName: string, lastName: string, state: string, format: StateRecordFormat): any[] {
    const records = [];

    // Generate 1-3 potential matches per state
    const numRecords = Math.floor(Math.random() * 3) + 1;

    for (let i = 0; i < numRecords; i++) {
      const record: any = {
        searchFirstName: firstName,
        searchLastName: lastName
      };

      // California format example
      if (state === 'CA') {
        record['OWNER_STREET_1'] = `${Math.floor(Math.random() * 9999)} ${['Main St', 'Oak Ave', 'Pine Rd', 'Hunter St'][Math.floor(Math.random() * 4)]}`;
        record['OWNER_STREET_2'] = '';
        record['OWNER_STREET_3'] = '';
        record['OWNER_CITY'] = ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento'][Math.floor(Math.random() * 4)];
        record['OWNER_STATE'] = 'CA';
        record['OWNER_ZIP'] = `9${Math.floor(Math.random() * 9)}${Math.floor(Math.random() * 999).toString().padStart(3, '0')}`;
        record['OWNER_COUNTRY_CODE'] = 'US';
        record['CURRENT_CASH_BALANCE'] = Math.floor(Math.random() * 50000) + 1000;
        record['NUMBER_OF_PENDING_CLAIMS'] = Math.floor(Math.random() * 3);
        record['NUMBER_OF_PAID_CLAIMS'] = Math.floor(Math.random() * 2);
        record['HOLDER_NAME'] = ['WILTON REASSURANCE COMPANY', 'STATE FARM', 'WELLS FARGO BANK', 'CHASE BANK'][Math.floor(Math.random() * 4)];
        record['HOLDER_STREET_1'] = `${Math.floor(Math.random() * 9999)} Business Blvd`;
        record['HOLDER_CITY'] = 'Los Angeles';
        record['HOLDER_STATE'] = 'CA';
        record['HOLDER_ZIP'] = '90210';

        // Add owner name field (simulate different name formats)
        const nameVariations = [
          `${firstName} ${lastName}`,
          `${lastName}, ${firstName}`,
          `${firstName.charAt(0)} ${lastName}`,
          `${firstName} ${lastName.charAt(0)}`
        ];
        record['OWNER_NAME'] = nameVariations[Math.floor(Math.random() * nameVariations.length)];
      }

      records.push(record);
    }

    return records;
  }

  /**
   * Verify address match against known addresses
   */
  private verifyAddressMatch(record: PublicRecordResult, knownAddresses: string[]): number {
    if (knownAddresses.length === 0) {
      return 0.5; // Neutral if no known addresses
    }

    const recordAddress = `${record.currentAddress.street1} ${record.currentAddress.city} ${record.currentAddress.state}`.toLowerCase();
    
    let bestMatch = 0;
    for (const knownAddr of knownAddresses) {
      const similarity = this.calculateAddressSimilarity(recordAddress, knownAddr.toLowerCase());
      bestMatch = Math.max(bestMatch, similarity);
    }

    return bestMatch;
  }

  /**
   * Calculate address similarity using fuzzy matching
   */
  private calculateAddressSimilarity(addr1: string, addr2: string): number {
    // Simple similarity calculation (can be enhanced with more sophisticated algorithms)
    const words1 = addr1.split(/\s+/);
    const words2 = addr2.split(/\s+/);
    
    let matches = 0;
    for (const word1 of words1) {
      for (const word2 of words2) {
        if (word1 === word2 || this.isStreetNumberMatch(word1, word2)) {
          matches++;
          break;
        }
      }
    }

    return matches / Math.max(words1.length, words2.length);
  }

  /**
   * Check if two strings represent the same street number
   */
  private isStreetNumberMatch(str1: string, str2: string): boolean {
    const num1 = parseInt(str1);
    const num2 = parseInt(str2);
    return !isNaN(num1) && !isNaN(num2) && num1 === num2;
  }

  /**
   * Calculate name matching confidence
   */
  private calculateNameMatch(recordName: string, searchFirst: string, searchLast: string): number {
    const fullSearchName = `${searchFirst} ${searchLast}`.toLowerCase();
    const recordNameLower = recordName.toLowerCase();

    // Exact match
    if (recordNameLower === fullSearchName) {
      return 1.0;
    }

    // Check if both first and last names are present
    const hasFirst = recordNameLower.includes(searchFirst.toLowerCase());
    const hasLast = recordNameLower.includes(searchLast.toLowerCase());

    if (hasFirst && hasLast) {
      return 0.9;
    } else if (hasFirst || hasLast) {
      return 0.6;
    }

    // Check for initials or partial matches
    const firstInitial = searchFirst.charAt(0).toLowerCase();
    if (recordNameLower.includes(firstInitial) && hasLast) {
      return 0.7;
    }

    return 0.3;
  }

  /**
   * Calculate location matching confidence
   */
  private calculateLocationMatch(city: string, state: string): number {
    // Higher confidence for major cities where person is more likely to be found
    const majorCities = ['los angeles', 'new york', 'chicago', 'houston', 'phoenix'];
    const cityLower = city.toLowerCase();

    if (majorCities.includes(cityLower)) {
      return 0.8;
    } else if (city && state) {
      return 0.6;
    } else {
      return 0.3;
    }
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(record: PublicRecordResult): number {
    const weights = {
      nameMatch: 0.4,
      addressMatch: 0.3,
      locationMatch: 0.2,
      assetValue: 0.1
    };

    let confidence = 
      record.matchingFactors.nameMatch * weights.nameMatch +
      record.matchingFactors.addressMatch * weights.addressMatch +
      record.matchingFactors.locationMatch * weights.locationMatch;

    // Boost confidence for higher asset values
    if (record.assetDetails.cashBalance && record.assetDetails.cashBalance > 10000) {
      confidence += 0.1;
    }

    // Boost confidence for verified addresses
    if (record.currentAddress.confidence > 0.8) {
      confidence += 0.05;
    }

    return Math.min(0.95, confidence);
  }

  /**
   * Generate name variations for better matching
   */
  private generateNameVariations(name: string): string[] {
    const variations = new Set<string>();
    variations.add(name);

    // Add common variations
    const parts = name.split(/\s+/);
    if (parts.length >= 2) {
      variations.add(`${parts[1]}, ${parts[0]}`); // Last, First
      variations.add(`${parts[0].charAt(0)} ${parts[1]}`); // F Last
      variations.add(`${parts[0]} ${parts[1].charAt(0)}`); // First L
    }

    return Array.from(variations);
  }

  /**
   * Add new state format
   */
  addStateFormat(stateFormat: StateRecordFormat): void {
    this.stateFormats.set(stateFormat.state, stateFormat);
    console.log(`✅ Added format for state: ${stateFormat.state}`);
  }

  /**
   * Get supported states
   */
  getSupportedStates(): string[] {
    return Array.from(this.stateFormats.keys());
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const publicRecordsIntegration = new PublicRecordsIntegrationService();
