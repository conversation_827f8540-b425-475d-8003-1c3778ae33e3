-- Migration script to add organizations table and update existing structure
-- Run this script to add organization support to existing AssetHunterPro installations

-- Step 1: Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE,
    industry VARCHAR(100),
    size VARCHAR(20) CHECK (size IN ('small', 'medium', 'large', 'enterprise')),
    established_date DATE,
    
    -- Address information
    street_address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    
    -- Contact information
    primary_email VARCHAR(255),
    primary_phone VARCHAR(20),
    website VARCHAR(255),
    
    -- Branding configuration
    logo_url VARCHAR(500),
    logo_file_name VARCHAR(255),
    primary_color VARCHAR(7) DEFAULT '#3B82F6', -- Blue
    secondary_color VARCHAR(7) DEFAULT '#1E40AF', -- Dark blue
    accent_color VARCHAR(7) DEFAULT '#10B981', -- Green
    
    -- Settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    currency VARCHAR(3) DEFAULT 'USD',
    language VARCHAR(5) DEFAULT 'en',
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 2: Insert default organization for existing installations
INSERT INTO organizations (
    id,
    name,
    subdomain,
    industry,
    size,
    primary_email,
    logo_url,
    is_active
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'AssetHunterPro Default',
    'default',
    'Asset Recovery',
    'medium',
    '<EMAIL>',
    NULL,
    true
) ON CONFLICT (id) DO NOTHING;

-- Step 3: Add organization_id column to users table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'organization_id') THEN
        ALTER TABLE users ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Step 4: Update existing users to belong to the default organization
UPDATE users 
SET organization_id = '00000000-0000-0000-0000-000000000001'
WHERE organization_id IS NULL;

-- Step 5: Make organization_id NOT NULL after migration
ALTER TABLE users ALTER COLUMN organization_id SET NOT NULL;

-- Step 6: Update unique constraint for employee_id to be per organization
DO $$
BEGIN
    -- Drop existing unique constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'users_employee_id_key' AND table_name = 'users') THEN
        ALTER TABLE users DROP CONSTRAINT users_employee_id_key;
    END IF;
    
    -- Add new unique constraint for employee_id within organization
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'users_org_employee_id_unique' AND table_name = 'users') THEN
        ALTER TABLE users ADD CONSTRAINT users_org_employee_id_unique 
        UNIQUE(organization_id, employee_id);
    END IF;
END $$;

-- Step 7: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX IF NOT EXISTS idx_organizations_active ON organizations(is_active);
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);

-- Step 8: Create storage bucket for company logos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'company-logos', 
  'company-logos', 
  true, 
  5242880, -- 5MB limit
  ARRAY[
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Step 9: Create storage policies for logo access
-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Organization members can upload logos" ON storage.objects;
DROP POLICY IF EXISTS "Organization members can view logos" ON storage.objects;
DROP POLICY IF EXISTS "Public can view logos" ON storage.objects;
DROP POLICY IF EXISTS "Organization admins can manage logos" ON storage.objects;
DROP POLICY IF EXISTS "Organization admins can delete logos" ON storage.objects;

-- Create new policies
CREATE POLICY "Organization members can upload logos" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role IN ('admin', 'senior_agent')
    )
  );

CREATE POLICY "Organization members can view logos" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
    )
  );

CREATE POLICY "Public can view logos" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'company-logos');

CREATE POLICY "Organization admins can manage logos" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role = 'admin'
    )
  );

CREATE POLICY "Organization admins can delete logos" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role = 'admin'
    )
  );

-- Step 10: Create a function to get organization logo URL
CREATE OR REPLACE FUNCTION get_organization_logo(org_id UUID)
RETURNS TEXT AS $$
DECLARE
    logo_url TEXT;
BEGIN
    SELECT o.logo_url INTO logo_url
    FROM organizations o
    WHERE o.id = org_id AND o.is_active = true;
    
    RETURN logo_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_organization_logo(UUID) TO authenticated;

-- Migration completed successfully
SELECT 'Organizations table and logo upload functionality added successfully!' as migration_status;
