-- Setup storage bucket for company logos
-- This script creates a secure storage bucket for organization logos

-- Create the company-logos bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'company-logos', 
  'company-logos', 
  true, 
  5242880, -- 5MB limit
  ARRAY[
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to upload logos for their organization
CREATE POLICY "Organization members can upload logos" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role IN ('admin', 'senior_agent')
    )
  );

-- Policy: Allow authenticated users to view logos for their organization
CREATE POLICY "Organization members can view logos" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
    )
  );

-- Policy: Allow public read access to logos (for branding display)
CREATE POLICY "Public can view logos" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'company-logos');

-- Policy: Allow organization admins to update/delete logos
CREATE POLICY "Organization admins can manage logos" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role = 'admin'
    )
  );

CREATE POLICY "Organization admins can delete logos" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'company-logos' AND
    auth.uid() IN (
      SELECT u.id 
      FROM users u 
      WHERE u.organization_id::text = (storage.foldername(name))[1]
      AND u.role = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX IF NOT EXISTS idx_organizations_active ON organizations(is_active);
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Insert a default organization for existing users
INSERT INTO organizations (
  id,
  name,
  subdomain,
  industry,
  size,
  primary_email,
  logo_url,
  is_active
) VALUES (
  '00000000-0000-0000-0000-000000000001',
  'AssetHunterPro Default',
  'default',
  'Asset Recovery',
  'medium',
  '<EMAIL>',
  NULL,
  true
) ON CONFLICT (id) DO NOTHING;

-- Update existing users to belong to the default organization
-- This is a migration step for existing installations
UPDATE users 
SET organization_id = '00000000-0000-0000-0000-000000000001'
WHERE organization_id IS NULL;
