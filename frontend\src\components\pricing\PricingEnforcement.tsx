import React, { useState, useEffect } from 'react'
import { 
  Lock, 
  Crown, 
  Zap, 
  AlertTriangle, 
  TrendingUp, 
  Users, 
  HardDrive,
  Activity,
  ArrowRight,
  X
} from 'lucide-react'
import { usePermissions, usePlanFeatures, useUsageLimit } from '../../hooks/usePermissions'

interface PricingEnforcementProps {
  feature?: string
  permission?: string
  usageType?: 'users' | 'claims' | 'storage' | 'api_calls'
  children: React.ReactNode
  fallback?: React.ReactNode
  showUpgrade?: boolean
}

export const PricingEnforcement: React.FC<PricingEnforcementProps> = ({
  feature,
  permission,
  usageType,
  children,
  fallback,
  showUpgrade = true
}) => {
  const { hasFeature, planName, subscription } = usePlanFeatures()
  const { hasPermission, lastCheck } = usePermissions()
  const { usage } = useUsageLimit(usageType || 'claims')
  const [isAllowed, setIsAllowed] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkAccess()
  }, [feature, permission, usageType])

  const checkAccess = async () => {
    setIsLoading(true)

    try {
      let allowed = true

      // Check feature access
      if (feature && !hasFeature(feature)) {
        allowed = false
      }

      // Check permission
      if (permission && allowed) {
        allowed = await hasPermission(permission)
      }

      // Check usage limits
      if (usageType && usage && allowed) {
        allowed = !usage.isAtLimit
      }

      setIsAllowed(allowed)
    } catch (error) {
      console.error('Access check error:', error)
      setIsAllowed(false)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-8 w-32"></div>
    )
  }

  if (isAllowed) {
    return <>{children}</>
  }

  if (fallback) {
    return <>{fallback}</>
  }

  if (!showUpgrade) {
    return null
  }

  return <UpgradePrompt 
    feature={feature}
    permission={permission}
    usageType={usageType}
    currentPlan={planName}
    lastCheck={lastCheck}
    usage={usage}
  />
}

interface UpgradePromptProps {
  feature?: string
  permission?: string
  usageType?: 'users' | 'claims' | 'storage' | 'api_calls'
  currentPlan: string
  lastCheck: any
  usage: any
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  feature,
  permission,
  usageType,
  currentPlan,
  lastCheck,
  usage
}) => {
  const [showModal, setShowModal] = useState(false)

  const getUpgradeMessage = () => {
    if (usageType && usage?.isAtLimit) {
      return {
        title: `${usageType.charAt(0).toUpperCase() + usageType.slice(1)} Limit Reached`,
        description: `You've reached your ${usageType} limit. Upgrade to continue.`,
        icon: getUsageIcon(usageType)
      }
    }

    if (feature) {
      return {
        title: 'Premium Feature',
        description: `${feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is available in higher plans.`,
        icon: Crown
      }
    }

    if (permission) {
      return {
        title: 'Permission Required',
        description: 'This action requires additional permissions.',
        icon: Lock
      }
    }

    return {
      title: 'Upgrade Required',
      description: 'This feature is not available in your current plan.',
      icon: TrendingUp
    }
  }

  const getUsageIcon = (type: string) => {
    switch (type) {
      case 'users': return Users
      case 'storage': return HardDrive
      case 'api_calls': return Activity
      default: return TrendingUp
    }
  }

  const getRecommendedPlan = () => {
    if (lastCheck?.required_plan) {
      return lastCheck.required_plan
    }

    // Simple upgrade logic based on current plan
    const upgradePath: Record<string, string> = {
      'bronze': 'silver',
      'silver': 'gold',
      'gold': 'topaz',
      'topaz': 'ruby',
      'ruby': 'diamond'
    }

    return upgradePath[currentPlan.toLowerCase()] || 'gold'
  }

  const upgradeInfo = getUpgradeMessage()
  const Icon = upgradeInfo.icon
  const recommendedPlan = getRecommendedPlan()

  return (
    <>
      {/* Inline upgrade prompt */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {upgradeInfo.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {upgradeInfo.description}
            </p>
          </div>
          <div className="flex-shrink-0">
            <button
              onClick={() => setShowModal(true)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Upgrade
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Usage bar for limit-based restrictions */}
        {usage && usage.limit !== -1 && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
              <span>{usageType?.charAt(0).toUpperCase() + usageType?.slice(1)} Usage</span>
              <span>{usage.current} / {usage.limit}</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  usage.percentage >= 100 
                    ? 'bg-red-500' 
                    : usage.percentage >= 80 
                    ? 'bg-yellow-500' 
                    : 'bg-blue-500'
                }`}
                style={{ width: `${Math.min(usage.percentage, 100)}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {/* Upgrade modal */}
      {showModal && (
        <UpgradeModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          currentPlan={currentPlan}
          recommendedPlan={recommendedPlan}
          feature={feature}
          usage={usage}
          usageType={usageType}
        />
      )}
    </>
  )
}

interface UpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  currentPlan: string
  recommendedPlan: string
  feature?: string
  usage?: any
  usageType?: string
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  currentPlan,
  recommendedPlan,
  feature,
  usage,
  usageType
}) => {
  if (!isOpen) return null

  const planPricing: Record<string, { monthly: number; annual: number; features: string[] }> = {
    silver: {
      monthly: 79,
      annual: 790,
      features: ['Up to 3 users', '500 claims', '15GB storage', 'Team collaboration']
    },
    gold: {
      monthly: 149,
      annual: 1490,
      features: ['Up to 10 users', '2,000 claims', '50GB storage', 'Advanced search', 'Bulk operations']
    },
    topaz: {
      monthly: 299,
      annual: 2990,
      features: ['Up to 25 users', '10,000 claims', '200GB storage', 'Advanced analytics', 'API access']
    },
    ruby: {
      monthly: 599,
      annual: 5990,
      features: ['Up to 100 users', '50,000 claims', '1TB storage', 'White label', 'Priority support']
    },
    diamond: {
      monthly: 1299,
      annual: 12990,
      features: ['Unlimited users', 'Unlimited claims', 'Unlimited storage', 'Custom integrations', 'Dedicated support']
    }
  }

  const plan = planPricing[recommendedPlan.toLowerCase()]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Upgrade to {recommendedPlan.charAt(0).toUpperCase() + recommendedPlan.slice(1)}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Current limitation */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
            <span className="text-sm font-medium text-red-800 dark:text-red-200">
              {feature && `Feature "${feature}" not available`}
              {usage && usageType && `${usageType} limit reached (${usage.current}/${usage.limit})`}
              {!feature && !usage && 'Access restricted'}
            </span>
          </div>
        </div>

        {/* Plan details */}
        {plan && (
          <div className="mb-6">
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                ${plan.monthly}
                <span className="text-lg font-normal text-gray-600 dark:text-gray-400">/month</span>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                or ${plan.annual}/year (save ${(plan.monthly * 12) - plan.annual})
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                What you'll get:
              </h4>
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Maybe Later
          </button>
          <button
            onClick={() => {
              // Handle upgrade action
              console.log('Upgrade to:', recommendedPlan)
              onClose()
            }}
            className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Upgrade Now
          </button>
        </div>
      </div>
    </div>
  )
}

// Usage limit warning component
export const UsageLimitWarning: React.FC<{
  usageType: 'users' | 'claims' | 'storage' | 'api_calls'
  threshold?: number
}> = ({ usageType, threshold = 80 }) => {
  const { usage } = useUsageLimit(usageType)

  if (!usage || usage.limit === -1 || usage.percentage < threshold) {
    return null
  }

  const getIcon = () => {
    switch (usageType) {
      case 'users': return Users
      case 'storage': return HardDrive
      case 'api_calls': return Activity
      default: return AlertTriangle
    }
  }

  const Icon = getIcon()
  const isAtLimit = usage.percentage >= 100
  const isNearLimit = usage.percentage >= threshold

  return (
    <div className={`p-3 rounded-lg border ${
      isAtLimit 
        ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
        : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
    }`}>
      <div className="flex items-center space-x-3">
        <Icon className={`h-5 w-5 ${
          isAtLimit ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'
        }`} />
        <div className="flex-1">
          <h4 className={`text-sm font-medium ${
            isAtLimit ? 'text-red-800 dark:text-red-200' : 'text-yellow-800 dark:text-yellow-200'
          }`}>
            {isAtLimit ? `${usageType} Limit Reached` : `${usageType} Limit Warning`}
          </h4>
          <p className={`text-sm ${
            isAtLimit ? 'text-red-700 dark:text-red-300' : 'text-yellow-700 dark:text-yellow-300'
          }`}>
            {usage.current} of {usage.limit} {usageType} used ({usage.percentage.toFixed(1)}%)
          </p>
        </div>
      </div>
    </div>
  )
}
