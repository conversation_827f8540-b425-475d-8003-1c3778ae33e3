import { useState, useEffect, createContext, useContext } from 'react'
import { supabase } from '../lib/supabase'
import { superUserService } from '../services/superUserService'
import type { SuperUser } from '../services/superUserService'

interface SuperUserContextType {
  superUser: SuperUser | null
  isSuperUser: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  checkSuperUserStatus: () => Promise<boolean>
}

const SuperUserContext = createContext<SuperUserContextType | undefined>(undefined)

export function useSuperUser() {
  const context = useContext(SuperUserContext)
  if (context === undefined) {
    throw new Error('useSuperUser must be used within a SuperUserProvider')
  }
  return context
}

export function useSuperUserAuth() {
  const [superUser, setSuperUser] = useState<SuperUser | null>(null)
  const [isSuperUser, setIsSuperUser] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkInitialAuth()
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          await checkSuperUserStatus()
        } else if (event === 'SIGNED_OUT') {
          setSuperUser(null)
          setIsSuperUser(false)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const checkInitialAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        await checkSuperUserStatus()
      }
    } catch (error) {
      console.error('Error checking initial auth:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkSuperUserStatus = async (): Promise<boolean> => {
    try {
      const isSU = await superUserService.isSuperUser()
      setIsSuperUser(isSU)

      if (isSU) {
        const suProfile = await superUserService.getCurrentSuperUser()
        setSuperUser(suProfile)
        
        // Log SuperUser login
        if (suProfile) {
          await superUserService.logSuperUserAction(
            'superuser_login',
            'SuperUser logged in',
            'system',
            'auth'
          )
        }
      } else {
        setSuperUser(null)
      }

      return isSU
    } catch (error) {
      console.error('Error checking SuperUser status:', error)
      setIsSuperUser(false)
      setSuperUser(null)
      return false
    }
  }

  const login = async (
    email: string, 
    password: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      // First, check if this email is a SuperUser
      const { data: superUserCheck, error: checkError } = await supabase
        .from('superusers')
        .select('id, is_active')
        .eq('email', email)
        .eq('is_active', true)
        .single()

      if (checkError || !superUserCheck) {
        return { success: false, error: 'Invalid SuperUser credentials' }
      }

      // Attempt Supabase auth login
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        // Log failed login attempt
        await supabase
          .from('superuser_audit_logs')
          .insert({
            superuser_email: email,
            action: 'login_failed',
            description: `Failed login attempt: ${error.message}`,
            severity: 'warning',
            ip_address: await getClientIP()
          })

        return { success: false, error: error.message }
      }

      if (data.user) {
        // Verify SuperUser status after login
        const isSU = await checkSuperUserStatus()
        if (!isSU) {
          await supabase.auth.signOut()
          return { success: false, error: 'Not authorized as SuperUser' }
        }

        return { success: true }
      }

      return { success: false, error: 'Login failed' }
    } catch (error) {
      console.error('SuperUser login error:', error)
      return { success: false, error: 'Login failed' }
    }
  }

  const logout = async (): Promise<void> => {
    try {
      // Log SuperUser logout
      if (superUser) {
        await superUserService.logSuperUserAction(
          'superuser_logout',
          'SuperUser logged out',
          'system',
          'auth'
        )
      }

      await supabase.auth.signOut()
      setSuperUser(null)
      setIsSuperUser(false)
    } catch (error) {
      console.error('SuperUser logout error:', error)
    }
  }

  return {
    superUser,
    isSuperUser,
    isLoading,
    login,
    logout,
    checkSuperUserStatus
  }
}

// Helper function to get client IP (simplified)
async function getClientIP(): Promise<string> {
  try {
    // In a real implementation, you might use a service to get the client IP
    // For now, return a placeholder
    return '127.0.0.1'
  } catch (error) {
    return '127.0.0.1'
  }
}

// SuperUser route guard hook
export function useSuperUserGuard() {
  const { isSuperUser, isLoading } = useSuperUser()
  
  return {
    isSuperUser,
    isLoading,
    canAccess: isSuperUser && !isLoading
  }
}

// Hook for SuperUser permissions
export function useSuperUserPermissions() {
  const { superUser } = useSuperUser()
  
  const hasPermission = (permission: string): boolean => {
    if (!superUser) return false
    return superUser.permissions.includes(permission) || 
           superUser.permissions.includes('platform:admin')
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: superUser?.permissions || []
  }
}

// SuperUser activity logger hook
export function useSuperUserLogger() {
  const logAction = async (
    action: string,
    description: string,
    targetType?: string,
    targetId?: string,
    metadata?: any
  ) => {
    try {
      await superUserService.logSuperUserAction(
        action,
        description,
        targetType,
        targetId,
        metadata
      )
    } catch (error) {
      console.error('Error logging SuperUser action:', error)
    }
  }

  const logError = async (
    action: string,
    error: string,
    targetType?: string,
    targetId?: string
  ) => {
    await logAction(
      action,
      `Error: ${error}`,
      targetType,
      targetId,
      { severity: 'error', error }
    )
  }

  const logWarning = async (
    action: string,
    warning: string,
    targetType?: string,
    targetId?: string
  ) => {
    await logAction(
      action,
      `Warning: ${warning}`,
      targetType,
      targetId,
      { severity: 'warning', warning }
    )
  }

  return {
    logAction,
    logError,
    logWarning
  }
}
