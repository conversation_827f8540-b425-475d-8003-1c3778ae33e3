// Database Schema Analysis and Comprehensive Testing
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://hhjfltgvnkeugftabzjl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoamZsdGd2bmtldWdmdGFiempsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTQ2NTQsImV4cCI6MjA2Mzg5MDY1NH0.i7s3ValZ_I9ncz70AT4QmOCh7S-lGbtrKY7dFs16Q_Q';

console.log('🔍 DATABASE SCHEMA ANALYSIS & COMPREHENSIVE TESTING');
console.log('====================================================');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function analyzeTableSchema(tableName) {
  try {
    const { data, error } = await supabase.from(tableName).select('*').limit(1);
    
    if (error) {
      return { tableName, accessible: false, error: error.message, columns: [] };
    }
    
    const columns = data && data.length > 0 ? Object.keys(data[0]) : [];
    return { tableName, accessible: true, columns, recordCount: data?.length || 0 };
  } catch (err) {
    return { tableName, accessible: false, error: err.message, columns: [] };
  }
}

async function runComprehensiveAnalysis() {
  console.log('\n📋 ANALYZING DATABASE SCHEMA...');
  console.log('================================');
  
  const tables = [
    'users', 'claims', 'teams', 'import_batches', 'batch_records',
    'claim_activities', 'claim_contacts', 'claim_documents', 
    'state_mapping_templates', 'standard_fields', 'upload_sessions',
    'permissions', 'business_rules', 'performance_metrics'
  ];
  
  const schemaAnalysis = {};
  
  for (const table of tables) {
    const analysis = await analyzeTableSchema(table);
    schemaAnalysis[table] = analysis;
    
    if (analysis.accessible) {
      console.log(`✅ ${table}: ${analysis.columns.length} columns`);
      if (analysis.columns.length > 0) {
        console.log(`   Columns: ${analysis.columns.join(', ')}`);
      }
    } else {
      console.log(`❌ ${table}: ${analysis.error}`);
    }
  }
  
  console.log('\n⚡ RUNNING PERFORMANCE TESTS...');
  console.log('===============================');
  
  let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0,
    tests: []
  };
  
  // Test 1: Connection Speed
  console.log('\n1. Connection Speed Test...');
  try {
    const start = Date.now();
    const { data, error } = await supabase.from('users').select('count').limit(1);
    const duration = Date.now() - start;
    
    if (error) {
      console.log(`❌ Connection failed: ${error.message}`);
      testResults.failed++;
    } else if (duration > 1000) {
      console.log(`⚠️  Connection slow: ${duration}ms`);
      testResults.warnings++;
    } else {
      console.log(`✅ Connection fast: ${duration}ms`);
      testResults.passed++;
    }
    testResults.tests.push({ name: 'Connection Speed', duration, status: error ? 'FAIL' : duration > 1000 ? 'WARN' : 'PASS' });
  } catch (err) {
    console.log(`❌ Connection error: ${err.message}`);
    testResults.failed++;
  }
  
  // Test 2: Table Access Performance
  console.log('\n2. Table Access Performance...');
  for (const table of ['users', 'claims', 'teams']) {
    try {
      const start = Date.now();
      const { data, error } = await supabase.from(table).select('*').limit(5);
      const duration = Date.now() - start;
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        testResults.failed++;
      } else if (duration > 800) {
        console.log(`⚠️  ${table}: slow access (${duration}ms)`);
        testResults.warnings++;
      } else {
        console.log(`✅ ${table}: fast access (${duration}ms, ${data?.length || 0} records)`);
        testResults.passed++;
      }
      testResults.tests.push({ name: `${table} Access`, duration, status: error ? 'FAIL' : duration > 800 ? 'WARN' : 'PASS' });
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      testResults.failed++;
    }
  }
  
  // Test 3: Complex Query Performance
  console.log('\n3. Complex Query Performance...');
  try {
    const start = Date.now();
    // Use actual column names from schema analysis
    const userColumns = schemaAnalysis.users?.columns || ['id'];
    const selectColumns = userColumns.slice(0, 3).join(', '); // Take first 3 columns
    
    const { data, error } = await supabase
      .from('users')
      .select(selectColumns)
      .limit(10);
    const duration = Date.now() - start;
    
    if (error) {
      console.log(`❌ Complex query failed: ${error.message}`);
      testResults.failed++;
    } else if (duration > 1500) {
      console.log(`⚠️  Complex query slow: ${duration}ms`);
      testResults.warnings++;
    } else {
      console.log(`✅ Complex query fast: ${duration}ms (${data?.length || 0} records)`);
      testResults.passed++;
    }
    testResults.tests.push({ name: 'Complex Query', duration, status: error ? 'FAIL' : duration > 1500 ? 'WARN' : 'PASS' });
  } catch (err) {
    console.log(`❌ Complex query error: ${err.message}`);
    testResults.failed++;
  }
  
  // Test 4: Concurrent Operations
  console.log('\n4. Concurrent Operations Test...');
  try {
    const start = Date.now();
    const promises = [
      supabase.from('users').select('id').limit(3),
      supabase.from('claims').select('id').limit(3),
      supabase.from('teams').select('id').limit(3),
      supabase.from('permissions').select('id').limit(3)
    ];
    
    const results = await Promise.all(promises);
    const duration = Date.now() - start;
    
    const errors = results.filter(r => r.error);
    if (errors.length > 0) {
      console.log(`❌ Concurrent operations failed: ${errors.length} errors`);
      testResults.failed++;
    } else if (duration > 2000) {
      console.log(`⚠️  Concurrent operations slow: ${duration}ms`);
      testResults.warnings++;
    } else {
      console.log(`✅ Concurrent operations fast: ${duration}ms`);
      testResults.passed++;
    }
    testResults.tests.push({ name: 'Concurrent Operations', duration, status: errors.length > 0 ? 'FAIL' : duration > 2000 ? 'WARN' : 'PASS' });
  } catch (err) {
    console.log(`❌ Concurrent operations error: ${err.message}`);
    testResults.failed++;
  }
  
  // Test 5: Data Integrity Check
  console.log('\n5. Data Integrity Check...');
  try {
    const start = Date.now();
    
    // Check if we have data in key tables
    const userCheck = await supabase.from('users').select('count');
    const claimCheck = await supabase.from('claims').select('count');
    const teamCheck = await supabase.from('teams').select('count');
    
    const duration = Date.now() - start;
    
    if (userCheck.error || claimCheck.error || teamCheck.error) {
      console.log(`❌ Data integrity check failed`);
      testResults.failed++;
    } else {
      console.log(`✅ Data integrity check passed (${duration}ms)`);
      testResults.passed++;
    }
    testResults.tests.push({ name: 'Data Integrity', duration, status: 'PASS' });
  } catch (err) {
    console.log(`❌ Data integrity error: ${err.message}`);
    testResults.failed++;
  }
  
  // Test 6: Stress Test (Multiple rapid queries)
  console.log('\n6. Stress Test (10 rapid queries)...');
  try {
    const start = Date.now();
    const stressPromises = [];
    
    for (let i = 0; i < 10; i++) {
      stressPromises.push(supabase.from('users').select('id').limit(1));
    }
    
    const stressResults = await Promise.all(stressPromises);
    const duration = Date.now() - start;
    
    const stressErrors = stressResults.filter(r => r.error);
    if (stressErrors.length > 0) {
      console.log(`❌ Stress test failed: ${stressErrors.length} errors`);
      testResults.failed++;
    } else if (duration > 3000) {
      console.log(`⚠️  Stress test slow: ${duration}ms`);
      testResults.warnings++;
    } else {
      console.log(`✅ Stress test passed: ${duration}ms (${stressResults.length} queries)`);
      testResults.passed++;
    }
    testResults.tests.push({ name: 'Stress Test', duration, status: stressErrors.length > 0 ? 'FAIL' : duration > 3000 ? 'WARN' : 'PASS' });
  } catch (err) {
    console.log(`❌ Stress test error: ${err.message}`);
    testResults.failed++;
  }
  
  // Generate comprehensive report
  console.log('\n📊 COMPREHENSIVE TEST RESULTS');
  console.log('==============================');
  
  const total = testResults.passed + testResults.failed + testResults.warnings;
  const successRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
  
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Warnings: ${testResults.warnings}`);
  console.log(`🎯 Success Rate: ${successRate}%`);
  
  // Calculate average response time
  const avgResponseTime = testResults.tests.length > 0 
    ? (testResults.tests.reduce((sum, test) => sum + test.duration, 0) / testResults.tests.length).toFixed(2)
    : 0;
  console.log(`📈 Average Response Time: ${avgResponseTime}ms`);
  
  // Health assessment
  let health = 'EXCELLENT';
  if (testResults.failed > 0) {
    health = 'CRITICAL';
  } else if (testResults.warnings > 3) {
    health = 'NEEDS_ATTENTION';
  } else if (testResults.warnings > 0) {
    health = 'GOOD';
  }
  
  console.log(`🏥 Database Health: ${health}`);
  console.log(`🚀 Production Ready: ${testResults.failed === 0 ? 'YES' : 'NO'}`);
  
  // Detailed recommendations
  console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
  if (testResults.failed === 0 && testResults.warnings === 0) {
    console.log('   ✅ Excellent performance! No optimizations needed.');
  } else {
    if (testResults.failed > 0) {
      console.log('   🔴 CRITICAL: Fix all failed tests before production');
    }
    if (testResults.warnings > 0) {
      console.log('   🟡 PERFORMANCE: Consider optimizing slow queries');
    }
    if (avgResponseTime > 500) {
      console.log('   🟡 LATENCY: Consider connection pooling or caching');
    }
  }
  
  // Schema recommendations
  console.log('\n🔧 SCHEMA RECOMMENDATIONS:');
  const accessibleTables = Object.values(schemaAnalysis).filter(t => t.accessible).length;
  const totalTables = Object.keys(schemaAnalysis).length;
  
  console.log(`   📊 Tables accessible: ${accessibleTables}/${totalTables}`);
  
  if (accessibleTables < totalTables) {
    console.log('   ⚠️  Some tables are not accessible - check permissions');
  }
  
  // Check for missing common columns
  const usersTable = schemaAnalysis.users;
  if (usersTable?.accessible) {
    const hasEmail = usersTable.columns.includes('email');
    const hasCreatedAt = usersTable.columns.includes('created_at');
    
    if (!hasEmail) console.log('   ⚠️  Users table missing email column');
    if (!hasCreatedAt) console.log('   ⚠️  Users table missing created_at column');
  }
  
  console.log('\n🎉 DATABASE ANALYSIS COMPLETE!');
  
  return { testResults, schemaAnalysis, health };
}

runComprehensiveAnalysis().catch(console.error);
