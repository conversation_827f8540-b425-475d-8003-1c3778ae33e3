import React, { useState, useEffect } from 'react'
import { 
  Search, 
  Filter, 
  Star, 
  Download, 
  ExternalLink, 
  Check, 
  Settings,
  Zap,
  Database,
  Mail,
  MessageSquare,
  CreditCard,
  FileText,
  Users,
  BarChart3,
  Shield,
  Globe
} from 'lucide-react'

interface Integration {
  id: string
  name: string
  description: string
  category: string
  provider: string
  logo: string
  rating: number
  reviews: number
  price: 'free' | 'paid' | 'freemium'
  status: 'available' | 'installed' | 'pending'
  features: string[]
  setup_time: string
  documentation_url: string
  support_url: string
}

export const IntegrationMarketplace: React.FC = () => {
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadIntegrations()
  }, [])

  const loadIntegrations = async () => {
    setIsLoading(true)
    try {
      // Mock integrations data
      const mockIntegrations: Integration[] = [
        {
          id: 'salesforce',
          name: 'Salesforce CRM',
          description: 'Sync claims and customer data with Salesforce CRM for unified customer management.',
          category: 'crm',
          provider: 'Salesforce',
          logo: '/integrations/salesforce.png',
          rating: 4.8,
          reviews: 1247,
          price: 'paid',
          status: 'available',
          features: ['Bi-directional sync', 'Real-time updates', 'Custom field mapping', 'Workflow automation'],
          setup_time: '15 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/salesforce',
          support_url: 'https://support.assethunterpro.com/salesforce'
        },
        {
          id: 'quickbooks',
          name: 'QuickBooks Online',
          description: 'Automatically sync financial data and generate invoices from recovered claims.',
          category: 'accounting',
          provider: 'Intuit',
          logo: '/integrations/quickbooks.png',
          rating: 4.6,
          reviews: 892,
          price: 'freemium',
          status: 'installed',
          features: ['Invoice generation', 'Payment tracking', 'Financial reporting', 'Tax preparation'],
          setup_time: '10 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/quickbooks',
          support_url: 'https://support.assethunterpro.com/quickbooks'
        },
        {
          id: 'twilio',
          name: 'Twilio Communications',
          description: 'Send SMS notifications and make automated calls for claim follow-ups.',
          category: 'communication',
          provider: 'Twilio',
          logo: '/integrations/twilio.png',
          rating: 4.7,
          reviews: 654,
          price: 'paid',
          status: 'available',
          features: ['SMS automation', 'Voice calls', 'WhatsApp messaging', 'Call recording'],
          setup_time: '5 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/twilio',
          support_url: 'https://support.assethunterpro.com/twilio'
        },
        {
          id: 'docusign',
          name: 'DocuSign',
          description: 'Enable electronic signatures for settlement agreements and legal documents.',
          category: 'legal',
          provider: 'DocuSign',
          logo: '/integrations/docusign.png',
          rating: 4.9,
          reviews: 1156,
          price: 'paid',
          status: 'pending',
          features: ['E-signatures', 'Document templates', 'Audit trails', 'Mobile signing'],
          setup_time: '20 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/docusign',
          support_url: 'https://support.assethunterpro.com/docusign'
        },
        {
          id: 'slack',
          name: 'Slack',
          description: 'Get real-time notifications and collaborate on claims directly in Slack.',
          category: 'communication',
          provider: 'Slack',
          logo: '/integrations/slack.png',
          rating: 4.5,
          reviews: 743,
          price: 'free',
          status: 'available',
          features: ['Real-time alerts', 'Claim updates', 'Team collaboration', 'Custom commands'],
          setup_time: '3 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/slack',
          support_url: 'https://support.assethunterpro.com/slack'
        },
        {
          id: 'hubspot',
          name: 'HubSpot CRM',
          description: 'Integrate with HubSpot for comprehensive customer relationship management.',
          category: 'crm',
          provider: 'HubSpot',
          logo: '/integrations/hubspot.png',
          rating: 4.4,
          reviews: 567,
          price: 'freemium',
          status: 'available',
          features: ['Contact sync', 'Deal tracking', 'Email sequences', 'Analytics'],
          setup_time: '12 minutes',
          documentation_url: 'https://docs.assethunterpro.com/integrations/hubspot',
          support_url: 'https://support.assethunterpro.com/hubspot'
        }
      ]

      setIntegrations(mockIntegrations)
    } catch (error) {
      console.error('Error loading integrations:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const categories = [
    { id: 'all', label: 'All Categories', icon: Globe },
    { id: 'crm', label: 'CRM', icon: Users },
    { id: 'accounting', label: 'Accounting', icon: CreditCard },
    { id: 'communication', label: 'Communication', icon: MessageSquare },
    { id: 'legal', label: 'Legal', icon: FileText },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'security', label: 'Security', icon: Shield }
  ]

  const getCategoryIcon = (category: string) => {
    const categoryMap: Record<string, React.ComponentType<any>> = {
      crm: Users,
      accounting: CreditCard,
      communication: MessageSquare,
      legal: FileText,
      analytics: BarChart3,
      security: Shield
    }
    return categoryMap[category] || Database
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'installed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getPriceColor = (price: string) => {
    switch (price) {
      case 'free': return 'text-green-600 dark:text-green-400'
      case 'freemium': return 'text-blue-600 dark:text-blue-400'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  const handleInstall = async (integrationId: string) => {
    try {
      // Mock installation process
      setIntegrations(prev => prev.map(integration => 
        integration.id === integrationId 
          ? { ...integration, status: 'pending' }
          : integration
      ))

      // Simulate installation delay
      setTimeout(() => {
        setIntegrations(prev => prev.map(integration => 
          integration.id === integrationId 
            ? { ...integration, status: 'installed' }
            : integration
        ))
      }, 2000)
    } catch (error) {
      console.error('Error installing integration:', error)
    }
  }

  const filteredIntegrations = integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Integration Marketplace
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Connect AssetHunterPro with your favorite tools and services
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {filteredIntegrations.length} integrations available
          </span>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search integrations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
        </div>

        <div className="flex items-center space-x-2 overflow-x-auto">
          {categories.map(category => {
            const Icon = category.icon
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{category.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Integration Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => {
          const CategoryIcon = getCategoryIcon(integration.category)
          
          return (
            <div key={integration.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <CategoryIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      {integration.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      by {integration.provider}
                    </p>
                  </div>
                </div>

                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(integration.status)}`}>
                  {integration.status === 'installed' && <Check className="h-3 w-3 mr-1" />}
                  {integration.status.charAt(0).toUpperCase() + integration.status.slice(1)}
                </span>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {integration.description}
              </p>

              {/* Features */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Key Features:
                </h4>
                <div className="flex flex-wrap gap-1">
                  {integration.features.slice(0, 3).map((feature, index) => (
                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      {feature}
                    </span>
                  ))}
                  {integration.features.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      +{integration.features.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Rating and Price */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(integration.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {integration.rating} ({integration.reviews})
                  </span>
                </div>

                <div className="text-right">
                  <div className={`text-sm font-medium ${getPriceColor(integration.price)}`}>
                    {integration.price.charAt(0).toUpperCase() + integration.price.slice(1)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {integration.setup_time} setup
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-3">
                {integration.status === 'available' && (
                  <button
                    onClick={() => handleInstall(integration.id)}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Install
                  </button>
                )}

                {integration.status === 'installed' && (
                  <button className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <Settings className="h-4 w-4 mr-2" />
                    Configure
                  </button>
                )}

                {integration.status === 'pending' && (
                  <button disabled className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-2"></div>
                    Installing...
                  </button>
                )}

                <button className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                  <ExternalLink className="h-4 w-4" />
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {/* Empty State */}
      {filteredIntegrations.length === 0 && (
        <div className="text-center py-12">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No integrations found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search or filter criteria
          </p>
        </div>
      )}
    </div>
  )
}
