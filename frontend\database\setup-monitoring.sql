-- ===================================================================
-- DATABASE MONITORING & ALERTING SETUP
-- AssetHunterPro - Production Monitoring Infrastructure
-- ===================================================================
-- This script creates comprehensive monitoring tables and functions
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 SETTING UP DATABASE MONITORING - ASSETHUNTERPRO';
    RAISE NOTICE '==================================================';
    RAISE NOTICE 'Timestamp: %', NOW();
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 1. CREATE MONITORING TABLES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🏗️  CREATING MONITORING TABLES';
    RAISE NOTICE '==============================';
END $$;

-- Database performance metrics table
CREATE TABLE IF NOT EXISTS database_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Timestamp
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Connection metrics
    active_connections INTEGER,
    max_connections INTEGER,
    connection_usage_percent DECIMAL(5,2),
    
    -- Query performance
    avg_query_time_ms DECIMAL(10,2),
    slow_query_count INTEGER,
    total_queries BIGINT,
    
    -- Database size metrics
    database_size_bytes BIGINT,
    database_size_pretty TEXT,
    largest_table TEXT,
    largest_table_size_bytes BIGINT,
    
    -- Index metrics
    total_index_size_bytes BIGINT,
    unused_index_count INTEGER,
    
    -- Table health
    bloated_table_count INTEGER,
    tables_needing_vacuum INTEGER,
    
    -- Security metrics
    rls_enabled_tables INTEGER,
    total_tables INTEGER,
    active_policies INTEGER,
    
    -- Performance status
    overall_health TEXT CHECK (overall_health IN ('EXCELLENT', 'GOOD', 'WARNING', 'CRITICAL')),
    
    -- Additional metadata
    notes TEXT
);

-- Query performance tracking
CREATE TABLE IF NOT EXISTS query_performance_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Query details
    query_type TEXT, -- 'SELECT', 'INSERT', 'UPDATE', 'DELETE'
    table_name TEXT,
    execution_time_ms DECIMAL(10,2),
    
    -- Performance classification
    performance_category TEXT CHECK (performance_category IN ('FAST', 'NORMAL', 'SLOW', 'CRITICAL')),
    
    -- Context
    user_id UUID,
    session_id TEXT,
    
    -- Query fingerprint (anonymized)
    query_fingerprint TEXT,
    
    INDEX idx_query_perf_time (recorded_at DESC),
    INDEX idx_query_perf_table (table_name),
    INDEX idx_query_perf_category (performance_category)
);

-- System alerts table
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    
    -- Alert details
    alert_type TEXT NOT NULL, -- 'PERFORMANCE', 'SECURITY', 'CAPACITY', 'ERROR'
    severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    title TEXT NOT NULL,
    description TEXT,
    
    -- Metrics that triggered the alert
    metric_name TEXT,
    metric_value DECIMAL(15,2),
    threshold_value DECIMAL(15,2),
    
    -- Status
    status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ACKNOWLEDGED', 'RESOLVED')),
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMPTZ,
    
    -- Resolution
    resolved_by UUID REFERENCES users(id),
    resolution_notes TEXT,
    
    INDEX idx_alerts_status (status),
    INDEX idx_alerts_severity (severity),
    INDEX idx_alerts_type (alert_type),
    INDEX idx_alerts_created (created_at DESC)
);

-- Database health snapshots
CREATE TABLE IF NOT EXISTS database_health_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    snapshot_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Overall health score (0-100)
    health_score INTEGER CHECK (health_score >= 0 AND health_score <= 100),
    health_status TEXT CHECK (health_status IN ('EXCELLENT', 'GOOD', 'WARNING', 'CRITICAL')),
    
    -- Performance metrics
    avg_response_time_ms DECIMAL(10,2),
    throughput_qps DECIMAL(10,2), -- Queries per second
    error_rate_percent DECIMAL(5,2),
    
    -- Resource utilization
    cpu_usage_percent DECIMAL(5,2),
    memory_usage_percent DECIMAL(5,2),
    disk_usage_percent DECIMAL(5,2),
    connection_usage_percent DECIMAL(5,2),
    
    -- Table statistics
    total_tables INTEGER,
    total_rows BIGINT,
    total_size_bytes BIGINT,
    
    -- Security status
    rls_compliance_percent DECIMAL(5,2),
    security_score INTEGER CHECK (security_score >= 0 AND security_score <= 100),
    
    -- Recommendations
    recommendations JSONB,
    
    INDEX idx_health_snapshots_time (snapshot_at DESC),
    INDEX idx_health_snapshots_score (health_score DESC)
);

DO $$
BEGIN
    RAISE NOTICE '✅ Monitoring tables created successfully';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 2. CREATE MONITORING FUNCTIONS
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 CREATING MONITORING FUNCTIONS';
    RAISE NOTICE '================================';
END $$;

-- Function to collect database performance metrics
CREATE OR REPLACE FUNCTION collect_database_metrics()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    metric_id UUID;
    conn_count INTEGER;
    max_conn INTEGER;
    db_size_bytes BIGINT;
    db_size_text TEXT;
    rls_count INTEGER;
    table_count INTEGER;
    policy_count INTEGER;
    health_status TEXT;
BEGIN
    -- Collect connection metrics
    SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active' INTO conn_count;
    SELECT setting::INTEGER FROM pg_settings WHERE name = 'max_connections' INTO max_conn;
    
    -- Collect database size
    SELECT pg_database_size(current_database()) INTO db_size_bytes;
    SELECT pg_size_pretty(db_size_bytes) INTO db_size_text;
    
    -- Collect RLS metrics
    SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public' INTO table_count;
    SELECT COUNT(*) 
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' AND c.relrowsecurity = true
    INTO rls_count;
    
    SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public' INTO policy_count;
    
    -- Determine health status
    IF conn_count::FLOAT / max_conn > 0.9 THEN
        health_status := 'CRITICAL';
    ELSIF conn_count::FLOAT / max_conn > 0.7 THEN
        health_status := 'WARNING';
    ELSIF rls_count < table_count THEN
        health_status := 'WARNING';
    ELSE
        health_status := 'EXCELLENT';
    END IF;
    
    -- Insert metrics
    INSERT INTO database_performance_metrics (
        active_connections,
        max_connections,
        connection_usage_percent,
        database_size_bytes,
        database_size_pretty,
        rls_enabled_tables,
        total_tables,
        active_policies,
        overall_health
    ) VALUES (
        conn_count,
        max_conn,
        ROUND((conn_count::DECIMAL / max_conn * 100), 2),
        db_size_bytes,
        db_size_text,
        rls_count,
        table_count,
        policy_count,
        health_status
    ) RETURNING id INTO metric_id;
    
    RETURN metric_id;
END;
$$;

-- Function to log query performance
CREATE OR REPLACE FUNCTION log_query_performance(
    p_query_type TEXT,
    p_table_name TEXT,
    p_execution_time_ms DECIMAL,
    p_user_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
    perf_category TEXT;
BEGIN
    -- Categorize performance
    IF p_execution_time_ms < 100 THEN
        perf_category := 'FAST';
    ELSIF p_execution_time_ms < 500 THEN
        perf_category := 'NORMAL';
    ELSIF p_execution_time_ms < 2000 THEN
        perf_category := 'SLOW';
    ELSE
        perf_category := 'CRITICAL';
    END IF;
    
    -- Insert log entry
    INSERT INTO query_performance_log (
        query_type,
        table_name,
        execution_time_ms,
        performance_category,
        user_id
    ) VALUES (
        p_query_type,
        p_table_name,
        p_execution_time_ms,
        perf_category,
        p_user_id
    ) RETURNING id INTO log_id;
    
    -- Create alert for critical performance
    IF perf_category = 'CRITICAL' THEN
        INSERT INTO system_alerts (
            alert_type,
            severity,
            title,
            description,
            metric_name,
            metric_value,
            threshold_value
        ) VALUES (
            'PERFORMANCE',
            'HIGH',
            'Slow Query Detected',
            format('Query on table %s took %s ms', p_table_name, p_execution_time_ms),
            'query_execution_time',
            p_execution_time_ms,
            2000
        );
    END IF;
    
    RETURN log_id;
END;
$$;

-- Function to create system alert
CREATE OR REPLACE FUNCTION create_system_alert(
    p_alert_type TEXT,
    p_severity TEXT,
    p_title TEXT,
    p_description TEXT DEFAULT NULL,
    p_metric_name TEXT DEFAULT NULL,
    p_metric_value DECIMAL DEFAULT NULL,
    p_threshold_value DECIMAL DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    alert_id UUID;
BEGIN
    INSERT INTO system_alerts (
        alert_type,
        severity,
        title,
        description,
        metric_name,
        metric_value,
        threshold_value
    ) VALUES (
        p_alert_type,
        p_severity,
        p_title,
        p_description,
        p_metric_name,
        p_metric_value,
        p_threshold_value
    ) RETURNING id INTO alert_id;
    
    RETURN alert_id;
END;
$$;

-- Function to take health snapshot
CREATE OR REPLACE FUNCTION take_health_snapshot()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    snapshot_id UUID;
    health_score INTEGER;
    health_status TEXT;
    conn_usage DECIMAL;
    rls_compliance DECIMAL;
    security_score INTEGER;
    recommendations JSONB;
BEGIN
    -- Calculate connection usage
    SELECT 
        ROUND((COUNT(*)::DECIMAL / (SELECT setting::INTEGER FROM pg_settings WHERE name = 'max_connections') * 100), 2)
    FROM pg_stat_activity WHERE state = 'active'
    INTO conn_usage;
    
    -- Calculate RLS compliance
    SELECT 
        ROUND((
            (SELECT COUNT(*) FROM pg_tables t JOIN pg_class c ON c.relname = t.tablename WHERE t.schemaname = 'public' AND c.relrowsecurity = true)::DECIMAL /
            (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public')::DECIMAL * 100
        ), 2)
    INTO rls_compliance;
    
    -- Calculate health score (0-100)
    health_score := 100;
    
    -- Deduct points for high connection usage
    IF conn_usage > 80 THEN
        health_score := health_score - 30;
    ELSIF conn_usage > 60 THEN
        health_score := health_score - 15;
    END IF;
    
    -- Deduct points for poor RLS compliance
    IF rls_compliance < 50 THEN
        health_score := health_score - 25;
    ELSIF rls_compliance < 80 THEN
        health_score := health_score - 10;
    END IF;
    
    -- Determine health status
    IF health_score >= 90 THEN
        health_status := 'EXCELLENT';
    ELSIF health_score >= 75 THEN
        health_status := 'GOOD';
    ELSIF health_score >= 60 THEN
        health_status := 'WARNING';
    ELSE
        health_status := 'CRITICAL';
    END IF;
    
    -- Calculate security score
    security_score := LEAST(100, GREATEST(0, rls_compliance::INTEGER));
    
    -- Generate recommendations
    recommendations := jsonb_build_object(
        'connection_optimization', CASE WHEN conn_usage > 70 THEN true ELSE false END,
        'rls_implementation', CASE WHEN rls_compliance < 100 THEN true ELSE false END,
        'performance_monitoring', true,
        'security_audit', CASE WHEN security_score < 80 THEN true ELSE false END
    );
    
    -- Insert snapshot
    INSERT INTO database_health_snapshots (
        health_score,
        health_status,
        connection_usage_percent,
        rls_compliance_percent,
        security_score,
        recommendations
    ) VALUES (
        health_score,
        health_status,
        conn_usage,
        rls_compliance,
        security_score,
        recommendations
    ) RETURNING id INTO snapshot_id;
    
    RETURN snapshot_id;
END;
$$;

DO $$
BEGIN
    RAISE NOTICE '✅ Monitoring functions created successfully';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 3. CREATE AUTOMATED MONITORING TRIGGERS
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '⚡ SETTING UP AUTOMATED MONITORING';
    RAISE NOTICE '==================================';
END $$;

-- Create a function to monitor table operations
CREATE OR REPLACE FUNCTION monitor_table_operations()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Log the operation (simplified for demo)
    IF TG_OP = 'INSERT' THEN
        PERFORM log_query_performance('INSERT', TG_TABLE_NAME, 50, auth.uid());
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM log_query_performance('UPDATE', TG_TABLE_NAME, 75, auth.uid());
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM log_query_performance('DELETE', TG_TABLE_NAME, 60, auth.uid());
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Add monitoring triggers to key tables (optional - can impact performance)
-- Uncomment these if you want automatic operation logging:

-- CREATE TRIGGER monitor_users_operations
--     AFTER INSERT OR UPDATE OR DELETE ON users
--     FOR EACH ROW EXECUTE FUNCTION monitor_table_operations();

-- CREATE TRIGGER monitor_claims_operations
--     AFTER INSERT OR UPDATE OR DELETE ON claims
--     FOR EACH ROW EXECUTE FUNCTION monitor_table_operations();

DO $$
BEGIN
    RAISE NOTICE '✅ Monitoring triggers configured';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 4. INITIAL MONITORING DATA COLLECTION
-- ===================================================================

DO $$
DECLARE
    metric_id UUID;
    snapshot_id UUID;
BEGIN
    RAISE NOTICE '📊 COLLECTING INITIAL MONITORING DATA';
    RAISE NOTICE '====================================';
    
    -- Collect initial metrics
    SELECT collect_database_metrics() INTO metric_id;
    RAISE NOTICE '✅ Initial metrics collected: %', metric_id;
    
    -- Take initial health snapshot
    SELECT take_health_snapshot() INTO snapshot_id;
    RAISE NOTICE '✅ Initial health snapshot taken: %', snapshot_id;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 5. MONITORING VIEWS FOR EASY ACCESS
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '👁️  CREATING MONITORING VIEWS';
    RAISE NOTICE '=============================';
END $$;

-- Current database health view
CREATE OR REPLACE VIEW current_database_health AS
SELECT 
    recorded_at,
    active_connections,
    max_connections,
    connection_usage_percent,
    database_size_pretty,
    rls_enabled_tables,
    total_tables,
    ROUND((rls_enabled_tables::DECIMAL / total_tables * 100), 2) as rls_compliance_percent,
    overall_health
FROM database_performance_metrics
ORDER BY recorded_at DESC
LIMIT 1;

-- Recent alerts view
CREATE OR REPLACE VIEW recent_alerts AS
SELECT 
    created_at,
    alert_type,
    severity,
    title,
    description,
    status,
    metric_name,
    metric_value,
    threshold_value
FROM system_alerts
WHERE status = 'ACTIVE'
ORDER BY created_at DESC;

-- Performance trends view
CREATE OR REPLACE VIEW performance_trends AS
SELECT 
    DATE_TRUNC('hour', recorded_at) as hour,
    AVG(execution_time_ms) as avg_execution_time,
    COUNT(*) as query_count,
    COUNT(*) FILTER (WHERE performance_category = 'SLOW') as slow_queries,
    COUNT(*) FILTER (WHERE performance_category = 'CRITICAL') as critical_queries
FROM query_performance_log
WHERE recorded_at >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', recorded_at)
ORDER BY hour DESC;

DO $$
BEGIN
    RAISE NOTICE '✅ Monitoring views created successfully';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 6. FINAL MONITORING SETUP VERIFICATION
-- ===================================================================

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    view_count INTEGER;
BEGIN
    RAISE NOTICE '🔍 MONITORING SETUP VERIFICATION';
    RAISE NOTICE '================================';
    
    -- Count monitoring tables
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name LIKE '%performance%' OR table_name LIKE '%alert%' OR table_name LIKE '%health%'
    INTO table_count;
    
    -- Count monitoring functions
    SELECT COUNT(*) 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND (routine_name LIKE '%monitor%' OR routine_name LIKE '%collect%' OR routine_name LIKE '%health%')
    INTO function_count;
    
    -- Count monitoring views
    SELECT COUNT(*) 
    FROM information_schema.views 
    WHERE table_schema = 'public' 
    AND (table_name LIKE '%health%' OR table_name LIKE '%alert%' OR table_name LIKE '%performance%')
    INTO view_count;
    
    RAISE NOTICE '📊 Monitoring Infrastructure Summary:';
    RAISE NOTICE '   - Monitoring Tables: %', table_count;
    RAISE NOTICE '   - Monitoring Functions: %', function_count;
    RAISE NOTICE '   - Monitoring Views: %', view_count;
    
    IF table_count >= 3 AND function_count >= 3 THEN
        RAISE NOTICE '✅ Monitoring infrastructure successfully deployed!';
    ELSE
        RAISE WARNING '⚠️  Some monitoring components may not be properly installed';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 DATABASE MONITORING SETUP COMPLETE!';
    RAISE NOTICE '📊 Your database now has comprehensive monitoring capabilities!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next Steps:';
    RAISE NOTICE '   1. Set up automated health snapshots (every hour)';
    RAISE NOTICE '   2. Configure alert notifications';
    RAISE NOTICE '   3. Create monitoring dashboards';
    RAISE NOTICE '   4. Set up log aggregation';
    RAISE NOTICE '';
END $$;
