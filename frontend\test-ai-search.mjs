// ===================================================================
// AI SEARCH TESTING SUITE
// AssetHunterPro - Comprehensive AI Search Component Testing
// ===================================================================

import { createClient } from '@supabase/supabase-js';

// Mock the AI search services since we can't import TypeScript directly
console.log('🔍 AI SEARCH COMPONENT TESTING SUITE');
console.log('====================================');

// Test data for comprehensive AI search testing
const testQueries = [
  {
    name: '<PERSON>',
    query: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      city: 'Los Angeles',
      state: 'CA',
      email: '<EMAIL>',
      phone: '(*************'
    }
  },
  {
    name: '<PERSON>',
    query: {
      firstName: '<PERSON>',
      lastName: 'Garcia',
      city: 'Houston',
      state: 'TX',
      email: '<EMAIL>'
    }
  },
  {
    name: 'Robert Johnson Test',
    query: {
      firstName: 'Robert',
      lastName: 'Johnson',
      city: 'New York',
      state: 'NY',
      phone: '(*************'
    }
  },
  {
    name: 'Empty Query Test',
    query: {
      firstName: '',
      lastName: '',
      city: '',
      state: ''
    }
  },
  {
    name: 'Partial Data Test',
    query: {
      firstName: 'Sarah',
      lastName: 'Williams'
    }
  }
];

// Mock AI Search Service Implementation
class MockAISearchService {
  constructor() {
    this.searchCount = 0;
    this.quotaUsed = 0;
    this.dailyLimit = 10;
  }

  async searchPerson(agentId, query, recordId, tenantId) {
    this.searchCount++;
    this.quotaUsed++;
    
    console.log(`\n🔍 AI Search #${this.searchCount} - Agent: ${agentId}`);
    console.log(`📋 Query:`, JSON.stringify(query, null, 2));
    console.log(`💰 Cost: $0.50 (Quota: ${this.quotaUsed}/${this.dailyLimit})`);
    
    // Check quota
    if (this.quotaUsed > this.dailyLimit) {
      return {
        success: false,
        quotaExceeded: true,
        error: 'Daily search quota exceeded',
        quota: {
          dailyUsed: this.quotaUsed,
          dailyLimit: this.dailyLimit,
          canSearch: false
        }
      };
    }

    // Simulate AI search processing
    const results = await this.performMockAISearch(query);
    
    return {
      success: true,
      results,
      quota: {
        dailyUsed: this.quotaUsed,
        dailyLimit: this.dailyLimit,
        canSearch: this.quotaUsed < this.dailyLimit
      }
    };
  }

  async performMockAISearch(query) {
    console.log('🚀 Executing parallel searches across data sources...');
    
    // Simulate the 8 data sources from the actual implementation
    const dataSources = [
      'Real Property Records (County APIs)',
      'Real Business Records (Secretary of State)', 
      'Real Court Records (PACER, State Courts)',
      'Real Voter Records (Election Offices)',
      'Real Social Media (Public APIs)',
      'Real Directory Services (WhitePages, 411)',
      'Real Professional Licenses (State Boards)',
      'Real Genealogy Records (FamilySearch)'
    ];

    const results = [];
    
    // Simulate search results based on query completeness
    const hasFullName = query.firstName && query.lastName;
    const hasLocation = query.city && query.state;
    const hasContact = query.email || query.phone;
    
    if (!hasFullName) {
      console.log('⚠️ Incomplete query - minimal results expected');
      return [{
        id: `basic_${Date.now()}`,
        confidence: 0.1,
        fullName: 'Incomplete Query',
        firstName: query.firstName || '',
        lastName: query.lastName || '',
        addresses: [],
        phoneNumbers: [],
        emailAddresses: [],
        propertyRecords: [],
        businessAffiliations: [],
        estimatedAssets: [],
        socialProfiles: [],
        relatives: [],
        employers: [],
        searchDate: new Date(),
        dataFreshness: 999,
        sources: ['Query Input Only - Insufficient Data']
      }];
    }

    // Simulate realistic search results
    for (let i = 0; i < dataSources.length; i++) {
      const source = dataSources[i];
      const searchSuccess = Math.random() > 0.3; // 70% success rate
      
      if (searchSuccess) {
        console.log(`✅ ${source}: Found data with ${(Math.random() * 40 + 60).toFixed(1)}% confidence`);
        
        const result = this.generateMockResult(query, source, i);
        if (result) {
          results.push(result);
        }
      } else {
        console.log(`ℹ️ ${source}: No data found or API unavailable`);
      }
    }

    // If no results, create basic fallback
    if (results.length === 0) {
      console.log('📝 No data sources returned results - creating basic result');
      results.push(this.createBasicResult(query));
    }

    // Deduplicate and merge results
    const mergedResults = this.deduplicateResults(results);
    
    // Sort by confidence
    const finalResults = mergedResults
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5); // Top 5 results

    console.log(`🎯 Search complete: ${finalResults.length} results found`);
    if (finalResults.length > 0) {
      const avgConfidence = (finalResults.reduce((sum, r) => sum + r.confidence, 0) / finalResults.length * 100).toFixed(1);
      console.log(`📊 Average confidence: ${avgConfidence}%`);
    }

    return finalResults;
  }

  generateMockResult(query, source, index) {
    const confidence = Math.random() * 0.4 + 0.6; // 60-100% confidence
    const fullName = `${query.firstName} ${query.lastName}`;
    
    const result = {
      id: `ai_${Date.now()}_${index}`,
      confidence,
      fullName,
      firstName: query.firstName,
      lastName: query.lastName,
      searchDate: new Date(),
      dataFreshness: Math.floor(Math.random() * 30), // 0-30 days old
      sources: [source]
    };

    // Add data based on source type
    if (source.includes('Property')) {
      result.addresses = [{
        address: `${Math.floor(Math.random() * 9999)} ${['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr'][Math.floor(Math.random() * 4)]}`,
        city: query.city || 'Unknown City',
        state: query.state || 'CA',
        zip: `${Math.floor(Math.random() * 90000) + 10000}`,
        type: 'current',
        confidence: confidence,
        verified: Math.random() > 0.5
      }];
      
      result.propertyRecords = [{
        address: result.addresses[0].address,
        propertyType: 'residential',
        ownershipType: 'sole',
        estimatedValue: Math.floor(Math.random() * 500000) + 200000,
        confidence: confidence
      }];
      
      result.estimatedAssets = [{
        type: 'real_estate',
        description: 'Property Ownership',
        estimatedValue: result.propertyRecords[0].estimatedValue,
        confidence: confidence,
        lastUpdated: new Date(),
        source: 'County Records'
      }];
    }

    if (source.includes('Business')) {
      result.businessAffiliations = [{
        businessName: `${query.lastName} ${['LLC', 'Inc', 'Corp'][Math.floor(Math.random() * 3)]}`,
        role: 'Owner/Officer',
        businessType: 'Limited Liability Company',
        confidence: confidence
      }];
      
      result.employers = [{
        companyName: result.businessAffiliations[0].businessName,
        position: 'Business Owner',
        confidence: confidence
      }];
    }

    if (source.includes('Social Media')) {
      result.socialProfiles = [{
        platform: ['LinkedIn', 'Facebook', 'Instagram'][Math.floor(Math.random() * 3)],
        profileName: fullName,
        profileUrl: `https://example.com/${query.firstName?.toLowerCase()}-${query.lastName?.toLowerCase()}`,
        verified: Math.random() > 0.7,
        confidence: confidence * 0.8 // Lower confidence for social media
      }];
    }

    if (source.includes('Directory')) {
      result.phoneNumbers = [{
        number: query.phone || `(${Math.floor(Math.random() * 800) + 200}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        type: 'home',
        carrier: 'Unknown',
        isActive: Math.random() > 0.3,
        confidence: confidence,
        verified: Math.random() > 0.5
      }];
      
      result.emailAddresses = [{
        email: query.email || `${query.firstName?.toLowerCase()}.${query.lastName?.toLowerCase()}@email.com`,
        isActive: Math.random() > 0.4,
        domain: query.email?.split('@')[1] || 'email.com',
        confidence: confidence,
        verified: Math.random() > 0.6
      }];
    }

    if (source.includes('Voter')) {
      result.addresses = result.addresses || [{
        address: `${Math.floor(Math.random() * 9999)} Voter Ave`,
        city: query.city || 'Unknown City',
        state: query.state || 'CA',
        zip: `${Math.floor(Math.random() * 90000) + 10000}`,
        type: 'current',
        confidence: 0.95, // High confidence for voter records
        verified: true
      }];
    }

    if (source.includes('Genealogy')) {
      result.relatives = [{
        name: `${['James', 'Mary', 'Robert', 'Patricia'][Math.floor(Math.random() * 4)]} ${query.lastName}`,
        relationship: ['spouse', 'parent', 'sibling', 'child'][Math.floor(Math.random() * 4)],
        confidence: confidence * 0.7
      }];
    }

    // Ensure all arrays exist
    result.addresses = result.addresses || [];
    result.phoneNumbers = result.phoneNumbers || [];
    result.emailAddresses = result.emailAddresses || [];
    result.propertyRecords = result.propertyRecords || [];
    result.businessAffiliations = result.businessAffiliations || [];
    result.estimatedAssets = result.estimatedAssets || [];
    result.socialProfiles = result.socialProfiles || [];
    result.relatives = result.relatives || [];
    result.employers = result.employers || [];

    return result;
  }

  createBasicResult(query) {
    return {
      id: `basic_${Date.now()}`,
      confidence: 0.1,
      fullName: `${query.firstName} ${query.lastName}`,
      firstName: query.firstName,
      lastName: query.lastName,
      addresses: query.city ? [{
        address: 'Address research needed',
        city: query.city,
        state: query.state || 'Unknown',
        zip: '00000',
        type: 'current',
        confidence: 0.1,
        verified: false
      }] : [],
      phoneNumbers: query.phone ? [{
        number: query.phone,
        type: 'unknown',
        carrier: 'Unknown',
        isActive: false,
        confidence: 0.1,
        verified: false
      }] : [],
      emailAddresses: query.email ? [{
        email: query.email,
        isActive: false,
        domain: query.email.split('@')[1] || 'unknown.com',
        confidence: 0.1,
        verified: false
      }] : [],
      propertyRecords: [],
      businessAffiliations: [],
      estimatedAssets: [],
      socialProfiles: [],
      relatives: [],
      employers: [],
      searchDate: new Date(),
      dataFreshness: 999,
      sources: ['Query Input Only - Real APIs Failed']
    };
  }

  deduplicateResults(results) {
    // Simple deduplication by name and confidence
    const seen = new Map();
    const deduplicated = [];

    for (const result of results) {
      const key = `${result.firstName}_${result.lastName}`;
      const existing = seen.get(key);
      
      if (!existing || result.confidence > existing.confidence) {
        seen.set(key, result);
      }
    }

    return Array.from(seen.values());
  }
}

// Test execution
async function runAISearchTests() {
  console.log('🚀 Starting AI Search Component Testing');
  console.log('========================================\n');

  const aiSearchService = new MockAISearchService();
  const testResults = [];

  for (let i = 0; i < testQueries.length; i++) {
    const test = testQueries[i];
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🧪 TEST ${i + 1}: ${test.name}`);
    console.log(`${'='.repeat(60)}`);

    const startTime = Date.now();
    
    try {
      const result = await aiSearchService.searchPerson(
        'test-agent-123',
        test.query,
        `test-record-${i}`,
        'test-tenant'
      );

      const duration = Date.now() - startTime;

      testResults.push({
        testName: test.name,
        success: result.success,
        duration,
        resultCount: result.results?.length || 0,
        quotaExceeded: result.quotaExceeded || false,
        avgConfidence: result.results?.length > 0 
          ? (result.results.reduce((sum, r) => sum + r.confidence, 0) / result.results.length * 100).toFixed(1)
          : 0,
        quota: result.quota
      });

      if (result.success && result.results) {
        console.log(`\n📊 SEARCH RESULTS ANALYSIS:`);
        console.log(`   - Results Found: ${result.results.length}`);
        console.log(`   - Average Confidence: ${testResults[testResults.length - 1].avgConfidence}%`);
        console.log(`   - Search Duration: ${duration}ms`);
        console.log(`   - Quota Status: ${result.quota.dailyUsed}/${result.quota.dailyLimit}`);

        // Analyze result quality
        result.results.forEach((person, index) => {
          console.log(`\n   👤 Result ${index + 1}: ${person.fullName} (${(person.confidence * 100).toFixed(1)}% confidence)`);
          console.log(`      - Addresses: ${person.addresses.length}`);
          console.log(`      - Phone Numbers: ${person.phoneNumbers.length}`);
          console.log(`      - Email Addresses: ${person.emailAddresses.length}`);
          console.log(`      - Property Records: ${person.propertyRecords.length}`);
          console.log(`      - Business Affiliations: ${person.businessAffiliations.length}`);
          console.log(`      - Social Profiles: ${person.socialProfiles.length}`);
          console.log(`      - Data Sources: ${person.sources.join(', ')}`);
          console.log(`      - Data Freshness: ${person.dataFreshness} days old`);
        });
      } else {
        console.log(`\n❌ SEARCH FAILED:`);
        console.log(`   - Error: ${result.error}`);
        console.log(`   - Quota Exceeded: ${result.quotaExceeded}`);
      }

    } catch (error) {
      console.error(`❌ Test failed with error:`, error);
      testResults.push({
        testName: test.name,
        success: false,
        duration: Date.now() - startTime,
        error: error.message
      });
    }

    // Brief pause between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Generate comprehensive test report
  console.log(`\n${'='.repeat(80)}`);
  console.log('📊 COMPREHENSIVE AI SEARCH TEST REPORT');
  console.log(`${'='.repeat(80)}`);

  const successfulTests = testResults.filter(t => t.success);
  const failedTests = testResults.filter(t => !t.success);
  const avgDuration = testResults.reduce((sum, t) => sum + t.duration, 0) / testResults.length;
  const totalResults = testResults.reduce((sum, t) => sum + (t.resultCount || 0), 0);

  console.log(`\n📈 PERFORMANCE METRICS:`);
  console.log(`   - Total Tests: ${testResults.length}`);
  console.log(`   - Successful: ${successfulTests.length}`);
  console.log(`   - Failed: ${failedTests.length}`);
  console.log(`   - Success Rate: ${(successfulTests.length / testResults.length * 100).toFixed(1)}%`);
  console.log(`   - Average Duration: ${avgDuration.toFixed(0)}ms`);
  console.log(`   - Total Results Found: ${totalResults}`);
  console.log(`   - Average Results per Search: ${(totalResults / successfulTests.length || 0).toFixed(1)}`);

  if (successfulTests.length > 0) {
    const avgConfidence = successfulTests.reduce((sum, t) => sum + parseFloat(t.avgConfidence || 0), 0) / successfulTests.length;
    console.log(`   - Average Confidence: ${avgConfidence.toFixed(1)}%`);
  }

  console.log(`\n🎯 QUALITY ASSESSMENT:`);
  if (avgDuration < 2000) {
    console.log(`   ✅ Response Time: Excellent (${avgDuration.toFixed(0)}ms)`);
  } else if (avgDuration < 5000) {
    console.log(`   ⚠️ Response Time: Acceptable (${avgDuration.toFixed(0)}ms)`);
  } else {
    console.log(`   ❌ Response Time: Poor (${avgDuration.toFixed(0)}ms)`);
  }

  const successRate = successfulTests.length / testResults.length;
  if (successRate >= 0.9) {
    console.log(`   ✅ Reliability: Excellent (${(successRate * 100).toFixed(1)}%)`);
  } else if (successRate >= 0.7) {
    console.log(`   ⚠️ Reliability: Good (${(successRate * 100).toFixed(1)}%)`);
  } else {
    console.log(`   ❌ Reliability: Needs Improvement (${(successRate * 100).toFixed(1)}%)`);
  }

  console.log(`\n💡 OPTIMIZATION RECOMMENDATIONS:`);
  console.log(`   1. Current system uses mock data - implement real API integrations`);
  console.log(`   2. Add caching to improve response times for repeated searches`);
  console.log(`   3. Implement progressive data loading for better user experience`);
  console.log(`   4. Add data validation and confidence scoring improvements`);
  console.log(`   5. Consider implementing free data sources first (government APIs)`);

  console.log(`\n💰 COST ANALYSIS:`);
  console.log(`   - Current Cost per Search: $0.50`);
  console.log(`   - Daily Quota: 10 searches`);
  console.log(`   - Monthly Cost (full usage): $150`);
  console.log(`   - Recommendation: Start with free APIs to reduce costs`);

  return testResults;
}

// Run the tests
runAISearchTests().catch(console.error);
