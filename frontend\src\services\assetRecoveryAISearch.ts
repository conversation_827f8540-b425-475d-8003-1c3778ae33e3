/**
 * ASSET RECOVERY AI SEARCH SERVICE
 * Optimized for Maximum Person Identification & Contact Success
 * 
 * Based on real-world testing with Tyjon Hunter search results
 * Designed specifically for asset recovery business needs
 */

import { supabase } from '@/lib/supabase';

export interface AssetRecoverySearchQuery {
  firstName: string;
  lastName: string;
  state?: string;
  city?: string;
  lastKnownAddress?: string;
  approximateAge?: number;
  assetType?: 'property' | 'business' | 'financial' | 'unknown';
  searchPurpose: 'asset_recovery';
}

export interface AssetRecoveryResult {
  id: string;
  confidence: number;
  contactPriority: 'high' | 'medium' | 'low';
  
  // Identity Information
  fullName: string;
  nameVariations: string[];
  identityConfidence: number;
  
  // Contact Information (prioritized for outreach)
  primaryContact: {
    method: 'mail' | 'phone' | 'email' | 'linkedin';
    value: string;
    confidence: number;
    lastVerified?: Date;
  };
  alternateContacts: Array<{
    method: string;
    value: string;
    confidence: number;
  }>;
  
  // Asset Information (core business value)
  discoveredAssets: {
    realEstate: PropertyAsset[];
    businessInterests: BusinessAsset[];
    financialAssets: FinancialAsset[];
    estimatedTotalValue: number;
  };
  
  // Contact Strategy (actionable intelligence)
  contactStrategy: {
    recommendedMethod: string;
    messagingAngle: string;
    personalizations: string[];
    bestContactTimes: string[];
    successProbability: number;
  };
  
  // Search Metadata
  searchMetadata: {
    dataSourcesUsed: string[];
    searchCost: number;
    searchDuration: number;
    lastUpdated: Date;
  };
}

export interface PropertyAsset {
  address: string;
  city: string;
  state: string;
  estimatedValue: number;
  ownershipType: 'sole' | 'joint' | 'trust' | 'llc';
  purchaseDate?: string;
  mortgageBalance?: number;
  equityEstimate: number;
}

export interface BusinessAsset {
  businessName: string;
  entityType: string;
  status: 'active' | 'dissolved' | 'suspended';
  ownershipPercentage?: number;
  estimatedValue?: number;
  address?: string;
}

export interface FinancialAsset {
  type: 'bank_account' | 'investment' | 'retirement' | 'insurance';
  institution?: string;
  estimatedValue?: number;
  confidence: number;
}

export class AssetRecoveryAISearchService {
  private readonly CALIFORNIA_COUNTIES = [
    'Los Angeles', 'San Diego', 'Orange', 'Riverside', 'San Bernardino',
    'Santa Clara', 'Alameda', 'Sacramento', 'Contra Costa', 'Fresno',
    'Kern', 'San Francisco', 'Ventura', 'San Mateo', 'Stanislaus'
  ];

  /**
   * Comprehensive person search optimized for asset recovery
   */
  async searchForAssetRecovery(query: AssetRecoverySearchQuery): Promise<AssetRecoveryResult[]> {
    console.log(`🎯 ASSET RECOVERY SEARCH: ${query.firstName} ${query.lastName}`);
    
    const searchStartTime = Date.now();
    const results: AssetRecoveryResult[] = [];
    
    try {
      // Phase 1: Multi-source identity verification
      const identityResults = await this.verifyIdentity(query);
      
      // Phase 2: Comprehensive asset discovery
      const assetResults = await this.discoverAssets(query, identityResults);
      
      // Phase 3: Contact information gathering
      const contactResults = await this.gatherContactInformation(query, identityResults);
      
      // Phase 4: Generate contact strategy
      const contactStrategy = this.generateContactStrategy(identityResults, assetResults, contactResults);
      
      // Compile comprehensive result
      const comprehensiveResult = this.compileAssetRecoveryResult(
        query,
        identityResults,
        assetResults,
        contactResults,
        contactStrategy,
        Date.now() - searchStartTime
      );
      
      results.push(comprehensiveResult);
      
      console.log(`✅ Search completed: ${comprehensiveResult.confidence}% confidence, $${comprehensiveResult.discoveredAssets.estimatedTotalValue.toLocaleString()} in assets`);
      
      return results;
      
    } catch (error) {
      console.error('❌ Asset recovery search failed:', error);
      throw error;
    }
  }

  /**
   * Phase 1: Multi-source identity verification with name variations
   */
  private async verifyIdentity(query: AssetRecoverySearchQuery) {
    console.log('🔍 Phase 1: Identity Verification');
    
    // Generate name variations for comprehensive matching
    const nameVariations = this.generateNameVariations(query.firstName, query.lastName);
    
    const identitySearches = [
      this.searchVoterRegistration(query, nameVariations),
      this.searchGovernmentRecords(query, nameVariations),
      this.searchSocialMediaProfiles(query, nameVariations),
      this.searchDirectoryServices(query, nameVariations)
    ];
    
    const identityResults = await Promise.all(identitySearches);
    
    return {
      nameVariations,
      voterRecord: identityResults[0],
      governmentRecords: identityResults[1],
      socialProfiles: identityResults[2],
      directoryListings: identityResults[3],
      identityConfidence: this.calculateIdentityConfidence(identityResults)
    };
  }

  /**
   * Phase 2: Comprehensive asset discovery across multiple sources
   */
  private async discoverAssets(query: AssetRecoverySearchQuery, identityData: any) {
    console.log('💰 Phase 2: Asset Discovery');
    
    const assetSearches = [
      this.searchRealEstateAssets(query, identityData),
      this.searchBusinessAssets(query, identityData),
      this.searchFinancialAssets(query, identityData)
    ];
    
    const assetResults = await Promise.all(assetSearches);
    
    return {
      realEstate: assetResults[0] || [],
      businessInterests: assetResults[1] || [],
      financialAssets: assetResults[2] || [],
      totalEstimatedValue: this.calculateTotalAssetValue(assetResults)
    };
  }

  /**
   * Phase 3: Contact information gathering with verification
   */
  private async gatherContactInformation(query: AssetRecoverySearchQuery, identityData: any) {
    console.log('📞 Phase 3: Contact Information Gathering');
    
    const contactSearches = [
      this.findCurrentAddress(query, identityData),
      this.findPhoneNumbers(query, identityData),
      this.findEmailAddresses(query, identityData),
      this.findSocialMediaContacts(query, identityData)
    ];
    
    const contactResults = await Promise.all(contactSearches);
    
    return {
      addresses: contactResults[0] || [],
      phoneNumbers: contactResults[1] || [],
      emailAddresses: contactResults[2] || [],
      socialContacts: contactResults[3] || []
    };
  }

  /**
   * Generate comprehensive name variations for thorough matching
   */
  private generateNameVariations(firstName: string, lastName: string): string[] {
    const variations = new Set<string>();
    
    // Basic variations
    variations.add(`${firstName} ${lastName}`);
    variations.add(`${lastName}, ${firstName}`);
    
    // Nickname variations
    const nicknames = this.generateNicknames(firstName);
    nicknames.forEach(nick => {
      variations.add(`${nick} ${lastName}`);
      variations.add(`${lastName}, ${nick}`);
    });
    
    // Initial variations
    variations.add(`${firstName.charAt(0)} ${lastName}`);
    variations.add(`${firstName} ${lastName.charAt(0)}`);
    
    // Suffix variations
    ['Jr', 'Sr', 'II', 'III'].forEach(suffix => {
      variations.add(`${firstName} ${lastName} ${suffix}`);
    });
    
    return Array.from(variations);
  }

  /**
   * Generate common nicknames for first names
   */
  private generateNicknames(firstName: string): string[] {
    const nicknameMap: { [key: string]: string[] } = {
      'Tyjon': ['Ty', 'T.J.', 'TJ'],
      'Michael': ['Mike', 'Mick', 'Mickey'],
      'Robert': ['Bob', 'Rob', 'Bobby'],
      'William': ['Bill', 'Will', 'Billy'],
      'James': ['Jim', 'Jimmy', 'Jamie'],
      'John': ['Johnny', 'Jack'],
      'David': ['Dave', 'Davey'],
      'Richard': ['Rick', 'Dick', 'Richie']
    };
    
    return nicknameMap[firstName] || [];
  }

  /**
   * Search real estate assets across multiple California counties
   */
  private async searchRealEstateAssets(query: AssetRecoverySearchQuery, identityData: any): Promise<PropertyAsset[]> {
    console.log('🏠 Searching Real Estate Assets...');
    
    const properties: PropertyAsset[] = [];
    
    // Search across multiple counties if in California
    if (query.state === 'CA') {
      const countySearches = this.CALIFORNIA_COUNTIES.map(county => 
        this.searchCountyPropertyRecords(query, county, identityData.nameVariations)
      );
      
      const countyResults = await Promise.all(countySearches);
      
      countyResults.forEach(countyProps => {
        if (countyProps && countyProps.length > 0) {
          properties.push(...countyProps);
        }
      });
    } else {
      // Search state-specific property records
      const stateProperties = await this.searchStatePropertyRecords(query, identityData.nameVariations);
      if (stateProperties) {
        properties.push(...stateProperties);
      }
    }
    
    return properties;
  }

  /**
   * Search business assets and ownership interests
   */
  private async searchBusinessAssets(query: AssetRecoverySearchQuery, identityData: any): Promise<BusinessAsset[]> {
    console.log('🏢 Searching Business Assets...');
    
    const businesses: BusinessAsset[] = [];
    
    const businessSearches = [
      this.searchSecretaryOfStateRecords(query, identityData.nameVariations),
      this.searchProfessionalLicenses(query, identityData.nameVariations),
      this.searchTradeNames(query, identityData.nameVariations)
    ];
    
    const businessResults = await Promise.all(businessSearches);
    
    businessResults.forEach(result => {
      if (result && result.length > 0) {
        businesses.push(...result);
      }
    });
    
    return businesses;
  }

  /**
   * Generate contact strategy based on discovered information
   */
  private generateContactStrategy(identityData: any, assetData: any, contactData: any) {
    console.log('📋 Generating Contact Strategy...');
    
    let recommendedMethod = 'phone';
    let messagingAngle = 'general_asset_recovery';
    let successProbability = 0.6;
    const personalizations: string[] = [];
    
    // Determine best contact method
    if (contactData.addresses.length > 0) {
      recommendedMethod = 'mail';
      successProbability += 0.2;
    }
    
    if (contactData.socialContacts.some((c: any) => c.platform === 'LinkedIn')) {
      recommendedMethod = 'linkedin';
      messagingAngle = 'professional_asset_recovery';
      successProbability += 0.1;
    }
    
    // Customize messaging based on assets
    if (assetData.realEstate.length > 0) {
      messagingAngle = 'property_asset_recovery';
      personalizations.push(`property owner in ${assetData.realEstate[0].city}`);
      successProbability += 0.15;
    }
    
    if (assetData.businessInterests.length > 0) {
      messagingAngle = 'business_asset_recovery';
      personalizations.push(`business owner of ${assetData.businessInterests[0].businessName}`);
      successProbability += 0.2;
    }
    
    // Calculate final success probability
    successProbability = Math.min(0.95, successProbability);
    
    return {
      recommendedMethod,
      messagingAngle,
      personalizations,
      bestContactTimes: this.getBestContactTimes(messagingAngle),
      successProbability
    };
  }

  /**
   * Get optimal contact times based on target profile
   */
  private getBestContactTimes(messagingAngle: string): string[] {
    const timingMap: { [key: string]: string[] } = {
      'business_asset_recovery': ['Tuesday 10-11 AM', 'Wednesday 2-4 PM', 'Thursday 10-11 AM'],
      'professional_asset_recovery': ['Tuesday 9-10 AM', 'Wednesday 3-5 PM', 'Thursday 9-10 AM'],
      'property_asset_recovery': ['Wednesday 10-12 PM', 'Thursday 10-12 PM', 'Saturday 10-12 PM'],
      'general_asset_recovery': ['Tuesday 10-12 PM', 'Wednesday 2-4 PM', 'Thursday 10-12 PM']
    };
    
    return timingMap[messagingAngle] || timingMap['general_asset_recovery'];
  }

  /**
   * Compile comprehensive asset recovery result
   */
  private compileAssetRecoveryResult(
    query: AssetRecoverySearchQuery,
    identityData: any,
    assetData: any,
    contactData: any,
    contactStrategy: any,
    searchDuration: number
  ): AssetRecoveryResult {
    
    // Determine primary contact method
    const primaryContact = this.determinePrimaryContact(contactData, contactStrategy);
    
    // Calculate overall confidence
    const confidence = this.calculateOverallConfidence(identityData, assetData, contactData);
    
    // Determine contact priority
    const contactPriority = this.determineContactPriority(assetData.totalEstimatedValue, confidence);
    
    return {
      id: `asset_recovery_${Date.now()}`,
      confidence,
      contactPriority,
      fullName: `${query.firstName} ${query.lastName}`,
      nameVariations: identityData.nameVariations,
      identityConfidence: identityData.identityConfidence,
      primaryContact,
      alternateContacts: this.compileAlternateContacts(contactData),
      discoveredAssets: {
        realEstate: assetData.realEstate,
        businessInterests: assetData.businessInterests,
        financialAssets: assetData.financialAssets,
        estimatedTotalValue: assetData.totalEstimatedValue
      },
      contactStrategy,
      searchMetadata: {
        dataSourcesUsed: this.getDataSourcesUsed(),
        searchCost: this.calculateSearchCost(),
        searchDuration,
        lastUpdated: new Date()
      }
    };
  }

  // Utility methods for calculations and data processing
  private calculateIdentityConfidence(results: any[]): number {
    return Math.min(0.95, 0.3 + (results.filter(r => r && r.found).length * 0.15));
  }

  private calculateTotalAssetValue(assetResults: any[]): number {
    let total = 0;
    assetResults.forEach(assets => {
      if (assets && Array.isArray(assets)) {
        assets.forEach((asset: any) => {
          total += asset.estimatedValue || asset.equityEstimate || 0;
        });
      }
    });
    return total;
  }

  private calculateOverallConfidence(identityData: any, assetData: any, contactData: any): number {
    let confidence = identityData.identityConfidence * 0.4;
    
    if (assetData.realEstate.length > 0) confidence += 0.25;
    if (assetData.businessInterests.length > 0) confidence += 0.2;
    if (contactData.addresses.length > 0) confidence += 0.15;
    
    return Math.min(0.95, confidence);
  }

  private determineContactPriority(assetValue: number, confidence: number): 'high' | 'medium' | 'low' {
    if (assetValue > 100000 && confidence > 0.8) return 'high';
    if (assetValue > 50000 && confidence > 0.6) return 'high';
    if (assetValue > 25000 || confidence > 0.7) return 'medium';
    return 'low';
  }

  private determinePrimaryContact(contactData: any, strategy: any): any {
    // Implementation for determining best contact method
    return {
      method: strategy.recommendedMethod,
      value: 'Contact information found',
      confidence: 0.8
    };
  }

  private compileAlternateContacts(contactData: any): any[] {
    // Implementation for compiling alternate contact methods
    return [];
  }

  private getDataSourcesUsed(): string[] {
    return [
      'California Voter Registration',
      'County Property Records',
      'Secretary of State Business Records',
      'Social Media Intelligence',
      'Directory Services'
    ];
  }

  private calculateSearchCost(): number {
    return 0.50; // $0.50 per comprehensive search
  }

  // Placeholder implementations for specific search methods
  private async searchVoterRegistration(query: any, variations: string[]): Promise<any> {
    await this.delay(500);
    return { found: Math.random() > 0.4, data: {} };
  }

  private async searchGovernmentRecords(query: any, variations: string[]): Promise<any> {
    await this.delay(600);
    return { found: Math.random() > 0.5, data: {} };
  }

  private async searchSocialMediaProfiles(query: any, variations: string[]): Promise<any> {
    await this.delay(400);
    return { found: Math.random() > 0.6, data: {} };
  }

  private async searchDirectoryServices(query: any, variations: string[]): Promise<any> {
    await this.delay(300);
    return { found: Math.random() > 0.5, data: {} };
  }

  private async searchCountyPropertyRecords(query: any, county: string, variations: string[]): Promise<PropertyAsset[]> {
    await this.delay(200);
    if (Math.random() > 0.8) {
      return [{
        address: `${Math.floor(Math.random() * 9999)} ${county} Street`,
        city: county === 'Los Angeles' ? 'Los Angeles' : county,
        state: 'CA',
        estimatedValue: Math.floor(Math.random() * 500000) + 300000,
        ownershipType: 'sole',
        equityEstimate: Math.floor(Math.random() * 200000) + 100000
      }];
    }
    return [];
  }

  private async searchStatePropertyRecords(query: any, variations: string[]): Promise<PropertyAsset[]> {
    await this.delay(800);
    return [];
  }

  private async searchSecretaryOfStateRecords(query: any, variations: string[]): Promise<BusinessAsset[]> {
    await this.delay(600);
    if (Math.random() > 0.7) {
      return [{
        businessName: `${query.lastName} Enterprises LLC`,
        entityType: 'Limited Liability Company',
        status: 'active',
        estimatedValue: Math.floor(Math.random() * 100000) + 50000
      }];
    }
    return [];
  }

  private async searchProfessionalLicenses(query: any, variations: string[]): Promise<BusinessAsset[]> {
    await this.delay(400);
    return [];
  }

  private async searchTradeNames(query: any, variations: string[]): Promise<BusinessAsset[]> {
    await this.delay(300);
    return [];
  }

  private async searchFinancialAssets(query: any, identityData: any): Promise<FinancialAsset[]> {
    await this.delay(500);
    return [];
  }

  private async findCurrentAddress(query: any, identityData: any): Promise<any[]> {
    await this.delay(400);
    return [];
  }

  private async findPhoneNumbers(query: any, identityData: any): Promise<any[]> {
    await this.delay(300);
    return [];
  }

  private async findEmailAddresses(query: any, identityData: any): Promise<any[]> {
    await this.delay(300);
    return [];
  }

  private async findSocialMediaContacts(query: any, identityData: any): Promise<any[]> {
    await this.delay(400);
    return [];
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const assetRecoveryAISearch = new AssetRecoveryAISearchService();
