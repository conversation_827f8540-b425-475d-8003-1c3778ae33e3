-- Asset Recovery Pro - Complete Database Schema
-- Supports all user stories: US-001 to US-022 (Junior Agents), US-101 to US-108 (Senior Agents), US-201 to US-210 (Admins)

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================
-- CORE USER & TEAM MANAGEMENT
-- ===================================

-- Organizations/Companies table for multi-tenant support
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE,
    industry VARCHAR(100),
    size VARCHAR(20) CHECK (size IN ('small', 'medium', 'large', 'enterprise')),
    established_date DATE,

    -- Address information
    street_address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',

    -- Contact information
    primary_email VARCHAR(255),
    primary_phone VARCHAR(20),
    website VARCHAR(255),

    -- Branding configuration
    logo_url VARCHAR(500),
    logo_file_name VARCHAR(255),
    primary_color VARCHAR(7) DEFAULT '#3B82F6', -- Blue
    secondary_color VARCHAR(7) DEFAULT '#1E40AF', -- Dark blue
    accent_color VARCHAR(7) DEFAULT '#10B981', -- Green

    -- Settings
    timezone VARCHAR(50) DEFAULT 'UTC',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    currency VARCHAR(3) DEFAULT 'USD',
    language VARCHAR(5) DEFAULT 'en',

    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User accounts and authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('junior_agent', 'senior_agent', 'admin', 'compliance', 'finance')),
    employee_id VARCHAR(50),
    department VARCHAR(100),
    manager_id UUID REFERENCES users(id),
    team_id UUID, -- Will reference teams table
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure unique employee_id within organization
    UNIQUE(organization_id, employee_id)
);

-- Teams and organizational structure
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key reference after teams table is created
ALTER TABLE users ADD CONSTRAINT fk_users_team_id FOREIGN KEY (team_id) REFERENCES teams(id);

-- User permissions and roles
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role VARCHAR(50) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    actions_allowed TEXT[] NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- CLAIMS MANAGEMENT CORE
-- ===================================

-- Main claims table (from batch imports and manual entry)
CREATE TABLE claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id VARCHAR(100),
    owner_name VARCHAR(255) NOT NULL,
    owner_first_name VARCHAR(100),
    owner_last_name VARCHAR(100), 
    owner_business_name VARCHAR(255),
    amount DECIMAL(12,2) NOT NULL,
    property_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'assigned', 'contacted', 'in_progress', 'documents_requested', 'under_review', 'approved', 'completed', 'on_hold', 'cancelled')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assigned_agent_id UUID REFERENCES users(id),
    state VARCHAR(2) NOT NULL,
    
    -- Holder information
    holder_name VARCHAR(255),
    holder_address TEXT,
    holder_city VARCHAR(100),
    holder_state VARCHAR(2),
    holder_zip VARCHAR(20),
    
    -- Owner address information
    owner_address TEXT,
    owner_city VARCHAR(100),
    owner_state VARCHAR(2),
    owner_zip VARCHAR(20),
    
    -- Additional metadata
    report_date DATE,
    shares_reported INTEGER,
    securities_name VARCHAR(255),
    cusip VARCHAR(20),
    
    -- Processing information
    import_batch_id UUID, -- References import_batches
    complexity_score INTEGER DEFAULT 1,
    estimated_recovery_amount DECIMAL(12,2),
    actual_recovery_amount DECIMAL(12,2),
    commission_rate DECIMAL(5,4) DEFAULT 0.25,
    commission_amount DECIMAL(12,2),
    
    -- Compliance and audit
    compliance_status VARCHAR(50) DEFAULT 'pending',
    last_contact_date DATE,
    next_followup_date DATE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Contact methods for claimants (US-002)
CREATE TABLE claim_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    contact_type VARCHAR(20) NOT NULL CHECK (contact_type IN ('phone', 'email', 'address')),
    contact_value VARCHAR(500) NOT NULL,
    label VARCHAR(50), -- Home, Work, Mobile, Primary, etc.
    is_primary BOOLEAN DEFAULT false,
    is_valid BOOLEAN DEFAULT true,
    last_verified_at TIMESTAMPTZ,
    verification_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activity timeline for all claim interactions (US-004)  
CREATE TABLE claim_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN ('call', 'email', 'sms', 'note', 'status_change', 'document_upload', 'payment', 'other')),
    outcome VARCHAR(50), -- Connected, Voicemail, Busy, Wrong Number, Interested, Not Interested, etc.
    title VARCHAR(255) NOT NULL,
    description TEXT,
    contact_method_id UUID REFERENCES claim_contacts(id),
    scheduled_followup_at TIMESTAMPTZ,
    duration_minutes INTEGER,
    attachments JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document management (US-003)
CREATE TABLE claim_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    file_name VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    category VARCHAR(100) NOT NULL CHECK (category IN ('id_documents', 'contracts', 'correspondence', 'state_forms', 'signatures', 'other')),
    description TEXT,
    file_path VARCHAR(1000),
    file_url VARCHAR(1000),
    uploaded_by UUID NOT NULL REFERENCES users(id),
    permissions VARCHAR(50) DEFAULT 'internal' CHECK (permissions IN ('internal', 'shareable')),
    is_signed BOOLEAN DEFAULT false,
    signature_status VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Claimant relationships (US-005)
CREATE TABLE claim_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    person_name VARCHAR(255) NOT NULL,
    relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN ('spouse', 'business_partner', 'heir', 'legal_representative', 'power_of_attorney', 'other')),
    contact_info JSONB DEFAULT '{}',
    is_primary_contact BOOLEAN DEFAULT false,
    documentation_provided BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- External references and links (US-006)
CREATE TABLE claim_references (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    url VARCHAR(1000) NOT NULL,
    description VARCHAR(500),
    category VARCHAR(50) NOT NULL CHECK (category IN ('research', 'verification', 'contact_info', 'legal', 'other')),
    added_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- BATCH PROCESSING & DATA MANAGEMENT
-- ===================================

-- Batch import tracking (US-106)
CREATE TABLE import_batches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id VARCHAR(100) UNIQUE NOT NULL,
    file_name VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    state VARCHAR(2) NOT NULL,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    total_records INTEGER NOT NULL,
    processed_records INTEGER DEFAULT 0,
    valid_records INTEGER DEFAULT 0,
    error_records INTEGER DEFAULT 0,
    duplicate_records INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'uploading' CHECK (status IN ('uploading', 'processing', 'completed', 'error')),
    storage_type VARCHAR(20) DEFAULT 'regular' CHECK (storage_type IN ('regular', 'chunked')),
    processing_notes TEXT,
    error_details JSONB DEFAULT '[]',
    field_mappings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Individual batch records before processing into claims
CREATE TABLE batch_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID NOT NULL REFERENCES import_batches(id) ON DELETE CASCADE,
    claim_id UUID REFERENCES claims(id), -- Set after successful processing
    row_number INTEGER NOT NULL,
    raw_data JSONB NOT NULL,
    mapped_data JSONB DEFAULT '{}',
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'error', 'duplicate')),
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- ===================================
-- COMMUNICATION MANAGEMENT
-- ===================================

-- Email templates and communication (US-008, US-204)
CREATE TABLE email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body_text TEXT NOT NULL,
    body_html TEXT,
    state_code VARCHAR(2),
    template_type VARCHAR(50) NOT NULL CHECK (template_type IN ('initial_contact', 'follow_up', 'document_request', 'completion', 'other')),
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- SMS templates (US-009)
CREATE TABLE sms_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    state_code VARCHAR(2),
    template_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    compliance_approved BOOLEAN DEFAULT false,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document templates (US-010, US-204)
CREATE TABLE document_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    template_type VARCHAR(100) NOT NULL CHECK (template_type IN ('engagement_letter', 'power_of_attorney', 'state_form', 'contract', 'other')),
    state_code VARCHAR(2),
    content TEXT NOT NULL,
    file_format VARCHAR(20) DEFAULT 'pdf',
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Communication logs (US-007, US-008, US-009)
CREATE TABLE communication_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES users(id),
    communication_type VARCHAR(20) NOT NULL CHECK (communication_type IN ('call', 'email', 'sms')),
    direction VARCHAR(10) NOT NULL CHECK (direction IN ('outbound', 'inbound')),
    recipient_contact VARCHAR(500),
    subject VARCHAR(500),
    content TEXT,
    template_id UUID, -- References email_templates, sms_templates, etc.
    status VARCHAR(50) NOT NULL CHECK (status IN ('sent', 'delivered', 'read', 'failed', 'bounced')),
    external_id VARCHAR(255), -- ID from email/SMS service
    metadata JSONB DEFAULT '{}',
    sent_at TIMESTAMPTZ DEFAULT NOW(),
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ
);

-- ===================================
-- BUSINESS RULES & COMPLIANCE
-- ===================================

-- Business rules and validation (US-203, US-207)
CREATE TABLE business_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('validation', 'workflow', 'compliance', 'fee_cap', 'auto_assignment')),
    state_code VARCHAR(2),
    configuration JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 1,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Compliance and audit logs (US-207)
CREATE TABLE compliance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    claim_id UUID REFERENCES claims(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- PERFORMANCE & ANALYTICS
-- ===================================

-- Performance metrics tracking (US-101, US-107, US-210)
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES users(id),
    team_id UUID REFERENCES teams(id),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('count', 'percentage', 'amount', 'duration')),
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System logs for monitoring (US-205)
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_level VARCHAR(20) NOT NULL CHECK (log_level IN ('debug', 'info', 'warning', 'error', 'critical')),
    component VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES users(id),
    details JSONB DEFAULT '{}',
    execution_time_ms INTEGER,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- FINANCIAL MANAGEMENT
-- ===================================

-- Payment tracking and financial management (US-209)
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id),
    payment_type VARCHAR(50) NOT NULL CHECK (payment_type IN ('claimant_payout', 'commission', 'fee', 'refund')),
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processed', 'completed', 'failed', 'cancelled')),
    payment_method VARCHAR(50),
    external_transaction_id VARCHAR(255),
    recipient_info JSONB DEFAULT '{}',
    processed_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    scheduled_date DATE,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Invoice generation and tracking
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    claim_id UUID NOT NULL REFERENCES claims(id),
    amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    due_date DATE,
    paid_at TIMESTAMPTZ,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- INTEGRATIONS & EXTERNAL SERVICES
-- ===================================

-- External service integrations (US-208, US-022)
CREATE TABLE integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_name VARCHAR(100) NOT NULL,
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('email', 'sms', 'payment', 'document', 'crm', 'skip_trace', 'other')),
    configuration JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMPTZ,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- API request logs for external services
CREATE TABLE api_request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_id UUID REFERENCES integrations(id),
    request_method VARCHAR(10) NOT NULL,
    request_url VARCHAR(1000) NOT NULL,
    request_headers JSONB,
    request_body JSONB,
    response_status INTEGER,
    response_headers JSONB,
    response_body JSONB,
    execution_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- INDEXES FOR PERFORMANCE
-- ===================================

-- Claims indexes
CREATE INDEX idx_claims_assigned_agent ON claims(assigned_agent_id);
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_state ON claims(state);
CREATE INDEX idx_claims_priority ON claims(priority);
CREATE INDEX idx_claims_created_at ON claims(created_at);
CREATE INDEX idx_claims_next_followup ON claims(next_followup_date);
CREATE INDEX idx_claims_owner_name ON claims(owner_name);

-- Activities indexes
CREATE INDEX idx_activities_claim_id ON claim_activities(claim_id);
CREATE INDEX idx_activities_agent_id ON claim_activities(agent_id);
CREATE INDEX idx_activities_created_at ON claim_activities(created_at);
CREATE INDEX idx_activities_followup ON claim_activities(scheduled_followup_at);

-- Contacts indexes
CREATE INDEX idx_contacts_claim_id ON claim_contacts(claim_id);
CREATE INDEX idx_contacts_type_value ON claim_contacts(contact_type, contact_value);

-- Batch processing indexes
CREATE INDEX idx_batch_records_batch_id ON batch_records(batch_id);
CREATE INDEX idx_batch_records_status ON batch_records(status);

-- Performance metrics indexes
CREATE INDEX idx_performance_agent_id ON performance_metrics(agent_id);
CREATE INDEX idx_performance_period ON performance_metrics(period_start, period_end);

-- Communication logs indexes
CREATE INDEX idx_communication_claim_id ON communication_logs(claim_id);
CREATE INDEX idx_communication_agent_id ON communication_logs(agent_id);
CREATE INDEX idx_communication_sent_at ON communication_logs(sent_at);

-- Compliance logs indexes
CREATE INDEX idx_compliance_user_id ON compliance_logs(user_id);
CREATE INDEX idx_compliance_claim_id ON compliance_logs(claim_id);
CREATE INDEX idx_compliance_timestamp ON compliance_logs(timestamp);

-- System logs indexes
CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp);
CREATE INDEX idx_system_logs_component ON system_logs(component);
CREATE INDEX idx_system_logs_level ON system_logs(log_level);

-- ===================================
-- INITIAL DATA SETUP
-- ===================================

-- Insert default permissions
INSERT INTO permissions (role, resource, actions_allowed) VALUES
('admin', '*', ARRAY['*']),
('senior_agent', 'claims', ARRAY['view_all', 'update_all', 'assign', 'approve']),
('senior_agent', 'team', ARRAY['view', 'manage']),
('senior_agent', 'batch', ARRAY['import', 'review']),
('senior_agent', 'reports', ARRAY['generate', 'view_all']),
('junior_agent', 'claims', ARRAY['view_own', 'update_own']),
('junior_agent', 'claimants', ARRAY['view', 'contact']),
('compliance', 'compliance', ARRAY['view', 'audit', 'report']),
('finance', 'payments', ARRAY['view', 'process', 'approve']),
('finance', 'invoices', ARRAY['generate', 'send', 'track']);

-- Insert default business rules
INSERT INTO business_rules (rule_name, rule_type, state_code, configuration, created_by) 
SELECT 
    'Default Fee Cap - ' || state_code,
    'fee_cap',
    state_code,
    jsonb_build_object('max_percentage', 25, 'max_amount', 10000),
    (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
FROM (VALUES ('CA'), ('TX'), ('NY'), ('FL'), ('IL')) AS states(state_code)
WHERE EXISTS (SELECT 1 FROM users WHERE role = 'admin');

-- ===================================
-- VIEWS FOR COMMON QUERIES
-- ===================================

-- Active claims with agent information
CREATE VIEW v_active_claims AS
SELECT 
    c.*,
    u.first_name || ' ' || u.last_name AS agent_name,
    u.email AS agent_email,
    t.name AS team_name,
    (SELECT COUNT(*) FROM claim_activities ca WHERE ca.claim_id = c.id) AS activity_count,
    (SELECT MAX(ca.created_at) FROM claim_activities ca WHERE ca.claim_id = c.id) AS last_activity_at
FROM claims c
LEFT JOIN users u ON c.assigned_agent_id = u.id
LEFT JOIN teams t ON u.team_id = t.id
WHERE c.status NOT IN ('completed', 'cancelled');

-- Team performance summary
CREATE VIEW v_team_performance AS
SELECT 
    t.id AS team_id,
    t.name AS team_name,
    u.first_name || ' ' || u.last_name AS manager_name,
    COUNT(DISTINCT team_users.id) AS team_size,
    COUNT(DISTINCT c.id) AS total_claims,
    COUNT(DISTINCT CASE WHEN c.status = 'completed' THEN c.id END) AS completed_claims,
    COALESCE(SUM(CASE WHEN c.status = 'completed' THEN c.actual_recovery_amount END), 0) AS total_recovery,
    COALESCE(AVG(CASE WHEN c.status = 'completed' THEN c.actual_recovery_amount END), 0) AS avg_recovery_per_claim
FROM teams t
LEFT JOIN users u ON t.manager_id = u.id
LEFT JOIN users team_users ON team_users.team_id = t.id
LEFT JOIN claims c ON c.assigned_agent_id = team_users.id
WHERE t.is_active = true
GROUP BY t.id, t.name, u.first_name, u.last_name; 