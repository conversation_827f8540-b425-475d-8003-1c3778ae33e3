import React, { useState, useEffect } from 'react'
import { 
  Shield, 
  Users, 
  Building2, 
  Activity, 
  AlertTriangle, 
  TrendingUp,
  Server,
  Settings,
  Flag,
  FileText,
  BarChart3,
  Clock
} from 'lucide-react'
import { superUserService } from '../services/superUserService'
import type { PlatformStats, SystemHealth, SystemAlert } from '../services/superUserService'

export const SuperUserDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'organizations' | 'system' | 'features' | 'audit'>('overview')
  const [platformStats, setPlatformStats] = useState<PlatformStats[]>([])
  const [systemHealth, setSystemHealth] = useState<SystemHealth[]>([])
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const [stats, health, alerts] = await Promise.all([
        superUserService.getPlatformStats('day', 7),
        superUserService.getSystemHealth(),
        superUserService.getSystemAlerts('active')
      ])

      setPlatformStats(stats)
      setSystemHealth(health)
      setSystemAlerts(alerts)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const latestStats = platformStats[0]

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'organizations', label: 'Organizations', icon: Building2 },
    { id: 'system', label: 'System Health', icon: Server },
    { id: 'features', label: 'Feature Flags', icon: Flag },
    { id: 'audit', label: 'Audit Logs', icon: FileText }
  ]

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400'
      case 'degraded': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'down': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'maintenance': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'error': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'warning': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
      default: return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
            <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              SuperUser Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Platform administration and monitoring
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        {latestStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Organizations</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {latestStats.active_organizations}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    +{latestStats.new_organizations} new
                  </p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {latestStats.active_users}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    +{latestStats.new_users} new
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Health</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {latestStats.uptime_percentage}%
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {latestStats.avg_response_time_ms}ms avg
                  </p>
                </div>
                <Activity className="h-8 w-8 text-purple-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Alerts</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {systemAlerts.length}
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-400">
                    {systemAlerts.filter(a => a.severity === 'critical').length} critical
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-600 dark:text-red-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* System Health Overview */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              System Health
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {systemHealth.map((health) => (
                <div key={health.service_name} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">
                      {health.service_name}
                    </h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getHealthStatusColor(health.status)}`}>
                      {health.status}
                    </span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    {health.response_time_ms && (
                      <div>Response: {health.response_time_ms}ms</div>
                    )}
                    {health.cpu_usage && (
                      <div>CPU: {health.cpu_usage}%</div>
                    )}
                    {health.memory_usage && (
                      <div>Memory: {health.memory_usage}%</div>
                    )}
                    {health.error_count > 0 && (
                      <div className="text-red-600 dark:text-red-400">
                        {health.error_count} errors
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Active Alerts */}
          {systemAlerts.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Active Alerts
              </h3>
              <div className="space-y-3">
                {systemAlerts.slice(0, 5).map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                      alert.severity === 'critical' ? 'text-red-600' :
                      alert.severity === 'error' ? 'text-red-500' :
                      alert.severity === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {alert.title}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {alert.message}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span>{alert.source_service}</span>
                        <span>{new Date(alert.created_at).toLocaleString()}</span>
                      </div>
                    </div>
                    <button
                      onClick={() => superUserService.acknowledgeAlert(alert.id)}
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                    >
                      Acknowledge
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Platform Trends */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Platform Trends (Last 7 Days)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Organizations Growth
                </h4>
                <div className="space-y-1">
                  {platformStats.slice(0, 7).reverse().map((stat, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {new Date(stat.period_start).toLocaleDateString()}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        +{stat.new_organizations}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  User Growth
                </h4>
                <div className="space-y-1">
                  {platformStats.slice(0, 7).reverse().map((stat, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {new Date(stat.period_start).toLocaleDateString()}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        +{stat.new_users}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  System Performance
                </h4>
                <div className="space-y-1">
                  {platformStats.slice(0, 7).reverse().map((stat, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {new Date(stat.period_start).toLocaleDateString()}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {stat.uptime_percentage}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Other tab content would be implemented in separate components */}
      {activeTab === 'organizations' && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Organization Management
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Organization management interface will be implemented here
          </p>
        </div>
      )}

      {activeTab === 'system' && (
        <div className="text-center py-12">
          <Server className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            System Health Details
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Detailed system health monitoring will be implemented here
          </p>
        </div>
      )}

      {activeTab === 'features' && (
        <div className="text-center py-12">
          <Flag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Feature Flag Management
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Feature flag management interface will be implemented here
          </p>
        </div>
      )}

      {activeTab === 'audit' && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Audit Logs
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            SuperUser audit log viewer will be implemented here
          </p>
        </div>
      )}
    </div>
  )
}
