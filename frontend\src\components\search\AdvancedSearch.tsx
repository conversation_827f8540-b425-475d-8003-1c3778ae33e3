import React, { useState, useEffect, useRef } from 'react'
import { 
  Search, 
  Filter, 
  Save, 
  Star, 
  X, 
  Calendar, 
  DollarSign, 
  User, 
  Tag,
  Clock,
  ChevronDown
} from 'lucide-react'
import { supabase } from '../../lib/supabase'

interface SearchFilter {
  field: string
  operator: 'equals' | 'contains' | 'starts_with' | 'greater_than' | 'less_than' | 'between' | 'in'
  value: any
  label?: string
}

interface SavedSearch {
  id: string
  name: string
  query: string
  filters: SearchFilter[]
  created_at: string
  is_favorite: boolean
  user_id: string
}

interface SearchResult {
  id: string
  type: 'claim' | 'claimant' | 'document' | 'user'
  title: string
  subtitle?: string
  description?: string
  metadata?: Record<string, any>
  url?: string
}

export const AdvancedSearch: React.FC = () => {
  const [query, setQuery] = useState('')
  const [filters, setFilters] = useState<SearchFilter[]>([])
  const [results, setResults] = useState<SearchResult[]>([])
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [showSavedSearches, setShowSavedSearches] = useState(false)
  const [saveSearchName, setSaveSearchName] = useState('')
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)

  const searchFields = [
    { key: 'claim_number', label: 'Claim Number', type: 'text', icon: Tag },
    { key: 'claimant_name', label: 'Claimant Name', type: 'text', icon: User },
    { key: 'amount', label: 'Amount', type: 'number', icon: DollarSign },
    { key: 'status', label: 'Status', type: 'select', icon: Tag, options: ['active', 'pending', 'completed', 'cancelled'] },
    { key: 'assigned_agent', label: 'Assigned Agent', type: 'text', icon: User },
    { key: 'created_at', label: 'Created Date', type: 'date', icon: Calendar },
    { key: 'updated_at', label: 'Updated Date', type: 'date', icon: Calendar }
  ]

  useEffect(() => {
    loadSavedSearches()
  }, [])

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (query.trim() || filters.length > 0) {
        performSearch()
      } else {
        setResults([])
      }
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query, filters])

  const loadSavedSearches = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // In a real implementation, load from database
      const mockSavedSearches: SavedSearch[] = [
        {
          id: '1',
          name: 'High Value Claims',
          query: '',
          filters: [{ field: 'amount', operator: 'greater_than', value: 10000 }],
          created_at: '2024-01-01T00:00:00Z',
          is_favorite: true,
          user_id: user.id
        },
        {
          id: '2',
          name: 'Pending Claims',
          query: '',
          filters: [{ field: 'status', operator: 'equals', value: 'pending' }],
          created_at: '2024-01-02T00:00:00Z',
          is_favorite: false,
          user_id: user.id
        }
      ]
      setSavedSearches(mockSavedSearches)
    } catch (error) {
      console.error('Error loading saved searches:', error)
    }
  }

  const performSearch = async () => {
    setIsLoading(true)
    try {
      // Mock search results - in real implementation, this would query the database
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'claim',
          title: 'Claim #12345',
          subtitle: 'John Doe - $15,000',
          description: 'Active claim for unclaimed property recovery',
          metadata: { status: 'active', agent: 'Jane Smith' },
          url: '/claims/12345'
        },
        {
          id: '2',
          type: 'claimant',
          title: 'John Doe',
          subtitle: '<EMAIL>',
          description: '3 active claims, $25,000 total value',
          metadata: { phone: '(*************', city: 'New York' },
          url: '/claimants/john-doe'
        },
        {
          id: '3',
          type: 'document',
          title: 'Power of Attorney - Claim #12345',
          subtitle: 'Uploaded 2 days ago',
          description: 'Legal document for claim processing',
          metadata: { type: 'legal', size: '2.4 MB' },
          url: '/documents/poa-12345'
        }
      ]

      // Filter results based on query and filters
      let filteredResults = mockResults
      if (query.trim()) {
        filteredResults = filteredResults.filter(result =>
          result.title.toLowerCase().includes(query.toLowerCase()) ||
          result.subtitle?.toLowerCase().includes(query.toLowerCase()) ||
          result.description?.toLowerCase().includes(query.toLowerCase())
        )
      }

      setResults(filteredResults)
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addFilter = (field: string) => {
    const fieldConfig = searchFields.find(f => f.key === field)
    if (!fieldConfig) return

    const newFilter: SearchFilter = {
      field,
      operator: fieldConfig.type === 'number' ? 'greater_than' : 'contains',
      value: '',
      label: fieldConfig.label
    }

    setFilters([...filters, newFilter])
  }

  const updateFilter = (index: number, updates: Partial<SearchFilter>) => {
    const newFilters = [...filters]
    newFilters[index] = { ...newFilters[index], ...updates }
    setFilters(newFilters)
  }

  const removeFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index))
  }

  const saveSearch = async () => {
    if (!saveSearchName.trim()) return

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const newSavedSearch: SavedSearch = {
        id: Date.now().toString(),
        name: saveSearchName,
        query,
        filters,
        created_at: new Date().toISOString(),
        is_favorite: false,
        user_id: user.id
      }

      setSavedSearches([...savedSearches, newSavedSearch])
      setSaveSearchName('')
      setShowSaveDialog(false)

      // In real implementation, save to database
    } catch (error) {
      console.error('Error saving search:', error)
    }
  }

  const loadSavedSearch = (savedSearch: SavedSearch) => {
    setQuery(savedSearch.query)
    setFilters(savedSearch.filters)
    setShowSavedSearches(false)
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'claim': return Tag
      case 'claimant': return User
      case 'document': return Tag
      case 'user': return User
      default: return Tag
    }
  }

  const getResultTypeColor = (type: string) => {
    switch (type) {
      case 'claim': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'claimant': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'document': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
      case 'user': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Search Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Advanced Search
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Search across claims, claimants, documents, and more
        </p>
      </div>

      {/* Search Input */}
      <div className="relative mb-4">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={searchInputRef}
          type="text"
          placeholder="Search for anything..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-1 rounded ${
              showFilters || filters.length > 0
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
            }`}
          >
            <Filter className="h-5 w-5" />
          </button>
          <button
            onClick={() => setShowSavedSearches(!showSavedSearches)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
          >
            <Star className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {(query.trim() || filters.length > 0) && (
            <button
              onClick={() => setShowSaveDialog(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Search
            </button>
          )}
        </div>

        <div className="text-sm text-gray-500 dark:text-gray-400">
          {results.length > 0 && `${results.length} results found`}
          {isLoading && 'Searching...'}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Filters</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Active Filters */}
          {filters.length > 0 && (
            <div className="space-y-3 mb-4">
              {filters.map((filter, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {filter.label}
                  </span>
                  <select
                    value={filter.operator}
                    onChange={(e) => updateFilter(index, { operator: e.target.value as any })}
                    className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                  >
                    <option value="equals">equals</option>
                    <option value="contains">contains</option>
                    <option value="starts_with">starts with</option>
                    <option value="greater_than">greater than</option>
                    <option value="less_than">less than</option>
                  </select>
                  <input
                    type="text"
                    value={filter.value}
                    onChange={(e) => updateFilter(index, { value: e.target.value })}
                    className="flex-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                    placeholder="Enter value..."
                  />
                  <button
                    onClick={() => removeFilter(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Add Filter */}
          <div className="flex flex-wrap gap-2">
            {searchFields.map((field) => {
              const Icon = field.icon
              return (
                <button
                  key={field.key}
                  onClick={() => addFilter(field.key)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {field.label}
                </button>
              )
            })}
          </div>
        </div>
      )}

      {/* Saved Searches Panel */}
      {showSavedSearches && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Saved Searches</h3>
            <button
              onClick={() => setShowSavedSearches(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-2">
            {savedSearches.map((savedSearch) => (
              <div
                key={savedSearch.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                onClick={() => loadSavedSearch(savedSearch)}
              >
                <div className="flex items-center space-x-3">
                  <Star className={`h-4 w-4 ${
                    savedSearch.is_favorite 
                      ? 'text-yellow-500 fill-current' 
                      : 'text-gray-400'
                  }`} />
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {savedSearch.name}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {savedSearch.filters.length} filters • {new Date(savedSearch.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <Clock className="h-4 w-4 text-gray-400" />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search Results */}
      <div className="space-y-4">
        {isLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Searching...</p>
          </div>
        )}

        {!isLoading && results.length === 0 && (query.trim() || filters.length > 0) && (
          <div className="text-center py-8">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No results found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search terms or filters
            </p>
          </div>
        )}

        {results.map((result) => {
          const Icon = getResultIcon(result.type)
          return (
            <div
              key={result.id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => {
                if (result.url) {
                  // Navigate to result URL
                  console.log('Navigate to:', result.url)
                }
              }}
            >
              <div className="flex items-start space-x-3">
                <Icon className="h-5 w-5 text-gray-400 mt-1" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {result.title}
                    </h3>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getResultTypeColor(result.type)}`}>
                      {result.type}
                    </span>
                  </div>
                  {result.subtitle && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      {result.subtitle}
                    </p>
                  )}
                  {result.description && (
                    <p className="text-sm text-gray-500 dark:text-gray-500">
                      {result.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Save Search
            </h3>
            <input
              type="text"
              placeholder="Enter search name..."
              value={saveSearchName}
              onChange={(e) => setSaveSearchName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 mb-4"
              autoFocus
            />
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSaveDialog(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={saveSearch}
                disabled={!saveSearchName.trim()}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
