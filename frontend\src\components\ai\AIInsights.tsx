import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb, 
  Target, 
  Clock,
  DollarSign,
  Users,
  Zap,
  ChevronRight,
  RefreshCw,
  Sparkles
} from 'lucide-react'

interface AIInsight {
  id: string
  type: 'prediction' | 'recommendation' | 'alert' | 'opportunity'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  category: 'performance' | 'financial' | 'operational' | 'risk'
  data: {
    current?: number
    predicted?: number
    timeframe?: string
    metrics?: Record<string, number>
  }
  actions?: Array<{
    label: string
    type: 'primary' | 'secondary'
    action: () => void
  }>
  created_at: string
}

interface PredictiveModel {
  name: string
  accuracy: number
  lastTrained: string
  predictions: Array<{
    metric: string
    current: number
    predicted: number
    timeframe: string
    confidence: number
  }>
}

export const AIInsights: React.FC = () => {
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [models, setModels] = useState<PredictiveModel[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  useEffect(() => {
    loadAIInsights()
    loadPredictiveModels()
  }, [])

  const loadAIInsights = async () => {
    setIsLoading(true)
    try {
      // Mock AI insights - in real implementation, this would come from your AI service
      const mockInsights: AIInsight[] = [
        {
          id: '1',
          type: 'prediction',
          title: 'Recovery Rate Forecast',
          description: 'Based on current trends, your recovery rate is predicted to increase by 8.5% next quarter.',
          confidence: 87,
          impact: 'high',
          category: 'performance',
          data: {
            current: 72.6,
            predicted: 78.8,
            timeframe: 'Next Quarter',
            metrics: { trend: 8.5, confidence: 87 }
          },
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          type: 'recommendation',
          title: 'Optimize Agent Workload',
          description: 'Sarah Johnson is handling 23% more cases than optimal. Redistributing 5 cases could improve overall team efficiency by 12%.',
          confidence: 92,
          impact: 'medium',
          category: 'operational',
          data: {
            current: 67,
            predicted: 62,
            metrics: { efficiency_gain: 12, cases_to_redistribute: 5 }
          },
          actions: [
            {
              label: 'Redistribute Cases',
              type: 'primary',
              action: () => console.log('Redistributing cases...')
            },
            {
              label: 'View Details',
              type: 'secondary',
              action: () => console.log('Viewing details...')
            }
          ],
          created_at: new Date().toISOString()
        },
        {
          id: '3',
          type: 'alert',
          title: 'Potential Bottleneck Detected',
          description: 'Document verification process is taking 34% longer than usual. This may impact Q3 targets.',
          confidence: 78,
          impact: 'high',
          category: 'risk',
          data: {
            current: 4.2,
            predicted: 5.6,
            timeframe: 'Current',
            metrics: { delay_increase: 34, affected_claims: 23 }
          },
          actions: [
            {
              label: 'Investigate',
              type: 'primary',
              action: () => console.log('Investigating bottleneck...')
            }
          ],
          created_at: new Date().toISOString()
        },
        {
          id: '4',
          type: 'opportunity',
          title: 'Revenue Optimization',
          description: 'Implementing automated follow-ups for pending claims could increase recovery by $127K annually.',
          confidence: 84,
          impact: 'high',
          category: 'financial',
          data: {
            predicted: 127000,
            timeframe: 'Annual',
            metrics: { roi: 340, implementation_cost: 37000 }
          },
          actions: [
            {
              label: 'Implement Automation',
              type: 'primary',
              action: () => console.log('Implementing automation...')
            },
            {
              label: 'Cost Analysis',
              type: 'secondary',
              action: () => console.log('Viewing cost analysis...')
            }
          ],
          created_at: new Date().toISOString()
        }
      ]

      setInsights(mockInsights)
    } catch (error) {
      console.error('Error loading AI insights:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadPredictiveModels = async () => {
    try {
      const mockModels: PredictiveModel[] = [
        {
          name: 'Recovery Rate Predictor',
          accuracy: 87.3,
          lastTrained: '2024-01-15',
          predictions: [
            { metric: 'Recovery Rate', current: 72.6, predicted: 78.8, timeframe: 'Q3 2024', confidence: 87 },
            { metric: 'Avg Resolution Time', current: 45, predicted: 41, timeframe: 'Q3 2024', confidence: 82 }
          ]
        },
        {
          name: 'Risk Assessment Model',
          accuracy: 91.2,
          lastTrained: '2024-01-12',
          predictions: [
            { metric: 'Default Risk', current: 12.4, predicted: 9.8, timeframe: 'Next Month', confidence: 91 },
            { metric: 'Compliance Score', current: 94.2, predicted: 96.1, timeframe: 'Next Month', confidence: 88 }
          ]
        }
      ]

      setModels(mockModels)
    } catch (error) {
      console.error('Error loading predictive models:', error)
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'prediction': return TrendingUp
      case 'recommendation': return Lightbulb
      case 'alert': return AlertTriangle
      case 'opportunity': return Target
      default: return Brain
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'prediction': return 'blue'
      case 'recommendation': return 'green'
      case 'alert': return 'red'
      case 'opportunity': return 'purple'
      default: return 'gray'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const filteredInsights = selectedCategory === 'all' 
    ? insights 
    : insights.filter(insight => insight.category === selectedCategory)

  const categories = [
    { id: 'all', label: 'All Insights', icon: Brain },
    { id: 'performance', label: 'Performance', icon: TrendingUp },
    { id: 'financial', label: 'Financial', icon: DollarSign },
    { id: 'operational', label: 'Operational', icon: Users },
    { id: 'risk', label: 'Risk', icon: AlertTriangle }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              AI Insights
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Intelligent recommendations and predictive analytics
            </p>
          </div>
        </div>

        <button
          onClick={loadAIInsights}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Insights
        </button>
      </div>

      {/* Category Filter */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        {categories.map(category => {
          const Icon = category.icon
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{category.label}</span>
            </button>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* AI Insights */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
              Latest Insights
            </h3>

            <div className="space-y-4">
              {filteredInsights.map(insight => {
                const Icon = getInsightIcon(insight.type)
                const color = getInsightColor(insight.type)

                return (
                  <div key={insight.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-4">
                      <div className={`w-10 h-10 bg-${color}-100 dark:bg-${color}-900/20 rounded-lg flex items-center justify-center`}>
                        <Icon className={`h-5 w-5 text-${color}-600 dark:text-${color}-400`} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {insight.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getImpactColor(insight.impact)}`}>
                              {insight.impact} impact
                            </span>
                            <div className="flex items-center space-x-1">
                              <Sparkles className="h-3 w-3 text-yellow-500" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {insight.confidence}%
                              </span>
                            </div>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {insight.description}
                        </p>

                        {/* Data Visualization */}
                        {insight.data && (
                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                              {insight.data.current && (
                                <div>
                                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                    {insight.category === 'financial' 
                                      ? `$${insight.data.current.toLocaleString()}`
                                      : insight.data.current
                                    }
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">Current</div>
                                </div>
                              )}
                              {insight.data.predicted && (
                                <div>
                                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                                    {insight.category === 'financial' 
                                      ? `$${insight.data.predicted.toLocaleString()}`
                                      : insight.data.predicted
                                    }
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {insight.data.timeframe || 'Predicted'}
                                  </div>
                                </div>
                              )}
                              {insight.data.metrics && Object.entries(insight.data.metrics).map(([key, value]) => (
                                <div key={key}>
                                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                    {typeof value === 'number' && value > 1000 
                                      ? `$${value.toLocaleString()}`
                                      : `${value}${key.includes('rate') || key.includes('percentage') ? '%' : ''}`
                                    }
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Actions */}
                        {insight.actions && insight.actions.length > 0 && (
                          <div className="flex items-center space-x-3">
                            {insight.actions.map((action, index) => (
                              <button
                                key={index}
                                onClick={action.action}
                                className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                  action.type === 'primary'
                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                }`}
                              >
                                {action.label}
                                <ChevronRight className="h-4 w-4 ml-1" />
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Predictive Models */}
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Predictive Models
            </h3>

            <div className="space-y-4">
              {models.map((model, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {model.name}
                    </h4>
                    <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                      {model.accuracy}% accurate
                    </span>
                  </div>

                  <div className="space-y-2">
                    {model.predictions.map((prediction, pIndex) => (
                      <div key={pIndex} className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          {prediction.metric}
                        </span>
                        <div className="text-right">
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            {prediction.current} → {prediction.predicted}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {prediction.timeframe}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>Last trained: {new Date(model.lastTrained).toLocaleDateString()}</span>
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                        Retrain
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              AI-Powered Actions
            </h3>

            <div className="space-y-3">
              <button className="w-full flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg hover:from-purple-100 hover:to-blue-100 dark:hover:from-purple-900/30 dark:hover:to-blue-900/30 transition-colors">
                <div className="flex items-center space-x-3">
                  <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Auto-optimize Workflows
                  </span>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg hover:from-green-100 hover:to-blue-100 dark:hover:from-green-900/30 dark:hover:to-blue-900/30 transition-colors">
                <div className="flex items-center space-x-3">
                  <Target className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Generate Forecasts
                  </span>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg hover:from-orange-100 hover:to-red-100 dark:hover:from-orange-900/30 dark:hover:to-red-900/30 transition-colors">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Risk Assessment
                  </span>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
