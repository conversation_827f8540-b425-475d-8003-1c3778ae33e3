import React from 'react'
import { Download, X, Smartphone, Monitor, Wifi, WifiOff } from 'lucide-react'
import { useP<PERSON>, useServiceWorker, useOfflineSync } from '../../hooks/usePWA'

export const PWAInstallBanner: React.FC = () => {
  const { 
    isInstallable, 
    isInstalled, 
    showInstallBanner, 
    canShowInstallBanner,
    installApp, 
    dismissInstallBanner 
  } = usePWA()

  if (!isInstallable || isInstalled || !showInstallBanner || !canShowInstallBanner) {
    return null
  }

  const handleInstall = async () => {
    const success = await installApp()
    if (success) {
      console.log('App installed successfully')
    }
  }

  return (
    <div className="fixed bottom-20 lg:bottom-6 left-4 right-4 lg:left-auto lg:right-6 lg:w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
          <Download className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Install AssetHunterPro
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Get the full app experience with offline access and push notifications.
          </p>
          
          <div className="flex items-center space-x-2 mt-3">
            <button
              onClick={handleInstall}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Install
            </button>
            <button
              onClick={dismissInstallBanner}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              Later
            </button>
          </div>
        </div>
        
        <button
          onClick={dismissInstallBanner}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  )
}

export const PWAStatusIndicator: React.FC = () => {
  const { isOnline, isStandalone } = usePWA()
  const { hasOfflineActions, syncOfflineActions, isSyncing } = useOfflineSync()

  if (!isStandalone && isOnline) {
    return null // Don't show status in browser when online
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="flex items-center space-x-2">
        {/* Online/Offline Status */}
        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isOnline 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {isOnline ? (
            <>
              <Wifi className="h-3 w-3 mr-1" />
              Online
            </>
          ) : (
            <>
              <WifiOff className="h-3 w-3 mr-1" />
              Offline
            </>
          )}
        </div>

        {/* PWA Mode Indicator */}
        {isStandalone && (
          <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            <Smartphone className="h-3 w-3 mr-1" />
            App Mode
          </div>
        )}

        {/* Offline Actions Sync */}
        {hasOfflineActions && (
          <button
            onClick={syncOfflineActions}
            disabled={isSyncing}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/30 disabled:opacity-50"
          >
            {isSyncing ? 'Syncing...' : 'Sync Pending'}
          </button>
        )}
      </div>
    </div>
  )
}

export const ServiceWorkerUpdateBanner: React.FC = () => {
  const { hasUpdate, updateServiceWorker } = useServiceWorker()

  if (!hasUpdate) return null

  return (
    <div className="fixed top-4 left-4 right-4 lg:left-auto lg:right-4 lg:w-96 bg-blue-600 text-white rounded-lg shadow-lg p-4 z-50">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium">Update Available</h3>
          <p className="text-sm opacity-90 mt-1">
            A new version of AssetHunterPro is ready to install.
          </p>
        </div>
        <button
          onClick={updateServiceWorker}
          className="ml-4 inline-flex items-center px-3 py-2 border border-white/20 text-sm leading-4 font-medium rounded-md text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/50"
        >
          Update
        </button>
      </div>
    </div>
  )
}

// Mobile-specific features component
export const MobileFeatures: React.FC = () => {
  const { isStandalone } = usePWA()

  if (!isStandalone) return null

  return (
    <div className="lg:hidden">
      {/* Mobile-specific features can be added here */}
      {/* For example: */}
      {/* - Pull to refresh */}
      {/* - Swipe gestures */}
      {/* - Mobile-optimized forms */}
      {/* - Touch-friendly interactions */}
    </div>
  )
}

// Offline indicator for forms and actions
export const OfflineIndicator: React.FC<{
  children: React.ReactNode
  showWhenOffline?: boolean
}> = ({ children, showWhenOffline = true }) => {
  const { isOnline } = usePWA()

  if (isOnline || !showWhenOffline) {
    return <>{children}</>
  }

  return (
    <div className="relative">
      {children}
      <div className="absolute inset-0 bg-gray-500/50 rounded flex items-center justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-lg">
          <div className="flex items-center space-x-2 text-sm">
            <WifiOff className="h-4 w-4 text-red-500" />
            <span className="text-gray-700 dark:text-gray-300">
              Offline - Changes will sync when connected
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
