-- Activity Timeline and Audit Trail Schema
-- This schema provides comprehensive audit logging for compliance

-- Activity types enum for better data integrity
CREATE TYPE activity_type AS ENUM (
  'user_login',
  'user_logout', 
  'claim_created',
  'claim_updated',
  'claim_assigned',
  'claim_completed',
  'document_uploaded',
  'document_downloaded',
  'document_deleted',
  'email_sent',
  'payment_processed',
  'status_changed',
  'bulk_operation',
  'search_performed',
  'export_generated',
  'settings_changed',
  'user_created',
  'user_updated',
  'user_deactivated',
  'organization_updated',
  'api_access',
  'webhook_triggered',
  'data_import',
  'data_export',
  'compliance_check',
  'security_event'
);

-- Main activity log table
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Core activity information
    activity_type activity_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Actor information (who performed the action)
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    user_email VARCHAR(255), -- Stored for audit even if user is deleted
    user_role VARCHAR(50),
    user_ip_address INET,
    user_agent TEXT,
    
    -- Target information (what was affected)
    target_type VARCHAR(50), -- 'claim', 'user', 'document', 'organization', etc.
    target_id VARCHAR(255), -- ID of the affected entity
    target_name VARCHAR(255), -- Human-readable name of the target
    
    -- Organization context
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Change tracking
    old_values JSONB, -- Previous state before change
    new_values JSONB, -- New state after change
    changes_summary TEXT, -- Human-readable summary of changes
    
    -- Request context
    request_id VARCHAR(100), -- For correlating related activities
    session_id VARCHAR(100),
    api_key_id VARCHAR(100), -- If action was performed via API
    
    -- Metadata
    metadata JSONB DEFAULT '{}', -- Additional context-specific data
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    
    -- Compliance and retention
    retention_period INTERVAL DEFAULT INTERVAL '7 years',
    is_sensitive BOOLEAN DEFAULT false,
    compliance_tags TEXT[], -- For compliance categorization
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ GENERATED ALWAYS AS (created_at + retention_period) STORED
);

-- Indexes for performance
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_organization_id ON activity_logs(organization_id);
CREATE INDEX idx_activity_logs_activity_type ON activity_logs(activity_type);
CREATE INDEX idx_activity_logs_target ON activity_logs(target_type, target_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at DESC);
CREATE INDEX idx_activity_logs_severity ON activity_logs(severity);
CREATE INDEX idx_activity_logs_compliance ON activity_logs USING GIN(compliance_tags);
CREATE INDEX idx_activity_logs_metadata ON activity_logs USING GIN(metadata);

-- Composite indexes for common queries
CREATE INDEX idx_activity_logs_user_date ON activity_logs(user_id, created_at DESC);
CREATE INDEX idx_activity_logs_org_type_date ON activity_logs(organization_id, activity_type, created_at DESC);
CREATE INDEX idx_activity_logs_target_date ON activity_logs(target_type, target_id, created_at DESC);

-- Activity summaries for dashboard widgets
CREATE TABLE activity_summaries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Time period
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('hour', 'day', 'week', 'month')),
    
    -- Aggregated metrics
    total_activities INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    activity_breakdown JSONB DEFAULT '{}', -- Count by activity type
    severity_breakdown JSONB DEFAULT '{}', -- Count by severity
    top_users JSONB DEFAULT '[]', -- Most active users
    top_targets JSONB DEFAULT '[]', -- Most affected entities
    
    -- Compliance metrics
    compliance_events INTEGER DEFAULT 0,
    security_events INTEGER DEFAULT 0,
    data_access_events INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique summaries per period
    UNIQUE(organization_id, period_start, period_type)
);

-- Index for activity summaries
CREATE INDEX idx_activity_summaries_org_period ON activity_summaries(organization_id, period_start DESC);

-- Function to log activities
CREATE OR REPLACE FUNCTION log_activity(
    p_activity_type activity_type,
    p_title VARCHAR(255),
    p_description TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_target_type VARCHAR(50) DEFAULT NULL,
    p_target_id VARCHAR(255) DEFAULT NULL,
    p_target_name VARCHAR(255) DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}',
    p_severity VARCHAR(20) DEFAULT 'info',
    p_user_ip INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_request_id VARCHAR(100) DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_activity_id UUID;
    v_user_email VARCHAR(255);
    v_user_role VARCHAR(50);
    v_organization_id UUID;
    v_changes_summary TEXT;
BEGIN
    -- Get user details if user_id provided
    IF p_user_id IS NOT NULL THEN
        SELECT email, role, organization_id 
        INTO v_user_email, v_user_role, v_organization_id
        FROM users 
        WHERE id = p_user_id;
    END IF;
    
    -- Generate changes summary if old/new values provided
    IF p_old_values IS NOT NULL AND p_new_values IS NOT NULL THEN
        v_changes_summary := generate_changes_summary(p_old_values, p_new_values);
    END IF;
    
    -- Insert activity log
    INSERT INTO activity_logs (
        activity_type,
        title,
        description,
        user_id,
        user_email,
        user_role,
        user_ip_address,
        user_agent,
        target_type,
        target_id,
        target_name,
        organization_id,
        old_values,
        new_values,
        changes_summary,
        metadata,
        severity,
        request_id
    ) VALUES (
        p_activity_type,
        p_title,
        p_description,
        p_user_id,
        v_user_email,
        v_user_role,
        p_user_ip,
        p_user_agent,
        p_target_type,
        p_target_id,
        p_target_name,
        v_organization_id,
        p_old_values,
        p_new_values,
        v_changes_summary,
        p_metadata,
        p_severity,
        p_request_id
    ) RETURNING id INTO v_activity_id;
    
    RETURN v_activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate human-readable changes summary
CREATE OR REPLACE FUNCTION generate_changes_summary(
    old_values JSONB,
    new_values JSONB
) RETURNS TEXT AS $$
DECLARE
    changes TEXT[] := '{}';
    key TEXT;
    old_val TEXT;
    new_val TEXT;
BEGIN
    -- Compare each key in new_values with old_values
    FOR key IN SELECT jsonb_object_keys(new_values)
    LOOP
        old_val := old_values ->> key;
        new_val := new_values ->> key;
        
        IF old_val IS DISTINCT FROM new_val THEN
            changes := array_append(changes, 
                format('%s: "%s" → "%s"', key, 
                    COALESCE(old_val, 'null'), 
                    COALESCE(new_val, 'null')
                )
            );
        END IF;
    END LOOP;
    
    RETURN array_to_string(changes, ', ');
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired activity logs
CREATE OR REPLACE FUNCTION cleanup_expired_activities() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM activity_logs 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically log user table changes
CREATE OR REPLACE FUNCTION trigger_log_user_changes() RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM log_activity(
            'user_created',
            format('User created: %s', NEW.email),
            format('New user account created with role: %s', NEW.role),
            NEW.id,
            'user',
            NEW.id::TEXT,
            NEW.email,
            NULL,
            to_jsonb(NEW),
            '{"trigger": "auto"}'::jsonb
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM log_activity(
            'user_updated',
            format('User updated: %s', NEW.email),
            'User account information updated',
            NEW.id,
            'user',
            NEW.id::TEXT,
            NEW.email,
            to_jsonb(OLD),
            to_jsonb(NEW),
            '{"trigger": "auto"}'::jsonb
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM log_activity(
            'user_deactivated',
            format('User deactivated: %s', OLD.email),
            'User account deactivated',
            OLD.id,
            'user',
            OLD.id::TEXT,
            OLD.email,
            to_jsonb(OLD),
            NULL,
            '{"trigger": "auto"}'::jsonb
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic logging
CREATE TRIGGER trigger_users_activity_log
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION trigger_log_user_changes();

-- Grant permissions
GRANT SELECT, INSERT ON activity_logs TO authenticated;
GRANT SELECT ON activity_summaries TO authenticated;
GRANT EXECUTE ON FUNCTION log_activity TO authenticated;

-- Row Level Security
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_summaries ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their organization's activity logs" ON activity_logs
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM users WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert activity logs for their organization" ON activity_logs
    FOR INSERT TO authenticated
    WITH CHECK (
        organization_id IN (
            SELECT organization_id FROM users WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can view their organization's activity summaries" ON activity_summaries
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM users WHERE id = auth.uid()
        )
    );

-- Create a scheduled job to generate daily summaries (PostgreSQL with pg_cron)
-- This would typically be set up separately in production
-- SELECT cron.schedule('generate-daily-activity-summaries', '0 1 * * *', 'SELECT generate_daily_activity_summaries();');
