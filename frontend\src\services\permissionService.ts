import { supabase } from '../lib/supabase'

export interface Permission {
  permission_code: string
  permission_name: string
  category: string
  subcategory?: string
  access_level: 'read' | 'write' | 'admin' | 'owner'
  minimum_plan_level: number
  requires_feature?: string
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  is_active: boolean
}

export interface UserRole {
  role_code: string
  role_name: string
  description: string
  role_level: number
  parent_role_code?: string
  minimum_plan_level: number
  is_active: boolean
}

export interface SubscriptionPlan {
  plan_id: string
  name: string
  description: string
  monthly_price: number
  annual_price?: number
  max_users: number
  max_claims: number
  max_storage_gb: number
  max_api_calls_monthly: number
  features: Record<string, any>
  included_states: number
  is_active: boolean
}

export interface OrganizationSubscription {
  id: string
  organization_id: string
  plan_id: string
  status: 'trial' | 'active' | 'suspended' | 'cancelled' | 'expired'
  billing_cycle: 'monthly' | 'annual' | 'one-time'
  trial_end_date?: string
  subscription_end_date?: string
  current_users: number
  current_claims: number
  current_storage_gb: number
  active_state_licenses: string[]
}

export interface PermissionCheck {
  allowed: boolean
  reason?: string
  upgrade_required?: boolean
  required_plan?: string
  feature_required?: string
}

class PermissionService {
  private permissionsCache = new Map<string, Permission[]>()
  private rolesCache = new Map<string, UserRole>()
  private subscriptionCache = new Map<string, OrganizationSubscription>()

  /**
   * Check if user has a specific permission
   */
  async hasPermission(
    userId: string, 
    permissionCode: string,
    context?: Record<string, any>
  ): Promise<PermissionCheck> {
    try {
      const user = await this.getUser(userId)
      if (!user) {
        return { allowed: false, reason: 'User not found' }
      }

      const subscription = await this.getOrganizationSubscription(user.organization_id)
      if (!subscription) {
        return { allowed: false, reason: 'No active subscription' }
      }

      // Check if subscription is active
      if (subscription.status !== 'active' && subscription.status !== 'trial') {
        return { allowed: false, reason: 'Subscription not active' }
      }

      // Get permission details
      const permission = await this.getPermission(permissionCode)
      if (!permission || !permission.is_active) {
        return { allowed: false, reason: 'Permission not found or inactive' }
      }

      // Check plan level requirement
      const plan = await this.getSubscriptionPlan(subscription.plan_id)
      if (!plan) {
        return { allowed: false, reason: 'Subscription plan not found' }
      }

      const planLevel = this.getPlanLevel(subscription.plan_id)
      if (planLevel < permission.minimum_plan_level) {
        return {
          allowed: false,
          reason: 'Plan upgrade required',
          upgrade_required: true,
          required_plan: this.getMinimumPlanForLevel(permission.minimum_plan_level)
        }
      }

      // Check feature requirement
      if (permission.requires_feature) {
        const hasFeature = plan.features[permission.requires_feature]
        if (!hasFeature) {
          return {
            allowed: false,
            reason: 'Feature not included in plan',
            upgrade_required: true,
            feature_required: permission.requires_feature
          }
        }
      }

      // Check role-based permissions
      const userPermissions = await this.getUserPermissions(userId)
      const hasRolePermission = userPermissions.some(p => p.permission_code === permissionCode)
      
      if (!hasRolePermission) {
        // Check for user-specific overrides
        const hasOverride = await this.hasPermissionOverride(userId, permissionCode)
        if (!hasOverride) {
          return { allowed: false, reason: 'Permission not granted to user role' }
        }
      }

      // Check context-specific conditions (e.g., own vs team vs all)
      if (context) {
        const contextCheck = await this.checkContextualPermissions(
          userId, 
          permissionCode, 
          context
        )
        if (!contextCheck.allowed) {
          return contextCheck
        }
      }

      return { allowed: true }
    } catch (error) {
      console.error('Error checking permission:', error)
      return { allowed: false, reason: 'Permission check failed' }
    }
  }

  /**
   * Get all permissions for a user
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    try {
      const cacheKey = `user_permissions_${userId}`
      if (this.permissionsCache.has(cacheKey)) {
        return this.permissionsCache.get(cacheKey)!
      }

      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single()

      if (!user) return []

      // Get role permissions
      const { data: rolePermissions } = await supabase
        .from('role_permissions')
        .select(`
          permission_code,
          permissions!inner(*)
        `)
        .eq('role_code', user.role)

      // Get user-specific overrides
      const { data: overrides } = await supabase
        .from('user_permission_overrides')
        .select(`
          permission_code,
          override_type,
          permissions!inner(*)
        `)
        .eq('user_id', userId)
        .or('expires_at.is.null,expires_at.gt.now()')

      let permissions: Permission[] = []

      // Add role permissions
      if (rolePermissions) {
        permissions = rolePermissions.map(rp => rp.permissions)
      }

      // Apply user overrides
      if (overrides) {
        overrides.forEach(override => {
          if (override.override_type === 'grant') {
            // Add permission if not already present
            if (!permissions.some(p => p.permission_code === override.permission_code)) {
              permissions.push(override.permissions)
            }
          } else if (override.override_type === 'deny') {
            // Remove permission
            permissions = permissions.filter(p => p.permission_code !== override.permission_code)
          }
        })
      }

      this.permissionsCache.set(cacheKey, permissions)
      return permissions
    } catch (error) {
      console.error('Error getting user permissions:', error)
      return []
    }
  }

  /**
   * Check multiple permissions at once
   */
  async hasAllPermissions(
    userId: string, 
    permissionCodes: string[],
    context?: Record<string, any>
  ): Promise<PermissionCheck> {
    for (const permissionCode of permissionCodes) {
      const check = await this.hasPermission(userId, permissionCode, context)
      if (!check.allowed) {
        return check
      }
    }
    return { allowed: true }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(
    userId: string, 
    permissionCodes: string[],
    context?: Record<string, any>
  ): Promise<PermissionCheck> {
    for (const permissionCode of permissionCodes) {
      const check = await this.hasPermission(userId, permissionCode, context)
      if (check.allowed) {
        return check
      }
    }
    return { allowed: false, reason: 'None of the required permissions granted' }
  }

  /**
   * Get organization subscription details
   */
  async getOrganizationSubscription(organizationId: string): Promise<OrganizationSubscription | null> {
    try {
      const cacheKey = `subscription_${organizationId}`
      if (this.subscriptionCache.has(cacheKey)) {
        return this.subscriptionCache.get(cacheKey)!
      }

      const { data, error } = await supabase
        .from('organization_subscriptions')
        .select('*')
        .eq('organization_id', organizationId)
        .single()

      if (error || !data) return null

      this.subscriptionCache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Error getting organization subscription:', error)
      return null
    }
  }

  /**
   * Get subscription plan details
   */
  async getSubscriptionPlan(planId: string): Promise<SubscriptionPlan | null> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('plan_id', planId)
        .single()

      if (error || !data) return null
      return data
    } catch (error) {
      console.error('Error getting subscription plan:', error)
      return null
    }
  }

  /**
   * Check usage limits
   */
  async checkUsageLimit(
    organizationId: string,
    limitType: 'users' | 'claims' | 'storage' | 'api_calls',
    requestedAmount: number = 1
  ): Promise<PermissionCheck> {
    try {
      const subscription = await this.getOrganizationSubscription(organizationId)
      if (!subscription) {
        return { allowed: false, reason: 'No subscription found' }
      }

      const plan = await this.getSubscriptionPlan(subscription.plan_id)
      if (!plan) {
        return { allowed: false, reason: 'Plan not found' }
      }

      let currentUsage: number
      let limit: number

      switch (limitType) {
        case 'users':
          currentUsage = subscription.current_users
          limit = plan.max_users
          break
        case 'claims':
          currentUsage = subscription.current_claims
          limit = plan.max_claims
          break
        case 'storage':
          currentUsage = subscription.current_storage_gb
          limit = plan.max_storage_gb
          break
        case 'api_calls':
          currentUsage = subscription.current_api_calls_monthly
          limit = plan.max_api_calls_monthly
          break
        default:
          return { allowed: false, reason: 'Invalid limit type' }
      }

      // -1 means unlimited
      if (limit === -1) {
        return { allowed: true }
      }

      if (currentUsage + requestedAmount > limit) {
        return {
          allowed: false,
          reason: `${limitType} limit exceeded`,
          upgrade_required: true
        }
      }

      return { allowed: true }
    } catch (error) {
      console.error('Error checking usage limit:', error)
      return { allowed: false, reason: 'Usage check failed' }
    }
  }

  /**
   * Grant permission override to user
   */
  async grantPermissionOverride(
    userId: string,
    permissionCode: string,
    grantedBy: string,
    reason: string,
    expiresAt?: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_permission_overrides')
        .upsert({
          user_id: userId,
          permission_code: permissionCode,
          override_type: 'grant',
          granted_by: grantedBy,
          reason,
          expires_at: expiresAt
        })

      if (error) throw error

      // Clear cache
      this.permissionsCache.delete(`user_permissions_${userId}`)
      return true
    } catch (error) {
      console.error('Error granting permission override:', error)
      return false
    }
  }

  /**
   * Revoke permission override
   */
  async revokePermissionOverride(
    userId: string,
    permissionCode: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_permission_overrides')
        .delete()
        .eq('user_id', userId)
        .eq('permission_code', permissionCode)

      if (error) throw error

      // Clear cache
      this.permissionsCache.delete(`user_permissions_${userId}`)
      return true
    } catch (error) {
      console.error('Error revoking permission override:', error)
      return false
    }
  }

  // Private helper methods
  private async getUser(userId: string) {
    const { data } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    return data
  }

  private async getPermission(permissionCode: string): Promise<Permission | null> {
    const { data } = await supabase
      .from('permissions')
      .select('*')
      .eq('permission_code', permissionCode)
      .single()
    return data
  }

  private async hasPermissionOverride(userId: string, permissionCode: string): Promise<boolean> {
    const { data } = await supabase
      .from('user_permission_overrides')
      .select('override_type')
      .eq('user_id', userId)
      .eq('permission_code', permissionCode)
      .eq('override_type', 'grant')
      .or('expires_at.is.null,expires_at.gt.now()')
      .single()

    return !!data
  }

  private async checkContextualPermissions(
    userId: string,
    permissionCode: string,
    context: Record<string, any>
  ): Promise<PermissionCheck> {
    // Implement context-specific logic here
    // For example, checking if user can only view their own claims vs team claims
    
    if (permissionCode.includes('view_own') && context.ownerId !== userId) {
      return { allowed: false, reason: 'Can only view own records' }
    }

    if (permissionCode.includes('view_team') && context.teamId) {
      // Check if user is part of the team
      const { data } = await supabase
        .from('users')
        .select('team_id')
        .eq('id', userId)
        .single()

      if (data?.team_id !== context.teamId) {
        return { allowed: false, reason: 'Not a member of this team' }
      }
    }

    return { allowed: true }
  }

  private getPlanLevel(planId: string): number {
    const planLevels: Record<string, number> = {
      'bronze': 1,
      'silver': 2,
      'gold': 3,
      'topaz': 4,
      'ruby': 5,
      'diamond': 6,
      'enterprise': 7
    }
    return planLevels[planId] || 0
  }

  private getMinimumPlanForLevel(level: number): string {
    const levelPlans: Record<number, string> = {
      1: 'bronze',
      2: 'silver',
      3: 'gold',
      4: 'topaz',
      5: 'ruby',
      6: 'diamond',
      7: 'enterprise'
    }
    return levelPlans[level] || 'enterprise'
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.permissionsCache.clear()
    this.rolesCache.clear()
    this.subscriptionCache.clear()
  }
}

export const permissionService = new PermissionService()
