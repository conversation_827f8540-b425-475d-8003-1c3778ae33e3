import React, { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { Plus, Settings, X, Move, BarChart3, Users, DollarSign, Clock } from 'lucide-react'

interface DashboardWidget {
  id: string
  type: 'chart' | 'metric' | 'list' | 'progress'
  title: string
  size: 'small' | 'medium' | 'large'
  position: { x: number; y: number }
  config: Record<string, any>
  data?: any
}

interface DashboardLayout {
  id: string
  name: string
  widgets: DashboardWidget[]
  user_id: string
  is_default: boolean
}

export const CustomizableDashboard: React.FC = () => {
  const [layout, setLayout] = useState<DashboardLayout | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [showWidgetPicker, setShowWidgetPicker] = useState(false)

  const availableWidgets = [
    {
      type: 'metric',
      title: 'Total Claims',
      icon: BarChart3,
      description: 'Total number of active claims',
      defaultSize: 'small'
    },
    {
      type: 'metric',
      title: 'Recovery Amount',
      icon: DollarSign,
      description: 'Total amount recovered this month',
      defaultSize: 'small'
    },
    {
      type: 'chart',
      title: 'Claims by Status',
      icon: BarChart3,
      description: 'Pie chart showing claim status distribution',
      defaultSize: 'medium'
    },
    {
      type: 'list',
      title: 'Recent Activities',
      icon: Clock,
      description: 'Latest claim activities and updates',
      defaultSize: 'large'
    },
    {
      type: 'chart',
      title: 'Monthly Performance',
      icon: BarChart3,
      description: 'Line chart showing monthly recovery trends',
      defaultSize: 'large'
    },
    {
      type: 'list',
      title: 'Top Performers',
      icon: Users,
      description: 'Agents with highest recovery rates',
      defaultSize: 'medium'
    }
  ]

  useEffect(() => {
    loadDashboardLayout()
  }, [])

  const loadDashboardLayout = async () => {
    // In a real implementation, this would load from the database
    const defaultLayout: DashboardLayout = {
      id: 'default',
      name: 'Default Dashboard',
      user_id: 'current-user',
      is_default: true,
      widgets: [
        {
          id: 'widget-1',
          type: 'metric',
          title: 'Total Claims',
          size: 'small',
          position: { x: 0, y: 0 },
          config: { metric: 'total_claims' },
          data: { value: 1247, change: '+12%' }
        },
        {
          id: 'widget-2',
          type: 'metric',
          title: 'Recovery Amount',
          size: 'small',
          position: { x: 1, y: 0 },
          config: { metric: 'recovery_amount' },
          data: { value: '$2.4M', change: '+8%' }
        },
        {
          id: 'widget-3',
          type: 'chart',
          title: 'Claims by Status',
          size: 'medium',
          position: { x: 0, y: 1 },
          config: { chart_type: 'pie' },
          data: {
            labels: ['Active', 'Pending', 'Completed', 'Cancelled'],
            values: [45, 23, 28, 4]
          }
        }
      ]
    }
    setLayout(defaultLayout)
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination || !layout) return

    const newWidgets = Array.from(layout.widgets)
    const [reorderedWidget] = newWidgets.splice(result.source.index, 1)
    newWidgets.splice(result.destination.index, 0, reorderedWidget)

    setLayout({
      ...layout,
      widgets: newWidgets
    })
  }

  const addWidget = (widgetType: any) => {
    if (!layout) return

    const newWidget: DashboardWidget = {
      id: `widget-${Date.now()}`,
      type: widgetType.type,
      title: widgetType.title,
      size: widgetType.defaultSize,
      position: { x: 0, y: layout.widgets.length },
      config: {},
      data: generateMockData(widgetType.type)
    }

    setLayout({
      ...layout,
      widgets: [...layout.widgets, newWidget]
    })
    setShowWidgetPicker(false)
  }

  const removeWidget = (widgetId: string) => {
    if (!layout) return

    setLayout({
      ...layout,
      widgets: layout.widgets.filter(w => w.id !== widgetId)
    })
  }

  const generateMockData = (type: string) => {
    switch (type) {
      case 'metric':
        return { value: Math.floor(Math.random() * 1000), change: '+5%' }
      case 'chart':
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
          values: Array.from({ length: 5 }, () => Math.floor(Math.random() * 100))
        }
      case 'list':
        return {
          items: [
            'New claim assigned to John Doe',
            'Payment received for claim #1234',
            'Document uploaded for claim #5678'
          ]
        }
      default:
        return {}
    }
  }

  const renderWidget = (widget: DashboardWidget) => {
    const sizeClasses = {
      small: 'col-span-1 row-span-1',
      medium: 'col-span-2 row-span-1',
      large: 'col-span-3 row-span-2'
    }

    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 ${sizeClasses[widget.size]}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {widget.title}
          </h3>
          {isEditing && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => removeWidget(widget.id)}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </button>
              <Move className="h-4 w-4 text-gray-400 cursor-move" />
            </div>
          )}
        </div>

        {/* Widget Content */}
        {widget.type === 'metric' && (
          <div>
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {widget.data?.value}
            </div>
            <div className="text-sm text-green-600">
              {widget.data?.change}
            </div>
          </div>
        )}

        {widget.type === 'chart' && (
          <div className="h-32 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
            <BarChart3 className="h-8 w-8 text-gray-400" />
            <span className="ml-2 text-gray-500">Chart Placeholder</span>
          </div>
        )}

        {widget.type === 'list' && (
          <div className="space-y-2">
            {widget.data?.items?.map((item: string, index: number) => (
              <div key={index} className="text-sm text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                {item}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  if (!layout) {
    return <div className="flex items-center justify-center h-64">Loading dashboard...</div>
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {layout.name}
        </h1>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowWidgetPicker(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Widget
          </button>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md ${
              isEditing
                ? 'border-blue-300 text-blue-700 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <Settings className="h-4 w-4 mr-2" />
            {isEditing ? 'Done Editing' : 'Edit Layout'}
          </button>
        </div>
      </div>

      {/* Dashboard Grid */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="dashboard" direction="horizontal">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="grid grid-cols-4 gap-4 auto-rows-min"
            >
              {layout.widgets.map((widget, index) => (
                <Draggable
                  key={widget.id}
                  draggableId={widget.id}
                  index={index}
                  isDragDisabled={!isEditing}
                >
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      {renderWidget(widget)}
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Widget Picker Modal */}
      {showWidgetPicker && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Add Widget
              </h3>
              <button
                onClick={() => setShowWidgetPicker(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="space-y-3">
              {availableWidgets.map((widget, index) => {
                const Icon = widget.icon
                return (
                  <button
                    key={index}
                    onClick={() => addWidget(widget)}
                    className="w-full text-left p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {widget.title}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {widget.description}
                        </div>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
