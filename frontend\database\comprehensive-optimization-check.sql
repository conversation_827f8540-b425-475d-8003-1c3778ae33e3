-- ===================================================================
-- COMPREHENSIVE DATABASE OPTIMIZATION & HEALTH CHECK
-- AssetHunterPro - Production Readiness Assessment
-- ===================================================================
-- Run this script in your Supabase SQL Editor for detailed analysis
-- ===================================================================

-- Enable timing for performance measurement
\timing on

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 STARTING COMPREHENSIVE DATABASE OPTIMIZATION CHECK';
    RAISE NOTICE '====================================================';
    RAISE NOTICE 'Timestamp: %', NOW();
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 1. DATABASE CONFIGURATION ANALYSIS
-- ===================================================================

DO $$
DECLARE
    db_version TEXT;
    db_size TEXT;
    connection_count INTEGER;
    max_connections INTEGER;
BEGIN
    RAISE NOTICE '⚙️  DATABASE CONFIGURATION ANALYSIS';
    RAISE NOTICE '===================================';
    
    -- Get database version
    SELECT version() INTO db_version;
    RAISE NOTICE '📊 Database Version: %', SPLIT_PART(db_version, ' ', 2);
    
    -- Get database size
    SELECT pg_size_pretty(pg_database_size(current_database())) INTO db_size;
    RAISE NOTICE '💾 Database Size: %', db_size;
    
    -- Get connection statistics
    SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active' INTO connection_count;
    SELECT setting::INTEGER FROM pg_settings WHERE name = 'max_connections' INTO max_connections;
    RAISE NOTICE '🔌 Active Connections: % / %', connection_count, max_connections;
    
    IF connection_count::FLOAT / max_connections > 0.8 THEN
        RAISE WARNING '⚠️  High connection usage: % / %', connection_count, max_connections;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 2. TABLE SIZE AND STATISTICS ANALYSIS
-- ===================================================================

DO $$
DECLARE
    table_info RECORD;
    total_size BIGINT := 0;
    large_table_count INTEGER := 0;
BEGIN
    RAISE NOTICE '📊 TABLE SIZE AND STATISTICS ANALYSIS';
    RAISE NOTICE '====================================';
    
    -- Analyze table sizes
    FOR table_info IN
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
            n_tup_ins + n_tup_upd + n_tup_del as total_operations,
            n_live_tup as live_tuples,
            n_dead_tup as dead_tuples
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
    LOOP
        total_size := total_size + table_info.size_bytes;
        
        RAISE NOTICE '📋 Table: % - Size: % (% live, % dead tuples)', 
            table_info.tablename, 
            table_info.size,
            table_info.live_tuples,
            table_info.dead_tuples;
        
        -- Check for tables that might need attention
        IF table_info.size_bytes > 100 * 1024 * 1024 THEN -- > 100MB
            large_table_count := large_table_count + 1;
            RAISE NOTICE '💡 Large table detected: % (%)', table_info.tablename, table_info.size;
        END IF;
        
        -- Check for high dead tuple ratio
        IF table_info.live_tuples > 0 AND 
           table_info.dead_tuples::FLOAT / table_info.live_tuples > 0.2 THEN
            RAISE WARNING '⚠️  High dead tuple ratio in %: % dead / % live', 
                table_info.tablename, table_info.dead_tuples, table_info.live_tuples;
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '📈 Total analyzed size: %', pg_size_pretty(total_size);
    RAISE NOTICE '🏢 Large tables (>100MB): %', large_table_count;
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 3. INDEX USAGE AND PERFORMANCE ANALYSIS
-- ===================================================================

DO $$
DECLARE
    index_info RECORD;
    unused_index_count INTEGER := 0;
    low_usage_count INTEGER := 0;
BEGIN
    RAISE NOTICE '🔍 INDEX USAGE AND PERFORMANCE ANALYSIS';
    RAISE NOTICE '======================================';
    
    -- Analyze index usage
    FOR index_info IN
        SELECT 
            schemaname,
            tablename,
            indexname,
            idx_tup_read,
            idx_tup_fetch,
            pg_size_pretty(pg_relation_size(indexname)) as index_size
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
        ORDER BY idx_tup_read DESC
    LOOP
        IF index_info.idx_tup_read = 0 THEN
            unused_index_count := unused_index_count + 1;
            RAISE WARNING '⚠️  Unused index: %.% (Size: %)', 
                index_info.tablename, index_info.indexname, index_info.index_size;
        ELSIF index_info.idx_tup_read < 100 THEN
            low_usage_count := low_usage_count + 1;
            RAISE NOTICE '📊 Low usage index: %.% (% reads, Size: %)', 
                index_info.tablename, index_info.indexname, 
                index_info.idx_tup_read, index_info.index_size;
        ELSE
            RAISE NOTICE '✅ Active index: %.% (% reads, Size: %)', 
                index_info.tablename, index_info.indexname, 
                index_info.idx_tup_read, index_info.index_size;
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 Index Summary:';
    RAISE NOTICE '   - Unused indexes: %', unused_index_count;
    RAISE NOTICE '   - Low usage indexes: %', low_usage_count;
    
    IF unused_index_count > 0 THEN
        RAISE NOTICE '💡 Consider dropping unused indexes to improve write performance';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 4. QUERY PERFORMANCE ANALYSIS
-- ===================================================================

DO $$
DECLARE
    slow_query_count INTEGER;
    avg_query_time NUMERIC;
BEGIN
    RAISE NOTICE '⚡ QUERY PERFORMANCE ANALYSIS';
    RAISE NOTICE '=============================';
    
    -- Check if pg_stat_statements is available
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements') THEN
        -- Get slow query statistics
        SELECT COUNT(*) 
        FROM pg_stat_statements 
        WHERE mean_exec_time > 1000 -- queries taking more than 1 second on average
        INTO slow_query_count;
        
        SELECT AVG(mean_exec_time)
        FROM pg_stat_statements
        INTO avg_query_time;
        
        RAISE NOTICE '📊 Query Performance Statistics:';
        RAISE NOTICE '   - Average query time: % ms', ROUND(avg_query_time, 2);
        RAISE NOTICE '   - Slow queries (>1s): %', slow_query_count;
        
        IF slow_query_count > 0 THEN
            RAISE WARNING '⚠️  % slow queries detected - review and optimize', slow_query_count;
        END IF;
    ELSE
        RAISE NOTICE '⚠️  pg_stat_statements extension not available';
        RAISE NOTICE '💡 Enable pg_stat_statements for detailed query analysis';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 5. SECURITY AND COMPLIANCE CHECK
-- ===================================================================

DO $$
DECLARE
    rls_table_count INTEGER;
    total_table_count INTEGER;
    policy_count INTEGER;
BEGIN
    RAISE NOTICE '🛡️  SECURITY AND COMPLIANCE CHECK';
    RAISE NOTICE '=================================';
    
    -- Check Row Level Security (RLS) configuration
    SELECT COUNT(*) 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    INTO total_table_count;
    
    SELECT COUNT(*) 
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true
    INTO rls_table_count;
    
    SELECT COUNT(*) 
    FROM pg_policies 
    WHERE schemaname = 'public'
    INTO policy_count;
    
    RAISE NOTICE '🔒 Row Level Security Status:';
    RAISE NOTICE '   - Tables with RLS: % / %', rls_table_count, total_table_count;
    RAISE NOTICE '   - Active policies: %', policy_count;
    
    IF rls_table_count < total_table_count THEN
        RAISE WARNING '⚠️  Not all tables have RLS enabled - security risk for multi-tenant app';
    END IF;
    
    -- Check for sensitive columns
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND column_name ILIKE '%password%'
    ) THEN
        RAISE NOTICE '🔐 Password columns detected - ensure proper hashing';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 6. BACKUP AND RECOVERY READINESS
-- ===================================================================

DO $$
DECLARE
    wal_level TEXT;
    archive_mode TEXT;
BEGIN
    RAISE NOTICE '💾 BACKUP AND RECOVERY READINESS';
    RAISE NOTICE '=================================';
    
    -- Check WAL configuration
    SELECT setting FROM pg_settings WHERE name = 'wal_level' INTO wal_level;
    SELECT setting FROM pg_settings WHERE name = 'archive_mode' INTO archive_mode;
    
    RAISE NOTICE '📊 WAL Configuration:';
    RAISE NOTICE '   - WAL Level: %', wal_level;
    RAISE NOTICE '   - Archive Mode: %', archive_mode;
    
    IF wal_level = 'minimal' THEN
        RAISE WARNING '⚠️  WAL level is minimal - consider replica or logical for better recovery';
    END IF;
    
    RAISE NOTICE '💡 Supabase handles automated backups - verify backup retention policy';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 7. FINAL RECOMMENDATIONS
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '💡 OPTIMIZATION RECOMMENDATIONS';
    RAISE NOTICE '===============================';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 IMMEDIATE ACTIONS:';
    RAISE NOTICE '   1. Review and address any WARNING messages above';
    RAISE NOTICE '   2. Drop unused indexes if any were detected';
    RAISE NOTICE '   3. Run VACUUM ANALYZE on tables with high dead tuple ratios';
    RAISE NOTICE '   4. Enable RLS on all tables for multi-tenant security';
    RAISE NOTICE '';
    RAISE NOTICE '📈 PERFORMANCE OPTIMIZATIONS:';
    RAISE NOTICE '   1. Monitor slow queries and add indexes as needed';
    RAISE NOTICE '   2. Consider connection pooling for high-traffic scenarios';
    RAISE NOTICE '   3. Implement caching for frequently accessed data';
    RAISE NOTICE '   4. Use prepared statements for repeated queries';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY ENHANCEMENTS:';
    RAISE NOTICE '   1. Ensure all sensitive data is properly encrypted';
    RAISE NOTICE '   2. Implement audit logging for compliance';
    RAISE NOTICE '   3. Regular security assessments and penetration testing';
    RAISE NOTICE '   4. Monitor for unusual access patterns';
    RAISE NOTICE '';
    RAISE NOTICE '✅ DATABASE OPTIMIZATION CHECK COMPLETE!';
    RAISE NOTICE '';
END $$;

-- Disable timing
\timing off
