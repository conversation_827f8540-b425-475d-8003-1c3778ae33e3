-- ===================================================================
-- ROW LEVEL SECURITY (RLS) IMPLEMENTATION
-- AssetHunterPro - Multi-Tenant Security & Data Isolation
-- ===================================================================
-- This script enables comprehensive RLS for secure multi-tenant operations
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔒 ENABLING ROW LEVEL SECURITY - ASSETHUNTERPRO';
    RAISE NOTICE '===============================================';
    RAISE NOTICE 'Timestamp: %', NOW();
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 1. ENABLE RLS ON ALL TENANT-SPECIFIC TABLES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🛡️  ENABLING RLS ON CORE TABLES';
    RAISE NOTICE '================================';
END $$;

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Enable RLS on claims table (main tenant data)
ALTER TABLE claims ENABLE ROW LEVEL SECURITY;

-- Enable RLS on teams table
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Enable RLS on claim activities
ALTER TABLE claim_activities ENABLE ROW LEVEL SECURITY;

-- Enable RLS on claim contacts
ALTER TABLE claim_contacts ENABLE ROW LEVEL SECURITY;

-- Enable RLS on claim documents
ALTER TABLE claim_documents ENABLE ROW LEVEL SECURITY;

-- Enable RLS on import batches
ALTER TABLE import_batches ENABLE ROW LEVEL SECURITY;

-- Enable RLS on batch records
ALTER TABLE batch_records ENABLE ROW LEVEL SECURITY;

-- Enable RLS on performance metrics
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

-- Enable RLS on business rules
ALTER TABLE business_rules ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    RAISE NOTICE '✅ RLS enabled on all core tables';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 2. CREATE HELPER FUNCTIONS FOR RLS POLICIES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🔧 CREATING RLS HELPER FUNCTIONS';
    RAISE NOTICE '================================';
END $$;

-- Function to get current user's team ID
CREATE OR REPLACE FUNCTION auth.current_user_team_id()
RETURNS UUID
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT team_id 
    FROM users 
    WHERE id = auth.uid()
    LIMIT 1;
$$;

-- Function to get current user's role
CREATE OR REPLACE FUNCTION auth.current_user_role()
RETURNS TEXT
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT role 
    FROM users 
    WHERE id = auth.uid()
    LIMIT 1;
$$;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() 
        AND role IN ('admin', 'compliance')
    );
$$;

-- Function to check if user is manager
CREATE OR REPLACE FUNCTION auth.is_manager()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() 
        AND role IN ('admin', 'compliance', 'senior_agent')
    );
$$;

DO $$
BEGIN
    RAISE NOTICE '✅ RLS helper functions created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 3. CREATE RLS POLICIES FOR USERS TABLE
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '👥 CREATING USER TABLE POLICIES';
    RAISE NOTICE '===============================';
END $$;

-- Users can view their own profile and team members
CREATE POLICY "users_select_policy" ON users
    FOR SELECT
    USING (
        id = auth.uid() OR  -- Own profile
        (team_id = auth.current_user_team_id() AND auth.is_manager()) OR  -- Team members for managers
        auth.is_admin()  -- Admins see all
    );

-- Users can update their own profile, managers can update team members
CREATE POLICY "users_update_policy" ON users
    FOR UPDATE
    USING (
        id = auth.uid() OR  -- Own profile
        (team_id = auth.current_user_team_id() AND auth.is_manager()) OR  -- Team members for managers
        auth.is_admin()  -- Admins update all
    );

-- Only admins can insert new users
CREATE POLICY "users_insert_policy" ON users
    FOR INSERT
    WITH CHECK (auth.is_admin());

-- Only admins can delete users
CREATE POLICY "users_delete_policy" ON users
    FOR DELETE
    USING (auth.is_admin());

DO $$
BEGIN
    RAISE NOTICE '✅ User table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 4. CREATE RLS POLICIES FOR CLAIMS TABLE
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '📋 CREATING CLAIMS TABLE POLICIES';
    RAISE NOTICE '=================================';
END $$;

-- Claims visibility based on assignment and team
CREATE POLICY "claims_select_policy" ON claims
    FOR SELECT
    USING (
        assigned_agent_id = auth.uid() OR  -- Assigned to user
        assigned_agent_id IN (
            SELECT id FROM users 
            WHERE team_id = auth.current_user_team_id()
        ) OR  -- Assigned to team member
        auth.is_admin()  -- Admins see all
    );

-- Claims can be updated by assigned agent, team managers, or admins
CREATE POLICY "claims_update_policy" ON claims
    FOR UPDATE
    USING (
        assigned_agent_id = auth.uid() OR  -- Assigned to user
        (assigned_agent_id IN (
            SELECT id FROM users 
            WHERE team_id = auth.current_user_team_id()
        ) AND auth.is_manager()) OR  -- Team member and user is manager
        auth.is_admin()  -- Admins update all
    );

-- Claims can be inserted by managers and admins
CREATE POLICY "claims_insert_policy" ON claims
    FOR INSERT
    WITH CHECK (
        auth.is_manager() OR auth.is_admin()
    );

-- Claims can be deleted by admins only
CREATE POLICY "claims_delete_policy" ON claims
    FOR DELETE
    USING (auth.is_admin());

DO $$
BEGIN
    RAISE NOTICE '✅ Claims table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 5. CREATE RLS POLICIES FOR TEAMS TABLE
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '👥 CREATING TEAMS TABLE POLICIES';
    RAISE NOTICE '================================';
END $$;

-- Users can view their own team and admins see all
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT
    USING (
        id = auth.current_user_team_id() OR
        auth.is_admin()
    );

-- Only admins can modify teams
CREATE POLICY "teams_modify_policy" ON teams
    FOR ALL
    USING (auth.is_admin())
    WITH CHECK (auth.is_admin());

DO $$
BEGIN
    RAISE NOTICE '✅ Teams table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 6. CREATE RLS POLICIES FOR ACTIVITY TABLES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '📊 CREATING ACTIVITY TABLE POLICIES';
    RAISE NOTICE '===================================';
END $$;

-- Claim activities - visible based on claim access
CREATE POLICY "claim_activities_select_policy" ON claim_activities
    FOR SELECT
    USING (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = auth.uid() OR
                  assigned_agent_id IN (
                      SELECT id FROM users 
                      WHERE team_id = auth.current_user_team_id()
                  )
        ) OR
        agent_id = auth.uid() OR  -- Created by user
        auth.is_admin()
    );

-- Activities can be inserted by users with claim access
CREATE POLICY "claim_activities_insert_policy" ON claim_activities
    FOR INSERT
    WITH CHECK (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = auth.uid() OR
                  (assigned_agent_id IN (
                      SELECT id FROM users 
                      WHERE team_id = auth.current_user_team_id()
                  ) AND auth.is_manager())
        ) OR
        auth.is_admin()
    );

-- Activities can be updated by creator or admins
CREATE POLICY "claim_activities_update_policy" ON claim_activities
    FOR UPDATE
    USING (
        agent_id = auth.uid() OR
        auth.is_admin()
    );

DO $$
BEGIN
    RAISE NOTICE '✅ Activity table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 7. CREATE RLS POLICIES FOR DOCUMENT TABLES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '📄 CREATING DOCUMENT TABLE POLICIES';
    RAISE NOTICE '==================================';
END $$;

-- Claim documents - visible based on claim access
CREATE POLICY "claim_documents_select_policy" ON claim_documents
    FOR SELECT
    USING (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = auth.uid() OR
                  assigned_agent_id IN (
                      SELECT id FROM users 
                      WHERE team_id = auth.current_user_team_id()
                  )
        ) OR
        uploaded_by = auth.uid() OR  -- Uploaded by user
        auth.is_admin()
    );

-- Documents can be inserted by users with claim access
CREATE POLICY "claim_documents_insert_policy" ON claim_documents
    FOR INSERT
    WITH CHECK (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = auth.uid() OR
                  (assigned_agent_id IN (
                      SELECT id FROM users 
                      WHERE team_id = auth.current_user_team_id()
                  ) AND auth.is_manager())
        ) OR
        auth.is_admin()
    );

-- Documents can be updated by uploader or admins
CREATE POLICY "claim_documents_update_policy" ON claim_documents
    FOR UPDATE
    USING (
        uploaded_by = auth.uid() OR
        auth.is_admin()
    );

DO $$
BEGIN
    RAISE NOTICE '✅ Document table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 8. CREATE RLS POLICIES FOR IMPORT TABLES
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '📥 CREATING IMPORT TABLE POLICIES';
    RAISE NOTICE '================================';
END $$;

-- Import batches - team-based access
CREATE POLICY "import_batches_policy" ON import_batches
    FOR ALL
    USING (
        uploaded_by = auth.uid() OR
        uploaded_by IN (
            SELECT id FROM users 
            WHERE team_id = auth.current_user_team_id()
        ) OR
        auth.is_admin()
    )
    WITH CHECK (
        auth.is_manager() OR auth.is_admin()
    );

-- Batch records - based on batch access
CREATE POLICY "batch_records_policy" ON batch_records
    FOR ALL
    USING (
        import_batch_id IN (
            SELECT id FROM import_batches
            WHERE uploaded_by = auth.uid() OR
                  uploaded_by IN (
                      SELECT id FROM users 
                      WHERE team_id = auth.current_user_team_id()
                  )
        ) OR
        auth.is_admin()
    );

DO $$
BEGIN
    RAISE NOTICE '✅ Import table policies created';
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 9. FINAL RLS VERIFICATION
-- ===================================================================

DO $$
DECLARE
    rls_enabled_count INTEGER;
    total_tables INTEGER;
    policy_count INTEGER;
BEGIN
    RAISE NOTICE '🔍 RLS IMPLEMENTATION VERIFICATION';
    RAISE NOTICE '==================================';
    
    -- Count tables with RLS enabled
    SELECT COUNT(*)
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true
    INTO rls_enabled_count;
    
    -- Count total tables
    SELECT COUNT(*)
    FROM pg_tables 
    WHERE schemaname = 'public'
    INTO total_tables;
    
    -- Count policies
    SELECT COUNT(*)
    FROM pg_policies 
    WHERE schemaname = 'public'
    INTO policy_count;
    
    RAISE NOTICE '📊 RLS Status Summary:';
    RAISE NOTICE '   - Tables with RLS: % / %', rls_enabled_count, total_tables;
    RAISE NOTICE '   - Active Policies: %', policy_count;
    
    IF rls_enabled_count >= 10 THEN
        RAISE NOTICE '✅ RLS successfully enabled on core tables!';
    ELSE
        RAISE WARNING '⚠️  Some tables may not have RLS enabled';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ROW LEVEL SECURITY IMPLEMENTATION COMPLETE!';
    RAISE NOTICE '🔒 Your database now has comprehensive multi-tenant security!';
    RAISE NOTICE '';
END $$;
