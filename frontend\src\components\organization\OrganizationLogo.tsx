import React, { useState, useEffect } from 'react'
import { Layout, Building2 } from 'lucide-react'
import { organizationService } from '../../services/organizationService'
import type { Organization } from '../../types/database'

interface OrganizationLogoProps {
  isExpanded?: boolean
  className?: string
  showFallback?: boolean
}

export const OrganizationLogo: React.FC<OrganizationLogoProps> = ({
  isExpanded = true,
  className = '',
  showFallback = true
}) => {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    loadOrganization()
  }, [])

  const loadOrganization = async () => {
    try {
      const org = await organizationService.getCurrentUserOrganization()
      setOrganization(org)
    } catch (error) {
      console.error('Error loading organization for logo:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageError = () => {
    setImageError(true)
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg animate-pulse">
          <div className="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded" />
        </div>
        {isExpanded && (
          <div className="animate-pulse">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-32 mb-1" />
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-24" />
          </div>
        )}
      </div>
    )
  }

  // Show organization logo if available and not errored
  if (organization?.logo_url && !imageError) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex-shrink-0">
          <img
            src={organization.logo_url}
            alt={`${organization.name} logo`}
            className="h-10 w-10 object-contain rounded-lg"
            onError={handleImageError}
          />
        </div>
        {isExpanded && (
          <div>
            <h1 className="text-lg font-bold text-gray-900 dark:text-gray-100 truncate">
              {organization.name}
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Asset Recovery Platform
            </p>
          </div>
        )}
      </div>
    )
  }

  // Show fallback if no logo or if showFallback is true
  if (showFallback) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
          {organization ? (
            <Building2 className="h-6 w-6 text-blue-600" />
          ) : (
            <Layout className="h-6 w-6 text-blue-600" />
          )}
        </div>
        {isExpanded && (
          <div>
            <h1 className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {organization?.name || 'AssetHunterPro'}
            </h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Asset Recovery Platform
            </p>
          </div>
        )}
      </div>
    )
  }

  // Return null if no fallback should be shown
  return null
}

// Compact version for smaller spaces
export const OrganizationLogoCompact: React.FC<{
  className?: string
}> = ({ className = '' }) => {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    loadOrganization()
  }, [])

  const loadOrganization = async () => {
    try {
      const org = await organizationService.getCurrentUserOrganization()
      setOrganization(org)
    } catch (error) {
      console.error('Error loading organization for compact logo:', error)
    }
  }

  const handleImageError = () => {
    setImageError(true)
  }

  if (organization?.logo_url && !imageError) {
    return (
      <img
        src={organization.logo_url}
        alt={`${organization.name} logo`}
        className={`h-8 w-8 object-contain rounded ${className}`}
        onError={handleImageError}
      />
    )
  }

  return (
    <div className={`p-1 bg-blue-100 dark:bg-blue-900/20 rounded ${className}`}>
      <Building2 className="h-6 w-6 text-blue-600" />
    </div>
  )
}
