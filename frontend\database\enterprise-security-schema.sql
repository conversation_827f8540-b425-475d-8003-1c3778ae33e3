-- Enterprise Security Suite Schema
-- Advanced security features for enterprise compliance

-- Multi-Factor Authentication settings
CREATE TABLE mfa_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- MFA configuration
    is_enabled BOOLEAN DEFAULT false,
    backup_codes TEXT[], -- Encrypted backup codes
    
    -- TOTP (Time-based One-Time Password)
    totp_secret VARCHAR(255), -- Encrypted TOTP secret
    totp_enabled BOOLEAN DEFAULT false,
    
    -- SMS-based MFA
    sms_phone VARCHAR(20),
    sms_enabled BOOLEAN DEFAULT false,
    
    -- Email-based MFA
    email_enabled BOOLEAN DEFAULT false,
    
    -- Hardware security keys (WebAuthn/FIDO2)
    webauthn_enabled BOOLEAN DEFAULT false,
    
    -- Recovery settings
    recovery_email VARCHAR(255),
    last_backup_codes_generated TIMESTAMPTZ,
    
    -- Enforcement
    enforced_by_admin BOOLEAN DEFAULT false,
    enforcement_date TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- WebAuthn credentials for hardware security keys
CREATE TABLE webauthn_credentials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- WebAuthn data
    credential_id TEXT NOT NULL,
    public_key TEXT NOT NULL,
    counter BIGINT DEFAULT 0,
    
    -- Metadata
    device_name VARCHAR(100),
    device_type VARCHAR(50), -- 'security_key', 'platform', 'cross_platform'
    
    -- Usage tracking
    last_used TIMESTAMPTZ,
    use_count INTEGER DEFAULT 0,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(credential_id)
);

-- Single Sign-On (SSO) configurations
CREATE TABLE sso_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- SSO provider details
    provider_name VARCHAR(100) NOT NULL, -- 'okta', 'azure_ad', 'google_workspace', 'custom_saml'
    provider_type VARCHAR(50) NOT NULL CHECK (provider_type IN ('saml', 'oauth2', 'oidc')),
    
    -- Configuration
    entity_id VARCHAR(255),
    sso_url TEXT NOT NULL,
    slo_url TEXT, -- Single Logout URL
    certificate TEXT, -- X.509 certificate for SAML
    
    -- OAuth/OIDC specific
    client_id VARCHAR(255),
    client_secret VARCHAR(255), -- Encrypted
    authorization_endpoint TEXT,
    token_endpoint TEXT,
    userinfo_endpoint TEXT,
    
    -- Attribute mapping
    attribute_mapping JSONB DEFAULT '{}', -- Map SSO attributes to user fields
    
    -- Settings
    is_enabled BOOLEAN DEFAULT false,
    auto_provision_users BOOLEAN DEFAULT false,
    default_role VARCHAR(50) DEFAULT 'junior_agent',
    
    -- Security
    require_signed_assertions BOOLEAN DEFAULT true,
    encrypt_assertions BOOLEAN DEFAULT false,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id, provider_name)
);

-- Advanced audit logging
CREATE TABLE security_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Event details
    event_type VARCHAR(100) NOT NULL, -- 'login', 'logout', 'permission_change', 'data_access', etc.
    event_category VARCHAR(50) NOT NULL, -- 'authentication', 'authorization', 'data', 'system'
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Actor information
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(255),
    user_role VARCHAR(50),
    
    -- Session and request details
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255),
    
    -- Resource accessed
    resource_type VARCHAR(100), -- 'claim', 'user', 'organization', 'system'
    resource_id VARCHAR(255),
    resource_name VARCHAR(255),
    
    -- Action details
    action VARCHAR(100) NOT NULL, -- 'create', 'read', 'update', 'delete', 'login', etc.
    outcome VARCHAR(20) DEFAULT 'success' CHECK (outcome IN ('success', 'failure', 'blocked')),
    
    -- Additional context
    details JSONB DEFAULT '{}',
    changes JSONB DEFAULT '{}', -- Before/after values for updates
    
    -- Compliance fields
    retention_period INTEGER DEFAULT 2555, -- Days to retain (7 years default)
    is_sensitive BOOLEAN DEFAULT false,
    
    -- Geolocation
    country_code VARCHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes for performance
    INDEX (organization_id, event_timestamp),
    INDEX (user_id, event_timestamp),
    INDEX (event_type, event_timestamp),
    INDEX (severity, event_timestamp)
);

-- IP whitelist/blacklist for access control
CREATE TABLE ip_access_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Rule details
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(20) NOT NULL CHECK (rule_type IN ('whitelist', 'blacklist')),
    
    -- IP specification
    ip_address INET,
    ip_range CIDR,
    
    -- Scope
    applies_to VARCHAR(20) DEFAULT 'all' CHECK (applies_to IN ('all', 'admin', 'api', 'specific_users')),
    specific_users UUID[], -- Array of user IDs if applies_to = 'specific_users'
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    description TEXT,
    created_by UUID REFERENCES users(id),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Session management and security
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Session details
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE,
    
    -- Device and location
    device_fingerprint VARCHAR(255),
    device_name VARCHAR(100),
    device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet'
    browser VARCHAR(100),
    os VARCHAR(100),
    
    -- Network information
    ip_address INET NOT NULL,
    country_code VARCHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    
    -- Session lifecycle
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    
    -- Security flags
    is_active BOOLEAN DEFAULT true,
    is_trusted_device BOOLEAN DEFAULT false,
    requires_mfa BOOLEAN DEFAULT false,
    mfa_verified BOOLEAN DEFAULT false,
    
    -- Risk assessment
    risk_score INTEGER DEFAULT 0, -- 0-100 risk score
    risk_factors TEXT[], -- Array of risk factors
    
    -- Termination
    terminated_at TIMESTAMPTZ,
    termination_reason VARCHAR(100) -- 'logout', 'timeout', 'admin_revoke', 'security_breach'
);

-- Data Loss Prevention (DLP) rules
CREATE TABLE dlp_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Rule identification
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(50) NOT NULL, -- 'content_inspection', 'data_classification', 'export_control'
    
    -- Pattern matching
    patterns TEXT[], -- Regex patterns to match
    keywords TEXT[], -- Keywords to detect
    data_types TEXT[], -- 'ssn', 'credit_card', 'email', 'phone', 'custom'
    
    -- Actions
    action VARCHAR(50) NOT NULL CHECK (action IN ('block', 'warn', 'log', 'quarantine')),
    notification_emails TEXT[],
    
    -- Scope
    applies_to_data_types TEXT[], -- 'documents', 'exports', 'emails', 'api_responses'
    exclude_roles TEXT[], -- Roles exempt from this rule
    
    -- Sensitivity
    sensitivity_level VARCHAR(20) DEFAULT 'medium' CHECK (sensitivity_level IN ('low', 'medium', 'high', 'critical')),
    
    -- Status
    is_enabled BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- DLP incidents and violations
CREATE TABLE dlp_incidents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    rule_id UUID NOT NULL REFERENCES dlp_rules(id),
    
    -- Incident details
    incident_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    
    -- User and context
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(255),
    
    -- Data involved
    data_type VARCHAR(100),
    data_classification VARCHAR(50),
    matched_patterns TEXT[],
    matched_content TEXT, -- Redacted/masked content
    
    -- Action taken
    action_taken VARCHAR(50),
    blocked BOOLEAN DEFAULT false,
    
    -- File/resource details
    file_name VARCHAR(255),
    file_size BIGINT,
    file_hash VARCHAR(255),
    
    -- Network context
    ip_address INET,
    user_agent TEXT,
    
    -- Resolution
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'false_positive')),
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    resolution_notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Compliance frameworks and controls
CREATE TABLE compliance_frameworks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Framework details
    framework_code VARCHAR(50) UNIQUE NOT NULL, -- 'soc2', 'gdpr', 'hipaa', 'pci_dss'
    framework_name VARCHAR(100) NOT NULL,
    version VARCHAR(20),
    
    -- Description
    description TEXT,
    requirements JSONB DEFAULT '{}', -- Detailed requirements
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization compliance status
CREATE TABLE organization_compliance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    framework_id UUID NOT NULL REFERENCES compliance_frameworks(id),
    
    -- Compliance status
    status VARCHAR(20) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'compliant', 'non_compliant')),
    compliance_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Assessment details
    last_assessment_date TIMESTAMPTZ,
    next_assessment_due TIMESTAMPTZ,
    assessor_name VARCHAR(100),
    
    -- Certification
    certification_date TIMESTAMPTZ,
    certification_expires TIMESTAMPTZ,
    certificate_number VARCHAR(100),
    
    -- Documentation
    evidence_documents TEXT[], -- URLs or file references
    gaps_identified TEXT[],
    remediation_plan TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id, framework_id)
);

-- Insert default compliance frameworks
INSERT INTO compliance_frameworks (framework_code, framework_name, version, description) VALUES
('soc2_type2', 'SOC 2 Type II', '2017', 'Service Organization Control 2 Type II compliance for security, availability, processing integrity, confidentiality, and privacy'),
('gdpr', 'GDPR', '2018', 'General Data Protection Regulation for EU data protection and privacy'),
('ccpa', 'CCPA', '2020', 'California Consumer Privacy Act for California residents data protection'),
('hipaa', 'HIPAA', '1996', 'Health Insurance Portability and Accountability Act for healthcare data protection'),
('pci_dss', 'PCI DSS', '4.0', 'Payment Card Industry Data Security Standard for payment card data protection'),
('iso27001', 'ISO 27001', '2013', 'International standard for information security management systems');

-- Functions for security logging
CREATE OR REPLACE FUNCTION log_security_event(
    p_organization_id UUID,
    p_event_type VARCHAR(100),
    p_event_category VARCHAR(50),
    p_user_id UUID DEFAULT NULL,
    p_action VARCHAR(100) DEFAULT NULL,
    p_resource_type VARCHAR(100) DEFAULT NULL,
    p_resource_id VARCHAR(255) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_details JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO security_audit_logs (
        organization_id, event_type, event_category, user_id, action,
        resource_type, resource_id, ip_address, details
    ) VALUES (
        p_organization_id, p_event_type, p_event_category, p_user_id, p_action,
        p_resource_type, p_resource_id, p_ip_address, p_details
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check IP access rules
CREATE OR REPLACE FUNCTION check_ip_access(
    p_organization_id UUID,
    p_ip_address INET,
    p_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_allowed BOOLEAN := true;
    v_rule RECORD;
BEGIN
    -- Check blacklist rules first
    FOR v_rule IN 
        SELECT * FROM ip_access_rules 
        WHERE organization_id = p_organization_id 
        AND rule_type = 'blacklist' 
        AND is_active = true
    LOOP
        -- Check if IP matches blacklist rule
        IF (v_rule.ip_address IS NOT NULL AND v_rule.ip_address = p_ip_address) OR
           (v_rule.ip_range IS NOT NULL AND p_ip_address << v_rule.ip_range) THEN
            -- Check if rule applies to this user
            IF v_rule.applies_to = 'all' OR 
               (v_rule.applies_to = 'specific_users' AND p_user_id = ANY(v_rule.specific_users)) THEN
                RETURN false; -- Blocked by blacklist
            END IF;
        END IF;
    END LOOP;
    
    -- Check whitelist rules
    v_allowed := false; -- Default to blocked if whitelist exists
    
    FOR v_rule IN 
        SELECT * FROM ip_access_rules 
        WHERE organization_id = p_organization_id 
        AND rule_type = 'whitelist' 
        AND is_active = true
    LOOP
        v_allowed := false; -- Whitelist exists, so default is blocked
        
        -- Check if IP matches whitelist rule
        IF (v_rule.ip_address IS NOT NULL AND v_rule.ip_address = p_ip_address) OR
           (v_rule.ip_range IS NOT NULL AND p_ip_address << v_rule.ip_range) THEN
            -- Check if rule applies to this user
            IF v_rule.applies_to = 'all' OR 
               (v_rule.applies_to = 'specific_users' AND p_user_id = ANY(v_rule.specific_users)) THEN
                RETURN true; -- Allowed by whitelist
            END IF;
        END IF;
    END LOOP;
    
    -- If no whitelist rules exist, allow by default (unless blocked by blacklist)
    IF NOT EXISTS (SELECT 1 FROM ip_access_rules WHERE organization_id = p_organization_id AND rule_type = 'whitelist' AND is_active = true) THEN
        v_allowed := true;
    END IF;
    
    RETURN v_allowed;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance
CREATE INDEX idx_security_audit_logs_org_time ON security_audit_logs(organization_id, event_timestamp DESC);
CREATE INDEX idx_security_audit_logs_user_time ON security_audit_logs(user_id, event_timestamp DESC);
CREATE INDEX idx_security_audit_logs_event_type ON security_audit_logs(event_type, event_timestamp DESC);
CREATE INDEX idx_user_sessions_user_active ON user_sessions(user_id, is_active, last_activity);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token) WHERE is_active = true;
CREATE INDEX idx_dlp_incidents_org_status ON dlp_incidents(organization_id, status, created_at DESC);
CREATE INDEX idx_ip_access_rules_org_active ON ip_access_rules(organization_id, is_active);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON mfa_settings TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON webauthn_credentials TO authenticated;
GRANT SELECT ON sso_configurations TO authenticated;
GRANT SELECT, INSERT ON security_audit_logs TO authenticated;
GRANT SELECT ON ip_access_rules TO authenticated;
GRANT SELECT, INSERT, UPDATE ON user_sessions TO authenticated;
GRANT SELECT ON dlp_rules TO authenticated;
GRANT SELECT, INSERT ON dlp_incidents TO authenticated;
GRANT SELECT ON compliance_frameworks TO authenticated;
GRANT SELECT ON organization_compliance TO authenticated;

-- Row Level Security
ALTER TABLE mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE webauthn_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE dlp_incidents ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own MFA settings" ON mfa_settings
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own WebAuthn credentials" ON webauthn_credentials
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can view their organization's audit logs" ON security_audit_logs
    FOR SELECT TO authenticated
    USING (organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid()));

CREATE POLICY "Users can manage their own sessions" ON user_sessions
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can view their organization's DLP incidents" ON dlp_incidents
    FOR SELECT TO authenticated
    USING (organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid()));
