/**
 * Test utility to verify organization logo functionality
 * Run this in the browser console to test the setup
 */

import { supabase } from '../lib/supabase'
import { organizationService } from '../services/organizationService'

export async function testOrganizationSetup() {
  console.log('🧪 Testing Organization Logo Setup...')
  
  try {
    // Test 1: Check if organizations table exists
    console.log('1️⃣ Testing organizations table...')
    const { data: orgs, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .limit(1)
    
    if (orgError) {
      console.error('❌ Organizations table not found:', orgError.message)
      return false
    }
    console.log('✅ Organizations table exists')
    
    // Test 2: Check if storage bucket exists
    console.log('2️⃣ Testing storage bucket...')
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets()
    
    if (bucketError) {
      console.error('❌ Storage bucket check failed:', bucketError.message)
      return false
    }
    
    const logosBucket = buckets?.find(b => b.id === 'company-logos')
    if (!logosBucket) {
      console.error('❌ company-logos bucket not found')
      return false
    }
    console.log('✅ Storage bucket exists')
    
    // Test 3: Check current user organization
    console.log('3️⃣ Testing current user organization...')
    const currentOrg = await organizationService.getCurrentUserOrganization()
    
    if (!currentOrg) {
      console.error('❌ No organization found for current user')
      return false
    }
    console.log('✅ Current user organization:', currentOrg.name)
    
    // Test 4: Check permissions
    console.log('4️⃣ Testing permissions...')
    const canManage = await organizationService.canManageOrganization(currentOrg.id)
    console.log(`✅ Can manage organization: ${canManage}`)
    
    // Test 5: Test file upload simulation (without actual file)
    console.log('5️⃣ Testing upload path...')
    const testPath = `${currentOrg.id}/test-logo-${Date.now()}.png`
    console.log(`✅ Upload path would be: ${testPath}`)
    
    console.log('🎉 All tests passed! Organization logo setup is working correctly.')
    
    return {
      success: true,
      organization: currentOrg,
      canManage,
      bucketExists: true,
      testPath
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error)
    return false
  }
}

export async function testLogoUpload(file: File) {
  console.log('🧪 Testing Logo Upload...')
  
  try {
    const currentOrg = await organizationService.getCurrentUserOrganization()
    if (!currentOrg) {
      console.error('❌ No organization found')
      return false
    }
    
    console.log(`📤 Uploading ${file.name} (${file.size} bytes)...`)
    const result = await organizationService.uploadLogo(currentOrg.id, file)
    
    if (result.success) {
      console.log('✅ Logo uploaded successfully!')
      console.log('🔗 Logo URL:', result.logo_url)
      return result
    } else {
      console.error('❌ Logo upload failed:', result.error)
      return false
    }
    
  } catch (error) {
    console.error('❌ Upload test failed:', error)
    return false
  }
}

export async function testLogoRemoval() {
  console.log('🧪 Testing Logo Removal...')
  
  try {
    const currentOrg = await organizationService.getCurrentUserOrganization()
    if (!currentOrg) {
      console.error('❌ No organization found')
      return false
    }
    
    console.log('🗑️ Removing current logo...')
    const result = await organizationService.removeLogo(currentOrg.id)
    
    if (result.success) {
      console.log('✅ Logo removed successfully!')
      return true
    } else {
      console.error('❌ Logo removal failed:', result.error)
      return false
    }
    
  } catch (error) {
    console.error('❌ Removal test failed:', error)
    return false
  }
}

// Helper function to create a test image file
export function createTestImageFile(): File {
  // Create a simple 1x1 pixel PNG
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.fillStyle = '#3B82F6'
    ctx.fillRect(0, 0, 1, 1)
  }
  
  return new Promise<File>((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'test-logo.png', { type: 'image/png' })
        resolve(file)
      }
    }, 'image/png')
  }) as any
}

// Export for browser console usage
if (typeof window !== 'undefined') {
  (window as any).testOrganizationSetup = testOrganizationSetup
  (window as any).testLogoUpload = testLogoUpload
  (window as any).testLogoRemoval = testLogoRemoval
  (window as any).createTestImageFile = createTestImageFile
  
  console.log('🔧 Organization test utilities loaded!')
  console.log('Run testOrganizationSetup() to verify setup')
  console.log('Run testLogoUpload(file) to test upload')
  console.log('Run testLogoRemoval() to test removal')
}
