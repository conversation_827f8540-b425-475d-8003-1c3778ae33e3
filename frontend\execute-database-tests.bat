@echo off
echo ===================================================================
echo ASSETHUNTERPRO DATABASE OPTIMIZATION TESTING SUITE
echo ===================================================================
echo.
echo This script will run comprehensive database tests including:
echo - Connection and health checks
echo - Performance optimization analysis  
echo - Data integrity validation
echo - Load/stress testing
echo - Security assessment
echo.
echo Make sure you have Node.js installed and are in the frontend directory
echo.
pause

echo.
echo 🚀 Starting database tests...
echo.

REM Run the comprehensive database test suite
node run-database-tests.js

echo.
echo ===================================================================
echo DATABASE TESTING COMPLETE
echo ===================================================================
echo.
echo Check the generated reports for detailed results:
echo - JSON report: database-comprehensive-report-*.json
echo - HTML report: database-comprehensive-report-*.html
echo.
echo Also run the SQL health check manually in Supabase:
echo - File: database/comprehensive-optimization-check.sql
echo.
pause
