import { useState, useEffect, useCallback } from 'react'
import { permissionService, type PermissionCheck } from '../services/permissionService'
import { useAuth } from '../contexts/AuthContext'

interface UsePermissionsReturn {
  hasPermission: (permission: string, context?: Record<string, any>) => Promise<boolean>
  hasAllPermissions: (permissions: string[], context?: Record<string, any>) => Promise<boolean>
  hasAnyPermission: (permissions: string[], context?: Record<string, any>) => Promise<boolean>
  checkUsageLimit: (limitType: 'users' | 'claims' | 'storage' | 'api_calls', amount?: number) => Promise<boolean>
  isLoading: boolean
  lastCheck: PermissionCheck | null
  subscription: any
  refreshPermissions: () => void
}

export function usePermissions(): UsePermissionsReturn {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [lastCheck, setLastCheck] = useState<PermissionCheck | null>(null)
  const [subscription, setSubscription] = useState<any>(null)

  // Load subscription data
  useEffect(() => {
    if (user?.organization_id) {
      loadSubscription()
    }
  }, [user?.organization_id])

  const loadSubscription = async () => {
    if (!user?.organization_id) return
    
    try {
      const sub = await permissionService.getOrganizationSubscription(user.organization_id)
      setSubscription(sub)
    } catch (error) {
      console.error('Error loading subscription:', error)
    }
  }

  const hasPermission = useCallback(async (
    permission: string, 
    context?: Record<string, any>
  ): Promise<boolean> => {
    if (!user?.id) return false

    setIsLoading(true)
    try {
      const check = await permissionService.hasPermission(user.id, permission, context)
      setLastCheck(check)
      return check.allowed
    } catch (error) {
      console.error('Permission check error:', error)
      setLastCheck({ allowed: false, reason: 'Permission check failed' })
      return false
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const hasAllPermissions = useCallback(async (
    permissions: string[], 
    context?: Record<string, any>
  ): Promise<boolean> => {
    if (!user?.id) return false

    setIsLoading(true)
    try {
      const check = await permissionService.hasAllPermissions(user.id, permissions, context)
      setLastCheck(check)
      return check.allowed
    } catch (error) {
      console.error('Permission check error:', error)
      setLastCheck({ allowed: false, reason: 'Permission check failed' })
      return false
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const hasAnyPermission = useCallback(async (
    permissions: string[], 
    context?: Record<string, any>
  ): Promise<boolean> => {
    if (!user?.id) return false

    setIsLoading(true)
    try {
      const check = await permissionService.hasAnyPermission(user.id, permissions, context)
      setLastCheck(check)
      return check.allowed
    } catch (error) {
      console.error('Permission check error:', error)
      setLastCheck({ allowed: false, reason: 'Permission check failed' })
      return false
    } finally {
      setIsLoading(false)
    }
  }, [user?.id])

  const checkUsageLimit = useCallback(async (
    limitType: 'users' | 'claims' | 'storage' | 'api_calls',
    amount: number = 1
  ): Promise<boolean> => {
    if (!user?.organization_id) return false

    setIsLoading(true)
    try {
      const check = await permissionService.checkUsageLimit(user.organization_id, limitType, amount)
      setLastCheck(check)
      return check.allowed
    } catch (error) {
      console.error('Usage limit check error:', error)
      setLastCheck({ allowed: false, reason: 'Usage check failed' })
      return false
    } finally {
      setIsLoading(false)
    }
  }, [user?.organization_id])

  const refreshPermissions = useCallback(() => {
    permissionService.clearCache()
    if (user?.organization_id) {
      loadSubscription()
    }
  }, [user?.organization_id])

  return {
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    checkUsageLimit,
    isLoading,
    lastCheck,
    subscription,
    refreshPermissions
  }
}

// Hook for component-level permission checking with caching
export function usePermissionCheck(
  permission: string | string[],
  context?: Record<string, any>
) {
  const [isAllowed, setIsAllowed] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { hasPermission, hasAllPermissions } = usePermissions()

  useEffect(() => {
    checkPermission()
  }, [permission, context])

  const checkPermission = async () => {
    setIsLoading(true)
    setError(null)

    try {
      let allowed: boolean
      
      if (Array.isArray(permission)) {
        allowed = await hasAllPermissions(permission, context)
      } else {
        allowed = await hasPermission(permission, context)
      }
      
      setIsAllowed(allowed)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Permission check failed')
      setIsAllowed(false)
    } finally {
      setIsLoading(false)
    }
  }

  return { isAllowed, isLoading, error, recheck: checkPermission }
}

// Hook for usage limit checking
export function useUsageLimit(limitType: 'users' | 'claims' | 'storage' | 'api_calls') {
  const [usage, setUsage] = useState<{
    current: number
    limit: number
    percentage: number
    isNearLimit: boolean
    isAtLimit: boolean
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { subscription, checkUsageLimit } = usePermissions()

  useEffect(() => {
    if (subscription) {
      calculateUsage()
    }
  }, [subscription, limitType])

  const calculateUsage = async () => {
    if (!subscription) return

    setIsLoading(true)
    try {
      let current: number
      let limit: number

      switch (limitType) {
        case 'users':
          current = subscription.current_users
          limit = subscription.max_users || 0
          break
        case 'claims':
          current = subscription.current_claims
          limit = subscription.max_claims || 0
          break
        case 'storage':
          current = subscription.current_storage_gb
          limit = subscription.max_storage_gb || 0
          break
        case 'api_calls':
          current = subscription.current_api_calls_monthly
          limit = subscription.max_api_calls_monthly || 0
          break
        default:
          return
      }

      // -1 means unlimited
      if (limit === -1) {
        setUsage({
          current,
          limit: -1,
          percentage: 0,
          isNearLimit: false,
          isAtLimit: false
        })
        return
      }

      const percentage = limit > 0 ? (current / limit) * 100 : 0
      const isNearLimit = percentage >= 80
      const isAtLimit = percentage >= 100

      setUsage({
        current,
        limit,
        percentage,
        isNearLimit,
        isAtLimit
      })
    } catch (error) {
      console.error('Error calculating usage:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const canAdd = async (amount: number = 1): Promise<boolean> => {
    return await checkUsageLimit(limitType, amount)
  }

  return { usage, isLoading, canAdd, refresh: calculateUsage }
}

// Component wrapper for permission-based rendering
export function PermissionGate({
  permission,
  permissions,
  requireAll = true,
  context,
  fallback = null,
  children
}: {
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  context?: Record<string, any>
  fallback?: React.ReactNode
  children: React.ReactNode
}) {
  const permissionToCheck = permission ? [permission] : permissions || []
  const { isAllowed, isLoading } = usePermissionCheck(
    requireAll ? permissionToCheck : permissionToCheck,
    context
  )

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-4 w-16"></div>
  }

  if (!isAllowed) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Hook for plan-based feature access
export function usePlanFeatures() {
  const { subscription } = usePermissions()
  const [features, setFeatures] = useState<Record<string, boolean>>({})
  const [planName, setPlanName] = useState<string>('')

  useEffect(() => {
    if (subscription) {
      loadPlanFeatures()
    }
  }, [subscription])

  const loadPlanFeatures = async () => {
    if (!subscription) return

    try {
      const plan = await permissionService.getSubscriptionPlan(subscription.plan_id)
      if (plan) {
        setFeatures(plan.features || {})
        setPlanName(plan.name)
      }
    } catch (error) {
      console.error('Error loading plan features:', error)
    }
  }

  const hasFeature = (featureName: string): boolean => {
    return features[featureName] === true
  }

  const getFeatureValue = (featureName: string): any => {
    return features[featureName]
  }

  return {
    features,
    planName,
    hasFeature,
    getFeatureValue,
    subscription
  }
}
