// Quick Database Performance Test
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://hhjfltgvnkeugftabzjl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoamZsdGd2bmtldWdmdGFiempsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTQ2NTQsImV4cCI6MjA2Mzg5MDY1NH0.i7s3ValZ_I9ncz70AT4QmOCh7S-lGbtrKY7dFs16Q_Q';

console.log('🚀 QUICK DATABASE PERFORMANCE TEST');
console.log('===================================');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function runQuickTests() {
  let passed = 0;
  let failed = 0;
  let warnings = 0;
  
  console.log('\n1. Testing Connection...');
  try {
    const start = Date.now();
    const { data, error } = await supabase.from('users').select('count').limit(1);
    const duration = Date.now() - start;
    
    if (error) {
      console.log(`❌ Connection failed: ${error.message}`);
      failed++;
    } else {
      console.log(`✅ Connection successful (${duration}ms)`);
      passed++;
    }
  } catch (err) {
    console.log(`❌ Connection error: ${err.message}`);
    failed++;
  }
  
  console.log('\n2. Testing Table Access...');
  const tables = ['users', 'claims', 'teams'];
  
  for (const table of tables) {
    try {
      const start = Date.now();
      const { data, error } = await supabase.from(table).select('*').limit(1);
      const duration = Date.now() - start;
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        failed++;
      } else {
        console.log(`✅ ${table}: accessible (${duration}ms)`);
        passed++;
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
      failed++;
    }
  }
  
  console.log('\n3. Testing Query Performance...');
  try {
    const start = Date.now();
    const { data, error } = await supabase
      .from('claims')
      .select('id, claim_number, status')
      .limit(10);
    const duration = Date.now() - start;
    
    if (error) {
      console.log(`❌ Performance test failed: ${error.message}`);
      failed++;
    } else if (duration > 1000) {
      console.log(`⚠️  Performance test slow: ${duration}ms`);
      warnings++;
    } else {
      console.log(`✅ Performance test passed: ${duration}ms`);
      passed++;
    }
  } catch (err) {
    console.log(`❌ Performance test error: ${err.message}`);
    failed++;
  }
  
  console.log('\n4. Testing Data Retrieval...');
  try {
    const start = Date.now();
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);
    const duration = Date.now() - start;
    
    if (usersError) {
      console.log(`❌ Data retrieval failed: ${usersError.message}`);
      failed++;
    } else {
      console.log(`✅ Data retrieval successful: ${users?.length || 0} records (${duration}ms)`);
      passed++;
    }
  } catch (err) {
    console.log(`❌ Data retrieval error: ${err.message}`);
    failed++;
  }
  
  console.log('\n5. Testing Concurrent Queries...');
  try {
    const start = Date.now();
    const promises = [
      supabase.from('users').select('id').limit(2),
      supabase.from('claims').select('id').limit(2),
      supabase.from('teams').select('id').limit(2)
    ];
    
    const results = await Promise.all(promises);
    const duration = Date.now() - start;
    
    const errors = results.filter(r => r.error);
    if (errors.length > 0) {
      console.log(`❌ Concurrent queries failed: ${errors.length} errors`);
      failed++;
    } else if (duration > 2000) {
      console.log(`⚠️  Concurrent queries slow: ${duration}ms`);
      warnings++;
    } else {
      console.log(`✅ Concurrent queries successful: ${duration}ms`);
      passed++;
    }
  } catch (err) {
    console.log(`❌ Concurrent queries error: ${err.message}`);
    failed++;
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⚠️  Warnings: ${warnings}`);
  
  const total = passed + failed + warnings;
  const successRate = ((passed / total) * 100).toFixed(1);
  console.log(`🎯 Success Rate: ${successRate}%`);
  
  // Health Assessment
  let health = 'EXCELLENT';
  if (failed > 0) {
    health = 'CRITICAL';
  } else if (warnings > 2) {
    health = 'NEEDS_ATTENTION';
  } else if (warnings > 0) {
    health = 'GOOD';
  }
  
  console.log(`🏥 Database Health: ${health}`);
  console.log(`🚀 Production Ready: ${failed === 0 ? 'YES' : 'NO'}`);
  
  if (failed === 0 && warnings === 0) {
    console.log('\n🎉 Excellent! Your database is optimized and production-ready!');
  } else if (failed === 0) {
    console.log('\n✅ Good! Minor optimizations recommended but production-ready.');
  } else {
    console.log('\n⚠️  Issues detected. Address failures before production deployment.');
  }
  
  return { passed, failed, warnings, health };
}

runQuickTests().catch(console.error);
