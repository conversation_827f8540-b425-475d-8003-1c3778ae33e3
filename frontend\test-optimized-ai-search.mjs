// ===================================================================
// OPTIMIZED AI SEARCH TESTING SUITE
// AssetHunterPro - Free-First AI Search Testing & Validation
// ===================================================================

console.log('🚀 OPTIMIZED AI SEARCH TESTING SUITE');
console.log('=====================================');

// Mock the optimized AI search service
class OptimizedAISearchTester {
  constructor() {
    this.SEARCH_TIERS = {
      free: {
        name: 'Free Tier',
        dailyLimit: 5,
        monthlyCost: 0,
        dataSources: [
          'Government Property Records',
          'Secretary of State Business Filings',
          'Public Court Records',
          'Social Media Public Profiles',
          'Free Directory Services'
        ],
        features: [
          'Basic contact information',
          'Property ownership records',
          'Business affiliations',
          'Public social profiles',
          'Basic background check'
        ]
      },
      premium: {
        name: 'Premium Tier',
        dailyLimit: 50,
        monthlyCost: 9.99,
        dataSources: [
          'All Free Sources',
          'Enhanced Property Data (Zillow)',
          'Professional Licenses',
          'Advanced Social Analytics',
          'Phone/Email Verification'
        ],
        features: [
          'All free features',
          'Property value estimates',
          'Professional background',
          'Social media analytics',
          'Contact verification',
          'Asset estimates'
        ]
      },
      enterprise: {
        name: 'Enterprise Tier',
        dailyLimit: 1000,
        monthlyCost: 99.99,
        dataSources: [
          'All Premium Sources',
          'Credit & Financial Data',
          'Advanced Background Checks',
          'Real-time Data Updates',
          'Custom Data Sources'
        ],
        features: [
          'All premium features',
          'Financial background',
          'Credit information',
          'Real-time updates',
          'Custom integrations',
          'Priority support'
        ]
      }
    };

    this.userQuotas = new Map();
  }

  async searchPerson(userId, query, requestedTier = 'free') {
    console.log(`\n🔍 OPTIMIZED SEARCH - User: ${userId}, Tier: ${requestedTier}`);
    console.log(`📋 Query:`, JSON.stringify(query, null, 2));

    // Get or create user quota
    let userQuota = this.userQuotas.get(userId) || {
      userId,
      tier: 'free',
      dailyLimit: 5,
      dailyUsed: 0,
      monthlyLimit: 150,
      monthlyUsed: 0,
      lastResetDate: new Date(),
      totalCost: 0,
      canUpgrade: true
    };

    // Check quota
    const quotaCheck = this.checkSearchQuota(userQuota, requestedTier);
    if (!quotaCheck.canSearch) {
      return {
        success: false,
        quotaExceeded: quotaCheck.quotaExceeded,
        upgradeRequired: quotaCheck.upgradeRequired,
        quota: userQuota,
        error: quotaCheck.reason
      };
    }

    // Perform tier-appropriate search
    const searchResults = await this.performTieredSearch(query, requestedTier);
    
    // Calculate cost
    const searchCost = this.calculateSearchCost(searchResults, requestedTier);
    
    // Update quota
    userQuota.dailyUsed++;
    userQuota.monthlyUsed++;
    userQuota.totalCost += searchCost;
    this.userQuotas.set(userId, userQuota);

    console.log(`💰 Search Cost: $${searchCost.toFixed(2)} (${requestedTier} tier)`);
    console.log(`📊 Quota: ${userQuota.dailyUsed}/${userQuota.dailyLimit} daily, $${userQuota.totalCost.toFixed(2)} total`);

    return {
      success: true,
      results: searchResults,
      quota: userQuota
    };
  }

  async performTieredSearch(query, tier) {
    console.log(`🎯 Executing ${tier} tier search...`);

    const searchPromises = [];
    
    // FREE TIER SOURCES (Always available)
    searchPromises.push(
      this.searchGovernmentRecords(query),
      this.searchBusinessFilings(query),
      this.searchPublicCourtRecords(query),
      this.searchFreeSocialMedia(query),
      this.searchFreeDirectories(query)
    );

    // PREMIUM TIER SOURCES
    if (tier === 'premium' || tier === 'enterprise') {
      searchPromises.push(
        this.searchEnhancedPropertyData(query),
        this.searchProfessionalLicenses(query),
        this.searchAdvancedSocial(query),
        this.verifyContactInformation(query)
      );
    }

    // ENTERPRISE TIER SOURCES
    if (tier === 'enterprise') {
      searchPromises.push(
        this.searchFinancialData(query),
        this.searchAdvancedBackground(query),
        this.searchRealTimeData(query)
      );
    }

    console.log(`🚀 Executing ${searchPromises.length} parallel searches...`);
    const searchResults = await Promise.allSettled(searchPromises);

    // Process results
    const processedData = this.processSearchResults(searchResults, query, tier);
    
    // Create person results
    const personResults = this.createOptimizedPersonResults(processedData, query, tier);

    return personResults.slice(0, 3); // Return top 3 results
  }

  // FREE TIER SEARCH METHODS
  async searchGovernmentRecords(query) {
    console.log('🏛️ Government Property Records (FREE)');
    await this.delay(500);
    
    const hasData = Math.random() > 0.3;
    if (hasData) {
      return {
        source: 'Government Property Records',
        confidence: 0.90,
        cost: 0,
        data: {
          properties: [{
            address: `${Math.floor(Math.random() * 9999)} Government Ave`,
            city: query.city || 'Property City',
            state: query.state || 'CA',
            ownerName: `${query.firstName} ${query.lastName}`,
            assessedValue: Math.floor(Math.random() * 400000) + 200000,
            propertyType: 'Residential',
            lastSaleDate: '2020-05-15'
          }]
        }
      };
    }
    return { source: 'Government Property Records', confidence: 0, cost: 0, data: null };
  }

  async searchBusinessFilings(query) {
    console.log('🏢 Business Filings (FREE)');
    await this.delay(400);
    
    const hasData = Math.random() > 0.6;
    if (hasData) {
      return {
        source: 'Secretary of State Business Filings',
        confidence: 0.85,
        cost: 0,
        data: {
          businesses: [{
            businessName: `${query.lastName} ${['LLC', 'Inc', 'Corp'][Math.floor(Math.random() * 3)]}`,
            entityType: 'Limited Liability Company',
            status: 'Active',
            filingDate: '2019-03-20',
            registeredAgent: `${query.firstName} ${query.lastName}`,
            businessAddress: `${Math.floor(Math.random() * 999)} Business Blvd`
          }]
        }
      };
    }
    return { source: 'Secretary of State Business Filings', confidence: 0, cost: 0, data: null };
  }

  async searchPublicCourtRecords(query) {
    console.log('⚖️ Public Court Records (FREE)');
    await this.delay(600);
    
    const hasData = Math.random() > 0.8; // Less common
    if (hasData) {
      return {
        source: 'Public Court Records',
        confidence: 0.95,
        cost: 0,
        data: {
          cases: [{
            caseNumber: `${Math.floor(Math.random() * 100)}-CV-${Math.floor(Math.random() * 10000)}`,
            court: 'Superior Court',
            caseType: 'Civil',
            filingDate: '2018-11-10',
            status: 'Closed'
          }]
        }
      };
    }
    return { source: 'Public Court Records', confidence: 0, cost: 0, data: null };
  }

  async searchFreeSocialMedia(query) {
    console.log('📱 Free Social Media (FREE)');
    await this.delay(300);
    
    const hasData = Math.random() > 0.4;
    if (hasData) {
      return {
        source: 'Social Media Public Profiles',
        confidence: 0.70,
        cost: 0,
        data: {
          profiles: [{
            platform: 'LinkedIn',
            profileName: `${query.firstName} ${query.lastName}`,
            profileUrl: `https://linkedin.com/in/${query.firstName?.toLowerCase()}-${query.lastName?.toLowerCase()}`,
            isPublic: true,
            lastActivity: '2024-01-15'
          }]
        }
      };
    }
    return { source: 'Social Media Public Profiles', confidence: 0, cost: 0, data: null };
  }

  async searchFreeDirectories(query) {
    console.log('📞 Free Directories (FREE)');
    await this.delay(350);
    
    const hasData = Math.random() > 0.5;
    if (hasData) {
      return {
        source: 'Free Directory Services',
        confidence: 0.65,
        cost: 0,
        data: {
          listings: [{
            name: `${query.firstName} ${query.lastName}`,
            phone: query.phone || `(${Math.floor(Math.random() * 800) + 200}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
            address: `${Math.floor(Math.random() * 9999)} Directory St`,
            isActive: Math.random() > 0.3
          }]
        }
      };
    }
    return { source: 'Free Directory Services', confidence: 0, cost: 0, data: null };
  }

  // PREMIUM TIER SEARCH METHODS
  async searchEnhancedPropertyData(query) {
    console.log('🏠 Enhanced Property Data (PREMIUM - $0.10)');
    await this.delay(700);
    
    const hasData = Math.random() > 0.2;
    if (hasData) {
      return {
        source: 'Enhanced Property Data (Zillow)',
        confidence: 0.92,
        cost: 0.10,
        data: {
          properties: [{
            address: `${Math.floor(Math.random() * 9999)} Premium Ave`,
            city: query.city || 'Premium City',
            state: query.state || 'CA',
            currentValue: Math.floor(Math.random() * 600000) + 400000,
            priceHistory: [
              { date: '2020-01-15', price: 450000 },
              { date: '2022-06-20', price: 520000 }
            ],
            propertyDetails: {
              bedrooms: Math.floor(Math.random() * 4) + 2,
              bathrooms: Math.floor(Math.random() * 3) + 1,
              squareFeet: Math.floor(Math.random() * 2000) + 1500
            }
          }]
        }
      };
    }
    return { source: 'Enhanced Property Data', confidence: 0, cost: 0.10, data: null };
  }

  async searchProfessionalLicenses(query) {
    console.log('🎓 Professional Licenses (PREMIUM - $0.15)');
    await this.delay(500);
    
    const hasData = Math.random() > 0.7;
    if (hasData) {
      return {
        source: 'Professional Licenses',
        confidence: 0.88,
        cost: 0.15,
        data: {
          licenses: [{
            licenseType: 'Real Estate License',
            licenseNumber: `RE${Math.floor(Math.random() * 1000000)}`,
            issuingState: query.state || 'CA',
            status: 'Active',
            expirationDate: '2025-12-31'
          }]
        }
      };
    }
    return { source: 'Professional Licenses', confidence: 0, cost: 0.15, data: null };
  }

  async searchAdvancedSocial(query) {
    console.log('📊 Advanced Social Analytics (PREMIUM - $0.15)');
    await this.delay(800);
    
    const hasData = Math.random() > 0.5;
    if (hasData) {
      return {
        source: 'Advanced Social Analytics',
        confidence: 0.75,
        cost: 0.15,
        data: {
          socialAnalytics: {
            platforms: ['LinkedIn', 'Facebook', 'Instagram'],
            totalFollowers: Math.floor(Math.random() * 5000) + 500,
            engagementRate: (Math.random() * 10 + 2).toFixed(2),
            lastActivity: '2024-01-20',
            professionalNetwork: Math.floor(Math.random() * 1000) + 100
          }
        }
      };
    }
    return { source: 'Advanced Social Analytics', confidence: 0, cost: 0.15, data: null };
  }

  async verifyContactInformation(query) {
    console.log('✅ Contact Verification (PREMIUM - $0.05)');
    await this.delay(300);
    
    return {
      source: 'Contact Verification',
      confidence: 0.95,
      cost: 0.05,
      data: {
        verification: {
          phoneVerified: query.phone ? Math.random() > 0.3 : false,
          emailVerified: query.email ? Math.random() > 0.2 : false,
          addressVerified: (query.city && query.state) ? Math.random() > 0.4 : false
        }
      }
    };
  }

  // ENTERPRISE TIER SEARCH METHODS
  async searchFinancialData(query) {
    console.log('💰 Financial Data (ENTERPRISE - $0.50)');
    await this.delay(1000);
    
    const hasData = Math.random() > 0.6;
    if (hasData) {
      return {
        source: 'Financial Data',
        confidence: 0.85,
        cost: 0.50,
        data: {
          financialProfile: {
            estimatedIncome: Math.floor(Math.random() * 200000) + 50000,
            creditScoreRange: '700-750',
            bankruptcyHistory: Math.random() > 0.9,
            taxLiens: Math.random() > 0.8,
            estimatedNetWorth: Math.floor(Math.random() * 1000000) + 100000
          }
        }
      };
    }
    return { source: 'Financial Data', confidence: 0, cost: 0.50, data: null };
  }

  async searchAdvancedBackground(query) {
    console.log('🔍 Advanced Background Check (ENTERPRISE - $0.25)');
    await this.delay(900);
    
    const hasData = Math.random() > 0.7;
    if (hasData) {
      return {
        source: 'Advanced Background Check',
        confidence: 0.90,
        cost: 0.25,
        data: {
          backgroundCheck: {
            criminalHistory: Math.random() > 0.9,
            employmentHistory: [
              {
                company: `${query.lastName} Industries`,
                position: 'Senior Manager',
                duration: '2018-2023'
              }
            ],
            educationHistory: [
              {
                institution: 'State University',
                degree: 'Bachelor of Science',
                graduationYear: 2015
              }
            ]
          }
        }
      };
    }
    return { source: 'Advanced Background Check', confidence: 0, cost: 0.25, data: null };
  }

  async searchRealTimeData(query) {
    console.log('⚡ Real-time Data Updates (ENTERPRISE - $0.30)');
    await this.delay(400);
    
    return {
      source: 'Real-time Data Updates',
      confidence: 0.98,
      cost: 0.30,
      data: {
        realTimeUpdates: {
          lastSeen: new Date().toISOString(),
          currentLocation: query.city || 'Unknown',
          recentActivity: 'Property search conducted',
          dataFreshness: 0 // Real-time
        }
      }
    };
  }

  processSearchResults(searchResults, query, tier) {
    const processedData = {
      addresses: [],
      phoneNumbers: [],
      emailAddresses: [],
      propertyRecords: [],
      businessAffiliations: [],
      socialProfiles: [],
      professionalLicenses: [],
      financialData: null,
      backgroundCheck: null,
      sources: [],
      totalCost: 0,
      freeSources: 0,
      paidSources: 0
    };

    searchResults.forEach((result) => {
      if (result.status === 'fulfilled' && result.value?.data) {
        const sourceData = result.value;
        processedData.sources.push(sourceData.source);
        processedData.totalCost += sourceData.cost || 0;
        
        if (sourceData.cost === 0) {
          processedData.freeSources++;
        } else {
          processedData.paidSources++;
        }

        // Process specific data types
        if (sourceData.data.properties) {
          processedData.propertyRecords.push(...sourceData.data.properties);
        }
        if (sourceData.data.businesses) {
          processedData.businessAffiliations.push(...sourceData.data.businesses);
        }
        if (sourceData.data.profiles) {
          processedData.socialProfiles.push(...sourceData.data.profiles);
        }
        if (sourceData.data.licenses) {
          processedData.professionalLicenses.push(...sourceData.data.licenses);
        }
        if (sourceData.data.financialProfile) {
          processedData.financialData = sourceData.data.financialProfile;
        }
        if (sourceData.data.backgroundCheck) {
          processedData.backgroundCheck = sourceData.data.backgroundCheck;
        }
        if (sourceData.data.listings) {
          sourceData.data.listings.forEach(listing => {
            if (listing.phone) {
              processedData.phoneNumbers.push({
                number: listing.phone,
                type: 'unknown',
                isActive: listing.isActive,
                confidence: sourceData.confidence,
                verified: false
              });
            }
            if (listing.address) {
              processedData.addresses.push({
                address: listing.address,
                city: query.city || 'Unknown',
                state: query.state || 'Unknown',
                zip: '00000',
                type: 'current',
                confidence: sourceData.confidence,
                verified: false
              });
            }
          });
        }
      }
    });

    return processedData;
  }

  createOptimizedPersonResults(processedData, query, tier) {
    const result = {
      id: `optimized_${Date.now()}`,
      confidence: this.calculateOverallConfidence(processedData),
      tier: tier,
      fullName: `${query.firstName} ${query.lastName}`,
      firstName: query.firstName,
      lastName: query.lastName,
      addresses: processedData.addresses,
      phoneNumbers: processedData.phoneNumbers,
      emailAddresses: processedData.emailAddresses,
      propertyRecords: processedData.propertyRecords,
      businessAffiliations: processedData.businessAffiliations,
      socialProfiles: processedData.socialProfiles,
      professionalLicenses: processedData.professionalLicenses || [],
      financialData: processedData.financialData,
      backgroundCheck: processedData.backgroundCheck,
      searchDate: new Date(),
      dataFreshness: Math.floor(Math.random() * 30),
      sources: processedData.sources,
      costBreakdown: {
        freeSources: processedData.freeSources,
        paidSources: processedData.paidSources,
        totalCost: processedData.totalCost
      }
    };

    return [result];
  }

  calculateOverallConfidence(processedData) {
    const dataPoints = [
      processedData.addresses.length,
      processedData.phoneNumbers.length,
      processedData.propertyRecords.length,
      processedData.businessAffiliations.length,
      processedData.socialProfiles.length,
      processedData.professionalLicenses.length,
      processedData.financialData ? 1 : 0,
      processedData.backgroundCheck ? 1 : 0
    ];
    
    const totalDataPoints = dataPoints.reduce((sum, count) => sum + count, 0);
    return Math.min(0.95, 0.3 + (totalDataPoints * 0.08));
  }

  calculateSearchCost(results, tier) {
    return results.reduce((total, result) => total + result.costBreakdown.totalCost, 0);
  }

  checkSearchQuota(quota, requestedTier) {
    if (quota.dailyUsed >= quota.dailyLimit) {
      return {
        canSearch: false,
        quotaExceeded: true,
        reason: `Daily limit reached (${quota.dailyLimit}). Upgrade for more searches.`
      };
    }

    if (requestedTier !== quota.tier && quota.tier === 'free') {
      return {
        canSearch: false,
        upgradeRequired: true,
        reason: `${requestedTier} tier features require subscription upgrade.`
      };
    }

    return { canSearch: true };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getSearchTiers() {
    return this.SEARCH_TIERS;
  }

  calculateUpgradeCost(currentTier, targetTier) {
    const current = this.SEARCH_TIERS[currentTier];
    const target = this.SEARCH_TIERS[targetTier];
    return target.monthlyCost - current.monthlyCost;
  }
}

// ===================================================================
// TEST EXECUTION
// ===================================================================

const testQueries = [
  {
    name: 'Complete Profile Test',
    query: {
      firstName: 'John',
      lastName: 'Smith',
      city: 'Los Angeles',
      state: 'CA',
      email: '<EMAIL>',
      phone: '(*************'
    }
  },
  {
    name: 'Business Owner Test',
    query: {
      firstName: 'Maria',
      lastName: 'Garcia',
      city: 'Houston',
      state: 'TX',
      email: '<EMAIL>'
    }
  },
  {
    name: 'Minimal Data Test',
    query: {
      firstName: 'Robert',
      lastName: 'Johnson'
    }
  }
];

async function runOptimizedAISearchTests() {
  console.log('\n🚀 STARTING OPTIMIZED AI SEARCH TESTS');
  console.log('=====================================\n');

  const aiSearchTester = new OptimizedAISearchTester();
  const testResults = [];

  // Display pricing tiers
  console.log('💰 PRICING TIERS:');
  const tiers = aiSearchTester.getSearchTiers();
  Object.entries(tiers).forEach(([tierName, tier]) => {
    console.log(`\n📊 ${tier.name}:`);
    console.log(`   - Cost: $${tier.monthlyCost}/month`);
    console.log(`   - Daily Limit: ${tier.dailyLimit} searches`);
    console.log(`   - Data Sources: ${tier.dataSources.length}`);
    console.log(`   - Features: ${tier.features.join(', ')}`);
  });

  // Test each tier with different queries
  const testScenarios = [
    { tier: 'free', userId: 'free-user-123' },
    { tier: 'premium', userId: 'premium-user-456' },
    { tier: 'enterprise', userId: 'enterprise-user-789' }
  ];

  for (const scenario of testScenarios) {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🧪 TESTING ${scenario.tier.toUpperCase()} TIER`);
    console.log(`${'='.repeat(80)}`);

    for (let i = 0; i < testQueries.length; i++) {
      const test = testQueries[i];
      console.log(`\n📋 Test ${i + 1}: ${test.name} (${scenario.tier} tier)`);
      console.log('-'.repeat(60));

      const startTime = Date.now();

      try {
        const result = await aiSearchTester.searchPerson(
          scenario.userId,
          test.query,
          scenario.tier
        );

        const duration = Date.now() - startTime;

        testResults.push({
          tier: scenario.tier,
          testName: test.name,
          success: result.success,
          duration,
          resultCount: result.results?.length || 0,
          cost: result.results?.[0]?.costBreakdown?.totalCost || 0,
          quotaExceeded: result.quotaExceeded || false,
          upgradeRequired: result.upgradeRequired || false,
          quota: result.quota
        });

        if (result.success && result.results) {
          const person = result.results[0];
          console.log(`\n📊 SEARCH RESULTS:`);
          console.log(`   - Confidence: ${(person.confidence * 100).toFixed(1)}%`);
          console.log(`   - Data Sources: ${person.sources.length} (${person.costBreakdown.freeSources} free, ${person.costBreakdown.paidSources} paid)`);
          console.log(`   - Search Cost: $${person.costBreakdown.totalCost.toFixed(2)}`);
          console.log(`   - Duration: ${duration}ms`);

          console.log(`\n👤 PERSON DATA FOUND:`);
          console.log(`   - Addresses: ${person.addresses.length}`);
          console.log(`   - Phone Numbers: ${person.phoneNumbers.length}`);
          console.log(`   - Property Records: ${person.propertyRecords.length}`);
          console.log(`   - Business Affiliations: ${person.businessAffiliations.length}`);
          console.log(`   - Social Profiles: ${person.socialProfiles.length}`);

          if (scenario.tier === 'premium' || scenario.tier === 'enterprise') {
            console.log(`   - Professional Licenses: ${person.professionalLicenses.length}`);
          }

          if (scenario.tier === 'enterprise') {
            console.log(`   - Financial Data: ${person.financialData ? 'Available' : 'Not Found'}`);
            console.log(`   - Background Check: ${person.backgroundCheck ? 'Available' : 'Not Found'}`);
          }

          console.log(`\n📈 QUOTA STATUS:`);
          console.log(`   - Daily Usage: ${result.quota.dailyUsed}/${result.quota.dailyLimit}`);
          console.log(`   - Total Cost: $${result.quota.totalCost.toFixed(2)}`);

        } else {
          console.log(`\n❌ SEARCH FAILED:`);
          console.log(`   - Error: ${result.error}`);
          console.log(`   - Quota Exceeded: ${result.quotaExceeded}`);
          console.log(`   - Upgrade Required: ${result.upgradeRequired}`);
        }

      } catch (error) {
        console.error(`❌ Test failed with error:`, error);
        testResults.push({
          tier: scenario.tier,
          testName: test.name,
          success: false,
          duration: Date.now() - startTime,
          error: error.message
        });
      }

      // Brief pause between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // Generate comprehensive comparison report
  console.log(`\n${'='.repeat(100)}`);
  console.log('📊 COMPREHENSIVE TIER COMPARISON REPORT');
  console.log(`${'='.repeat(100)}`);

  const tierResults = {};
  testResults.forEach(result => {
    if (!tierResults[result.tier]) {
      tierResults[result.tier] = {
        totalTests: 0,
        successfulTests: 0,
        totalCost: 0,
        avgDuration: 0,
        avgConfidence: 0,
        totalDataPoints: 0
      };
    }

    const tier = tierResults[result.tier];
    tier.totalTests++;

    if (result.success) {
      tier.successfulTests++;
      tier.totalCost += result.cost || 0;
      tier.avgDuration += result.duration;
    }
  });

  // Calculate averages
  Object.keys(tierResults).forEach(tierName => {
    const tier = tierResults[tierName];
    tier.avgDuration = tier.avgDuration / tier.totalTests;
    tier.successRate = (tier.successfulTests / tier.totalTests * 100).toFixed(1);
    tier.avgCostPerSearch = tier.successfulTests > 0 ? (tier.totalCost / tier.successfulTests).toFixed(2) : '0.00';
  });

  console.log('\n📈 TIER PERFORMANCE COMPARISON:');
  console.log('================================');

  Object.entries(tierResults).forEach(([tierName, stats]) => {
    const tierInfo = aiSearchTester.getSearchTiers()[tierName];
    console.log(`\n🎯 ${tierName.toUpperCase()} TIER:`);
    console.log(`   Monthly Cost: $${tierInfo.monthlyCost}`);
    console.log(`   Daily Limit: ${tierInfo.dailyLimit} searches`);
    console.log(`   Success Rate: ${stats.successRate}%`);
    console.log(`   Avg Duration: ${stats.avgDuration.toFixed(0)}ms`);
    console.log(`   Avg Cost per Search: $${stats.avgCostPerSearch}`);
    console.log(`   Data Sources: ${tierInfo.dataSources.length}`);
  });

  console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
  console.log('=================================');
  console.log('✅ FREE TIER: Perfect for getting started - 5 searches/day with government data');
  console.log('✅ PREMIUM TIER: Best value for regular users - 50 searches/day with enhanced data');
  console.log('✅ ENTERPRISE TIER: For power users - unlimited searches with financial data');
  console.log('');
  console.log('🎯 KEY ADVANTAGES OF OPTIMIZED SYSTEM:');
  console.log('   1. START FREE: No upfront costs, immediate value');
  console.log('   2. SCALE AFFORDABLY: Pay only for what you need');
  console.log('   3. REAL DATA SOURCES: Government APIs, business records, social media');
  console.log('   4. TRANSPARENT PRICING: Clear cost breakdown per search');
  console.log('   5. FLEXIBLE TIERS: Easy upgrades as needs grow');

  console.log('\n🚀 NEXT STEPS FOR IMPLEMENTATION:');
  console.log('=================================');
  console.log('1. Implement free government API integrations first');
  console.log('2. Add premium data sources with clear cost tracking');
  console.log('3. Create subscription management system');
  console.log('4. Add caching to reduce repeated search costs');
  console.log('5. Implement usage analytics and optimization suggestions');

  return testResults;
}

// Run the optimized AI search tests
runOptimizedAISearchTests().catch(console.error);
