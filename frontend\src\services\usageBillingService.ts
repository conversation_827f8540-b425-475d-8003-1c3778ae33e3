import { supabase } from '../lib/supabase'

export interface UsageEvent {
  id?: string
  organization_id: string
  meter_code: string
  quantity: number
  user_id?: string
  resource_id?: string
  resource_type?: string
  metadata?: Record<string, any>
  event_timestamp?: string
  rate_applied?: number
  cost?: number
}

export interface BillingMeter {
  meter_code: string
  meter_name: string
  description: string
  unit_type: string
  unit_label: string
  base_rate: number
  free_tier_limit: number
  overage_policy: 'block' | 'charge' | 'warn'
  overage_rate?: number
}

export interface UsageAggregate {
  organization_id: string
  meter_code: string
  billing_period_start: string
  billing_period_end: string
  total_quantity: number
  billable_quantity: number
  included_quantity: number
  overage_quantity: number
  total_cost: number
  volume_discount_amount: number
}

export interface UpsellOpportunity {
  id: string
  organization_id: string
  current_plan: string
  recommended_plan: string
  potential_savings: number
  trigger_metric: string
  trigger_value: number
  status: 'active' | 'presented' | 'accepted' | 'declined' | 'expired'
  discount_percent?: number
  expires_at?: string
}

class UsageBillingService {
  /**
   * Record a usage event for billing
   */
  async recordUsage(event: Omit<UsageEvent, 'id'>): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('record_usage_event', {
        p_organization_id: event.organization_id,
        p_meter_code: event.meter_code,
        p_quantity: event.quantity,
        p_user_id: event.user_id || null,
        p_resource_id: event.resource_id || null,
        p_resource_type: event.resource_type || null,
        p_metadata: event.metadata || {}
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error recording usage:', error)
      return null
    }
  }

  /**
   * Record API call usage
   */
  async recordAPICall(organizationId: string, userId?: string, endpoint?: string): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'api_calls',
      quantity: 1,
      user_id: userId,
      resource_type: 'api_endpoint',
      resource_id: endpoint,
      metadata: { endpoint, timestamp: new Date().toISOString() }
    })
  }

  /**
   * Record storage usage
   */
  async recordStorageUsage(organizationId: string, sizeInBytes: number, fileType?: string): Promise<void> {
    const sizeInGB = sizeInBytes / (1024 * 1024 * 1024)
    
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'storage_gb',
      quantity: sizeInGB,
      resource_type: 'file_storage',
      metadata: { file_type: fileType, size_bytes: sizeInBytes }
    })
  }

  /**
   * Record document processing usage
   */
  async recordDocumentProcessing(
    organizationId: string, 
    userId: string, 
    documentId: string,
    processingType: string
  ): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'document_processing',
      quantity: 1,
      user_id: userId,
      resource_type: 'document',
      resource_id: documentId,
      metadata: { processing_type: processingType }
    })
  }

  /**
   * Record SMS message usage
   */
  async recordSMSUsage(organizationId: string, phoneNumber: string, messageLength: number): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'sms_messages',
      quantity: 1,
      resource_type: 'sms',
      metadata: { phone_number: phoneNumber, message_length: messageLength }
    })
  }

  /**
   * Record email usage
   */
  async recordEmailUsage(organizationId: string, recipientCount: number, emailType: string): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'email_sends',
      quantity: recipientCount,
      resource_type: 'email',
      metadata: { email_type: emailType, recipient_count: recipientCount }
    })
  }

  /**
   * Record data export usage
   */
  async recordDataExport(
    organizationId: string, 
    userId: string, 
    exportType: string,
    recordCount: number
  ): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'data_exports',
      quantity: 1,
      user_id: userId,
      resource_type: 'export',
      metadata: { export_type: exportType, record_count: recordCount }
    })
  }

  /**
   * Record advanced search usage
   */
  async recordAdvancedSearch(
    organizationId: string, 
    userId: string, 
    searchQuery: string,
    resultCount: number
  ): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'advanced_search',
      quantity: 1,
      user_id: userId,
      resource_type: 'search',
      metadata: { 
        query_complexity: searchQuery.length > 100 ? 'complex' : 'simple',
        result_count: resultCount
      }
    })
  }

  /**
   * Record report generation usage
   */
  async recordReportGeneration(
    organizationId: string, 
    userId: string, 
    reportType: string,
    dataPoints: number
  ): Promise<void> {
    await this.recordUsage({
      organization_id: organizationId,
      meter_code: 'report_generation',
      quantity: 1,
      user_id: userId,
      resource_type: 'report',
      metadata: { report_type: reportType, data_points: dataPoints }
    })
  }

  /**
   * Get current usage for organization
   */
  async getCurrentUsage(organizationId: string, period?: string): Promise<UsageAggregate[]> {
    try {
      const periodStart = period || new Date().toISOString().slice(0, 7) + '-01' // Current month
      
      const { data, error } = await supabase
        .from('usage_aggregates')
        .select('*')
        .eq('organization_id', organizationId)
        .gte('billing_period_start', periodStart)
        .order('meter_code')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting current usage:', error)
      return []
    }
  }

  /**
   * Get usage history
   */
  async getUsageHistory(
    organizationId: string, 
    meterCode?: string,
    months: number = 6
  ): Promise<UsageAggregate[]> {
    try {
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - months)
      
      let query = supabase
        .from('usage_aggregates')
        .select('*')
        .eq('organization_id', organizationId)
        .gte('billing_period_start', startDate.toISOString())
        .order('billing_period_start', { ascending: false })

      if (meterCode) {
        query = query.eq('meter_code', meterCode)
      }

      const { data, error } = await query
      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting usage history:', error)
      return []
    }
  }

  /**
   * Check if usage would exceed limits
   */
  async checkUsageLimit(
    organizationId: string, 
    meterCode: string, 
    requestedQuantity: number
  ): Promise<{
    allowed: boolean
    current: number
    limit: number
    after_addition: number
    overage_cost?: number
    warning?: string
  }> {
    try {
      // Get current usage for this month
      const currentUsage = await this.getCurrentUsage(organizationId)
      const meterUsage = currentUsage.find(u => u.meter_code === meterCode)
      const current = meterUsage?.total_quantity || 0

      // Get plan limits
      const { data: subscription } = await supabase
        .from('organization_subscriptions')
        .select(`
          plan_id,
          plan_meter_configs!inner(included_units, rate_override, hard_limit, soft_limit)
        `)
        .eq('organization_id', organizationId)
        .eq('plan_meter_configs.meter_code', meterCode)
        .single()

      if (!subscription) {
        return { allowed: false, current, limit: 0, after_addition: current + requestedQuantity }
      }

      const config = subscription.plan_meter_configs
      const limit = config.hard_limit || config.included_units || 0
      const afterAddition = current + requestedQuantity

      // Check hard limit
      if (config.hard_limit && afterAddition > config.hard_limit) {
        return {
          allowed: false,
          current,
          limit: config.hard_limit,
          after_addition: afterAddition,
          warning: 'Hard limit would be exceeded'
        }
      }

      // Calculate overage cost if applicable
      let overageCost = 0
      if (afterAddition > config.included_units) {
        const overageQuantity = afterAddition - config.included_units
        const rate = config.rate_override || 0
        overageCost = overageQuantity * rate
      }

      // Check soft limit for warnings
      let warning
      if (config.soft_limit && afterAddition > config.soft_limit) {
        warning = 'Soft limit exceeded - consider upgrading'
      }

      return {
        allowed: true,
        current,
        limit,
        after_addition: afterAddition,
        overage_cost: overageCost,
        warning
      }
    } catch (error) {
      console.error('Error checking usage limit:', error)
      return { allowed: false, current: 0, limit: 0, after_addition: requestedQuantity }
    }
  }

  /**
   * Get billing meters
   */
  async getBillingMeters(): Promise<BillingMeter[]> {
    try {
      const { data, error } = await supabase
        .from('billing_meters')
        .select('*')
        .eq('is_active', true)
        .order('meter_code')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting billing meters:', error)
      return []
    }
  }

  /**
   * Get upsell opportunities
   */
  async getUpsellOpportunities(organizationId: string): Promise<UpsellOpportunity[]> {
    try {
      const { data, error } = await supabase
        .from('upsell_opportunities')
        .select('*')
        .eq('organization_id', organizationId)
        .in('status', ['active', 'presented'])
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error getting upsell opportunities:', error)
      return []
    }
  }

  /**
   * Respond to upsell opportunity
   */
  async respondToUpsell(
    opportunityId: string, 
    response: 'accepted' | 'declined'
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('upsell_opportunities')
        .update({
          status: response,
          responded_at: new Date().toISOString()
        })
        .eq('id', opportunityId)

      if (error) throw error
      return true
    } catch (error) {
      console.error('Error responding to upsell:', error)
      return false
    }
  }

  /**
   * Calculate projected costs
   */
  async calculateProjectedCosts(
    organizationId: string,
    projectedUsage: Record<string, number>
  ): Promise<{
    total_cost: number
    breakdown: Array<{
      meter_code: string
      quantity: number
      included: number
      overage: number
      cost: number
    }>
  }> {
    try {
      const meters = await this.getBillingMeters()
      const breakdown = []
      let totalCost = 0

      // Get plan configuration
      const { data: subscription } = await supabase
        .from('organization_subscriptions')
        .select(`
          plan_id,
          plan_meter_configs(meter_code, included_units, rate_override)
        `)
        .eq('organization_id', organizationId)
        .single()

      for (const [meterCode, quantity] of Object.entries(projectedUsage)) {
        const meter = meters.find(m => m.meter_code === meterCode)
        if (!meter) continue

        const config = subscription?.plan_meter_configs?.find(c => c.meter_code === meterCode)
        const includedUnits = config?.included_units || 0
        const rate = config?.rate_override || meter.base_rate

        const overage = Math.max(0, quantity - includedUnits)
        const cost = overage * rate

        breakdown.push({
          meter_code: meterCode,
          quantity,
          included: Math.min(quantity, includedUnits),
          overage,
          cost
        })

        totalCost += cost
      }

      return { total_cost: totalCost, breakdown }
    } catch (error) {
      console.error('Error calculating projected costs:', error)
      return { total_cost: 0, breakdown: [] }
    }
  }

  /**
   * Generate usage report
   */
  async generateUsageReport(
    organizationId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    summary: {
      total_cost: number
      total_events: number
      top_meters: Array<{ meter_code: string; quantity: number; cost: number }>
    }
    details: UsageAggregate[]
  }> {
    try {
      // Get detailed usage
      const { data: details, error } = await supabase
        .from('usage_aggregates')
        .select('*')
        .eq('organization_id', organizationId)
        .gte('billing_period_start', startDate)
        .lte('billing_period_end', endDate)
        .order('billing_period_start')

      if (error) throw error

      // Calculate summary
      const totalCost = details?.reduce((sum, item) => sum + item.total_cost, 0) || 0
      const totalEvents = details?.reduce((sum, item) => sum + item.total_quantity, 0) || 0

      // Get top meters by cost
      const meterTotals = new Map()
      details?.forEach(item => {
        const existing = meterTotals.get(item.meter_code) || { quantity: 0, cost: 0 }
        meterTotals.set(item.meter_code, {
          meter_code: item.meter_code,
          quantity: existing.quantity + item.total_quantity,
          cost: existing.cost + item.total_cost
        })
      })

      const topMeters = Array.from(meterTotals.values())
        .sort((a, b) => b.cost - a.cost)
        .slice(0, 5)

      return {
        summary: {
          total_cost: totalCost,
          total_events: totalEvents,
          top_meters: topMeters
        },
        details: details || []
      }
    } catch (error) {
      console.error('Error generating usage report:', error)
      return {
        summary: { total_cost: 0, total_events: 0, top_meters: [] },
        details: []
      }
    }
  }
}

export const usageBillingService = new UsageBillingService()
