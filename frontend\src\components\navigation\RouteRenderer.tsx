import { lazy, Suspense } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { LoginForm } from '@/components/auth/LoginForm'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

// Lazy load components for better performance
const ClaimsDashboard = lazy(() => import('@/components/claims/ClaimsDashboard'))
const ClaimDetail = lazy(() => import('@/components/claims/ClaimDetail').then(m => ({ default: m.ClaimDetail })))
const BatchImport = lazy(() => import('@/components/batch/BatchImport'))
const AnalyticsDashboard = lazy(() => import('@/features/analytics').then(m => ({ default: m.AnalyticsDashboard })))
const WorkflowAutomation = lazy(() => import('@/features/workflow').then(m => ({ default: m.WorkflowAutomation })))
const UserManagement = lazy(() => import('@/features/admin').then(m => ({ default: m.UserManagement })))
const SystemSettings = lazy(() => import('@/features/admin').then(m => ({ default: m.SystemSettings })))
const GlobalSearch = lazy(() => import('@/features/search').then(m => ({ default: m.GlobalSearch })))
const MainDashboard = lazy(() => import('@/features/dashboard').then(m => ({ default: m.MainDashboard })))
const JuniorAgentWorkspace = lazy(() => import('@/features/junior-agent').then(m => ({ default: m.JuniorAgentWorkspace })))
const Week2TestIntegration = lazy(() => import('@/pages/Week2TestIntegration'))
const EnhancedDashboard = lazy(() => import('@/components/EnhancedDashboard'))
const DashboardDemo = lazy(() => import('@/pages/DashboardDemo'))
const ProductionAISearch = lazy(() => import('@/pages/ProductionAISearch'))
const AdminAISearchManagement = lazy(() => import('@/components/AdminAISearchManagement'))
const AgentAISearch = lazy(() => import('@/components/AgentAISearch'))
const OrganizationSettings = lazy(() => import('@/pages/OrganizationSettings').then(m => ({ default: m.OrganizationSettings })))

export type View = 'dashboard' | 'enhanced-dashboard' | 'claims' | 'claim-detail' | 'claimants' | 'payments' | 'admin' | 'compliance' | 'finance' | 'batch-import' | 'analytics' | 'workflow' | 'users' | 'settings' | 'search' | 'ai-discovery' | 'dashboard-demo' | 'login' | 'week2-test' | 'production-ai-search' | 'ai-search-admin' | 'agent-ai-search' | 'organization'

interface RouteRendererProps {
  currentView: View
  selectedClaimId?: string | null
  onViewChange: (view: View) => void
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <p className="text-sm text-gray-600 dark:text-gray-400">Loading component...</p>
      </div>
    </div>
  )
}

export function RouteRenderer({ currentView, selectedClaimId, onViewChange }: RouteRendererProps) {
  const { user } = useAuth()

  // Helper function to get search limits based on role
  const getRoleBasedSearchLimit = (role?: string): number => {
    switch (role) {
      case 'admin': return 999
      case 'senior_agent': return 100
      case 'compliance': return 50
      case 'junior_agent': return 10
      case 'contractor': return 5
      case 'finance': return 0
      default: return 10
    }
  }

  const renderRoute = () => {
    switch (currentView) {
      case 'login':
        return <LoginForm />

      case 'dashboard':
        if (user?.role === 'junior_agent') {
          return (
            <JuniorAgentWorkspace 
              agentId={user?.id || user?.email || 'unknown'}
              agentName={user?.email?.split('@')[0] || 'Agent'}
            />
          )
        }
        return <MainDashboard />

      case 'claims':
        return <ClaimsDashboard />

      case 'claim-detail':
        return selectedClaimId ? <ClaimDetail claimId={selectedClaimId} /> : <ClaimsDashboard />

      case 'batch-import':
        return <BatchImport />

      case 'analytics':
        return <AnalyticsDashboard />

      case 'workflow':
        return <WorkflowAutomation />

      case 'search':
        return <GlobalSearch />

      case 'users':
        return <UserManagement />

      case 'settings':
        return <SystemSettings />

      case 'organization':
        return <OrganizationSettings />

      case 'ai-discovery':
        return <Week2TestIntegration />

      case 'enhanced-dashboard':
        return (
          <EnhancedDashboard 
            userId={user?.id || 'demo-user'} 
            userRole={user?.role || 'admin'}
            onNavigate={(section) => {
              if (section === 'batch-upload') onViewChange('batch-import')
              else if (section === 'ai-discovery') onViewChange('ai-discovery')
              else if (section === 'monitoring') onViewChange('analytics')
              else if (section === 'reports') onViewChange('analytics')
            }}
          />
        )

      case 'dashboard-demo':
        return <DashboardDemo />

      case 'week2-test':
        return <Week2TestIntegration />

      case 'production-ai-search':
        return <ProductionAISearch />

      case 'ai-search-admin':
        return <AdminAISearchManagement />

      case 'agent-ai-search':
        return user ? (
          <AgentAISearch 
            agentId={user?.id || user?.email || 'unknown'}
            userRole={user?.role || 'junior_agent'}
            maxSearches={getRoleBasedSearchLimit(user?.role)}
            tenantId={user?.tenantId}
          />
        ) : <div>Please log in to access AI Search</div>

      // Placeholder routes for future features
      case 'claimants':
      case 'payments':
      case 'admin':
      case 'compliance':
      case 'finance':
        return (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8 text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Coming Soon
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                This feature is under development
              </p>
            </CardContent>
          </Card>
        )

      default:
        return <MainDashboard />
    }
  }

  return (
    <Suspense fallback={<LoadingFallback />}>
      {renderRoute()}
    </Suspense>
  )
}
