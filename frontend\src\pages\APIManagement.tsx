import React, { useState, useEffect } from 'react'
import { <PERSON>, Plus, <PERSON><PERSON>, <PERSON>, <PERSON>Off, Trash2, <PERSON>ting<PERSON>, Webhook, Activity } from 'lucide-react'

interface APIKey {
  id: string
  name: string
  key: string
  permissions: string[]
  last_used: string | null
  created_at: string
  expires_at: string | null
  is_active: boolean
}

interface WebhookEndpoint {
  id: string
  name: string
  url: string
  events: string[]
  secret: string
  is_active: boolean
  last_triggered: string | null
  success_rate: number
}

export const APIManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'keys' | 'webhooks' | 'logs'>('keys')
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([])
  const [showCreateKey, setShowCreateKey] = useState(false)
  const [showCreateWebhook, setShowCreateWebhook] = useState(false)
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())

  useEffect(() => {
    loadAPIKeys()
    loadWebhooks()
  }, [])

  const loadAPIKeys = async () => {
    // Mock data - in real implementation, load from database
    const mockKeys: APIKey[] = [
      {
        id: '1',
        name: 'Production API',
        key: 'ahp_live_1234567890abcdef',
        permissions: ['claims:read', 'claims:write', 'analytics:read'],
        last_used: '2024-01-15T10:30:00Z',
        created_at: '2024-01-01T00:00:00Z',
        expires_at: null,
        is_active: true
      },
      {
        id: '2',
        name: 'Analytics Dashboard',
        key: 'ahp_live_abcdef1234567890',
        permissions: ['analytics:read', 'reports:read'],
        last_used: '2024-01-14T15:45:00Z',
        created_at: '2024-01-05T00:00:00Z',
        expires_at: '2024-12-31T23:59:59Z',
        is_active: true
      }
    ]
    setApiKeys(mockKeys)
  }

  const loadWebhooks = async () => {
    // Mock data - in real implementation, load from database
    const mockWebhooks: WebhookEndpoint[] = [
      {
        id: '1',
        name: 'CRM Integration',
        url: 'https://api.yourcrm.com/webhooks/assethunter',
        events: ['claim.created', 'claim.updated', 'payment.received'],
        secret: 'whsec_1234567890abcdef',
        is_active: true,
        last_triggered: '2024-01-15T09:15:00Z',
        success_rate: 98.5
      },
      {
        id: '2',
        name: 'Slack Notifications',
        url: '*****************************************************************************',
        events: ['claim.completed', 'payment.received'],
        secret: 'whsec_abcdef1234567890',
        is_active: true,
        last_triggered: '2024-01-14T16:30:00Z',
        success_rate: 100
      }
    ]
    setWebhooks(mockWebhooks)
  }

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys)
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId)
    } else {
      newVisible.add(keyId)
    }
    setVisibleKeys(newVisible)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Show toast notification
  }

  const maskKey = (key: string) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 4)
  }

  const renderAPIKeys = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">API Keys</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Manage API keys for programmatic access to AssetHunterPro
          </p>
        </div>
        <button
          onClick={() => setShowCreateKey(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create API Key
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-4">
            {apiKeys.map((apiKey) => (
              <div key={apiKey.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {apiKey.name}
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        apiKey.is_active 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      }`}>
                        {apiKey.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                      <span>Created: {new Date(apiKey.created_at).toLocaleDateString()}</span>
                      {apiKey.last_used && (
                        <span>Last used: {new Date(apiKey.last_used).toLocaleDateString()}</span>
                      )}
                    </div>
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {visibleKeys.has(apiKey.id) ? apiKey.key : maskKey(apiKey.key)}
                        </code>
                        <button
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {visibleKeys.has(apiKey.id) ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                        <button
                          onClick={() => copyToClipboard(apiKey.key)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-1">
                        {apiKey.permissions.map((permission) => (
                          <span
                            key={permission}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                          >
                            {permission}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <Settings className="h-4 w-4" />
                    </button>
                    <button className="text-red-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderWebhooks = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Webhooks</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Configure webhooks to receive real-time notifications about events
          </p>
        </div>
        <button
          onClick={() => setShowCreateWebhook(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Webhook
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-4">
            {webhooks.map((webhook) => (
              <div key={webhook.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {webhook.name}
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        webhook.is_active 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      }`}>
                        {webhook.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {webhook.success_rate}% success rate
                      </span>
                    </div>
                    <div className="mt-2">
                      <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {webhook.url}
                      </code>
                    </div>
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-1">
                        {webhook.events.map((event) => (
                          <span
                            key={event}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                          >
                            {event}
                          </span>
                        ))}
                      </div>
                    </div>
                    {webhook.last_triggered && (
                      <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Last triggered: {new Date(webhook.last_triggered).toLocaleString()}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <Settings className="h-4 w-4" />
                    </button>
                    <button className="text-red-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderAPILogs = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">API Activity Logs</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Monitor API usage and webhook delivery attempts
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-12">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              API Logs Coming Soon
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Detailed API activity logs and webhook delivery monitoring will be available here.
            </p>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">API Management</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage API keys, webhooks, and monitor API usage
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'keys', label: 'API Keys', icon: Key },
            { id: 'webhooks', label: 'Webhooks', icon: Webhook },
            { id: 'logs', label: 'Activity Logs', icon: Activity }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'keys' && renderAPIKeys()}
      {activeTab === 'webhooks' && renderWebhooks()}
      {activeTab === 'logs' && renderAPILogs()}
    </div>
  )
}
