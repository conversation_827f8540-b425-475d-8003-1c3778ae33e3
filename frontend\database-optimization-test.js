// ===================================================================
// COMPREHENSIVE DATABASE OPTIMIZATION TEST SUITE
// AssetHunterPro - Production Readiness Database Testing
// ===================================================================

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Database configuration
const supabaseUrl = 'https://hhjfltgvnkeugftabzjl.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoamZsdGd2bmtldWdmdGFiempsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTQ2NTQsImV4cCI6MjA2Mzg5MDY1NH0.i7s3ValZ_I9ncz70AT4QmOCh7S-lGbtrKY7dFs16Q_Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test results storage
let testResults = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  },
  performance: {
    connectionTime: 0,
    queryTimes: [],
    averageQueryTime: 0,
    slowQueries: []
  },
  optimization: {
    indexUsage: [],
    tableStats: [],
    recommendations: []
  }
};

// Utility functions
function logTest(name, status, details = '', duration = 0) {
  const test = {
    name,
    status,
    details,
    duration,
    timestamp: new Date().toISOString()
  };
  
  testResults.tests.push(test);
  testResults.summary.total++;
  
  if (status === 'PASS') {
    testResults.summary.passed++;
    console.log(`✅ ${name} - ${details} (${duration}ms)`);
  } else if (status === 'FAIL') {
    testResults.summary.failed++;
    console.log(`❌ ${name} - ${details} (${duration}ms)`);
  } else if (status === 'WARN') {
    testResults.summary.warnings++;
    console.log(`⚠️  ${name} - ${details} (${duration}ms)`);
  }
}

function logPerformance(queryName, duration, query = '') {
  testResults.performance.queryTimes.push({ queryName, duration, query });
  
  if (duration > 1000) {
    testResults.performance.slowQueries.push({ queryName, duration, query });
    logTest(`Performance Warning: ${queryName}`, 'WARN', `Slow query detected: ${duration}ms`, duration);
  }
}

// ===================================================================
// 1. CONNECTION AND BASIC HEALTH TESTS
// ===================================================================

async function testDatabaseConnection() {
  console.log('\n🔍 TESTING DATABASE CONNECTION...');
  console.log('=====================================');
  
  const startTime = Date.now();
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    const connectionTime = Date.now() - startTime;
    testResults.performance.connectionTime = connectionTime;
    
    if (error) {
      logTest('Database Connection', 'FAIL', `Connection failed: ${error.message}`, connectionTime);
      return false;
    }
    
    logTest('Database Connection', 'PASS', 'Successfully connected to Supabase', connectionTime);
    return true;
  } catch (error) {
    const connectionTime = Date.now() - startTime;
    logTest('Database Connection', 'FAIL', `Connection error: ${error.message}`, connectionTime);
    return false;
  }
}

async function testTableExistence() {
  console.log('\n📋 TESTING TABLE EXISTENCE...');
  console.log('===============================');
  
  const expectedTables = [
    'users', 'organizations', 'claims', 'documents', 'activities',
    'subscription_plans', 'organization_subscriptions', 'user_roles',
    'permissions', 'role_permissions', 'user_permission_overrides',
    'usage_tracking', 'plan_features', 'billing_meters', 'usage_events',
    'usage_aggregates', 'mfa_settings', 'security_audit_logs',
    'user_sessions', 'superusers', 'platform_stats', 'system_health'
  ];
  
  for (const table of expectedTables) {
    const startTime = Date.now();
    
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      const duration = Date.now() - startTime;
      
      if (error) {
        logTest(`Table: ${table}`, 'FAIL', `Table not accessible: ${error.message}`, duration);
      } else {
        logTest(`Table: ${table}`, 'PASS', 'Table exists and accessible', duration);
      }
      
      logPerformance(`Table check: ${table}`, duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Table: ${table}`, 'FAIL', `Error checking table: ${error.message}`, duration);
    }
  }
}

// ===================================================================
// 2. PERFORMANCE TESTING
// ===================================================================

async function testQueryPerformance() {
  console.log('\n⚡ TESTING QUERY PERFORMANCE...');
  console.log('================================');
  
  const performanceTests = [
    {
      name: 'Simple User Count',
      query: () => supabase.from('users').select('count'),
      threshold: 500
    },
    {
      name: 'Organization with Users',
      query: () => supabase
        .from('organizations')
        .select(`
          *,
          users(count)
        `)
        .limit(10),
      threshold: 1000
    },
    {
      name: 'Claims with Documents',
      query: () => supabase
        .from('claims')
        .select(`
          *,
          documents(count),
          activities(count)
        `)
        .limit(20),
      threshold: 1500
    },
    {
      name: 'Complex Join Query',
      query: () => supabase
        .from('users')
        .select(`
          *,
          organization:organizations(*),
          claims(
            *,
            documents(count)
          )
        `)
        .limit(5),
      threshold: 2000
    }
  ];
  
  for (const test of performanceTests) {
    const startTime = Date.now();
    
    try {
      const { data, error } = await test.query();
      const duration = Date.now() - startTime;
      
      if (error) {
        logTest(`Performance: ${test.name}`, 'FAIL', `Query failed: ${error.message}`, duration);
      } else if (duration > test.threshold) {
        logTest(`Performance: ${test.name}`, 'WARN', `Query slow: ${duration}ms (threshold: ${test.threshold}ms)`, duration);
      } else {
        logTest(`Performance: ${test.name}`, 'PASS', `Query fast: ${duration}ms`, duration);
      }
      
      logPerformance(test.name, duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Performance: ${test.name}`, 'FAIL', `Query error: ${error.message}`, duration);
    }
  }
}

// ===================================================================
// 3. DATA INTEGRITY TESTS
// ===================================================================

async function testDataIntegrity() {
  console.log('\n🔒 TESTING DATA INTEGRITY...');
  console.log('==============================');

  // Test foreign key constraints
  const integrityTests = [
    {
      name: 'User-Organization Relationship',
      test: async () => {
        const { data: users } = await supabase
          .from('users')
          .select('id, organization_id')
          .not('organization_id', 'is', null)
          .limit(10);

        if (!users || users.length === 0) return { valid: true, details: 'No users with organizations found' };

        const orgIds = [...new Set(users.map(u => u.organization_id))];
        const { data: orgs } = await supabase
          .from('organizations')
          .select('id')
          .in('id', orgIds);

        const validOrgs = orgs?.map(o => o.id) || [];
        const invalidRefs = users.filter(u => !validOrgs.includes(u.organization_id));

        return {
          valid: invalidRefs.length === 0,
          details: invalidRefs.length > 0 ? `${invalidRefs.length} orphaned user records` : 'All references valid'
        };
      }
    },
    {
      name: 'Claims-User Assignment',
      test: async () => {
        const { data: claims } = await supabase
          .from('claims')
          .select('id, assigned_to')
          .not('assigned_to', 'is', null)
          .limit(10);

        if (!claims || claims.length === 0) return { valid: true, details: 'No assigned claims found' };

        const userIds = [...new Set(claims.map(c => c.assigned_to))];
        const { data: users } = await supabase
          .from('users')
          .select('id')
          .in('id', userIds);

        const validUsers = users?.map(u => u.id) || [];
        const invalidRefs = claims.filter(c => !validUsers.includes(c.assigned_to));

        return {
          valid: invalidRefs.length === 0,
          details: invalidRefs.length > 0 ? `${invalidRefs.length} claims assigned to non-existent users` : 'All assignments valid'
        };
      }
    }
  ];

  for (const test of integrityTests) {
    const startTime = Date.now();

    try {
      const result = await test.test();
      const duration = Date.now() - startTime;

      if (result.valid) {
        logTest(`Integrity: ${test.name}`, 'PASS', result.details, duration);
      } else {
        logTest(`Integrity: ${test.name}`, 'FAIL', result.details, duration);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Integrity: ${test.name}`, 'FAIL', `Test error: ${error.message}`, duration);
    }
  }
}

// ===================================================================
// 4. LOAD TESTING
// ===================================================================

async function testDatabaseLoad() {
  console.log('\n🏋️ TESTING DATABASE LOAD...');
  console.log('=============================');

  const loadTests = [
    {
      name: 'Concurrent User Queries',
      test: async () => {
        const concurrentQueries = Array(10).fill().map(() =>
          supabase.from('users').select('id, email, role').limit(5)
        );

        const startTime = Date.now();
        const results = await Promise.all(concurrentQueries);
        const duration = Date.now() - startTime;

        const failures = results.filter(r => r.error);
        return {
          success: failures.length === 0,
          duration,
          details: failures.length > 0 ? `${failures.length} queries failed` : `All 10 concurrent queries succeeded`
        };
      }
    },
    {
      name: 'Bulk Data Retrieval',
      test: async () => {
        const startTime = Date.now();
        const { data, error } = await supabase
          .from('claims')
          .select('*')
          .limit(100);

        const duration = Date.now() - startTime;

        return {
          success: !error,
          duration,
          details: error ? `Error: ${error.message}` : `Retrieved ${data?.length || 0} records`
        };
      }
    }
  ];

  for (const test of loadTests) {
    try {
      const result = await test.test();

      if (result.success) {
        if (result.duration > 3000) {
          logTest(`Load: ${test.name}`, 'WARN', `${result.details} (slow: ${result.duration}ms)`, result.duration);
        } else {
          logTest(`Load: ${test.name}`, 'PASS', `${result.details} (${result.duration}ms)`, result.duration);
        }
      } else {
        logTest(`Load: ${test.name}`, 'FAIL', result.details, result.duration);
      }

      logPerformance(`Load test: ${test.name}`, result.duration);
    } catch (error) {
      logTest(`Load: ${test.name}`, 'FAIL', `Test error: ${error.message}`, 0);
    }
  }
}

// ===================================================================
// 5. SECURITY TESTING
// ===================================================================

async function testDatabaseSecurity() {
  console.log('\n🛡️ TESTING DATABASE SECURITY...');
  console.log('=================================');

  const securityTests = [
    {
      name: 'Row Level Security (RLS)',
      test: async () => {
        // Test that RLS is properly configured
        try {
          // This should fail if RLS is properly configured for tenant isolation
          const { data, error } = await supabase
            .from('users')
            .select('*')
            .limit(1000); // Try to get more data than should be allowed

          // If we get data without proper authentication context, RLS might not be configured
          if (data && data.length > 0) {
            return {
              secure: false,
              details: `Retrieved ${data.length} user records without tenant context - RLS may not be configured`
            };
          }

          return {
            secure: true,
            details: 'RLS appears to be properly configured'
          };
        } catch (error) {
          return {
            secure: true,
            details: 'Access properly restricted'
          };
        }
      }
    },
    {
      name: 'Sensitive Data Exposure',
      test: async () => {
        const { data, error } = await supabase
          .from('users')
          .select('password_hash')
          .limit(1);

        if (error && error.message.includes('password_hash')) {
          return {
            secure: true,
            details: 'Password hashes properly protected'
          };
        }

        if (data && data.length > 0 && data[0].password_hash) {
          return {
            secure: false,
            details: 'Password hashes exposed through API'
          };
        }

        return {
          secure: true,
          details: 'No sensitive data exposure detected'
        };
      }
    }
  ];

  for (const test of securityTests) {
    const startTime = Date.now();

    try {
      const result = await test.test();
      const duration = Date.now() - startTime;

      if (result.secure) {
        logTest(`Security: ${test.name}`, 'PASS', result.details, duration);
      } else {
        logTest(`Security: ${test.name}`, 'FAIL', result.details, duration);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logTest(`Security: ${test.name}`, 'FAIL', `Test error: ${error.message}`, duration);
    }
  }
}

// ===================================================================
// 6. OPTIMIZATION RECOMMENDATIONS
// ===================================================================

async function generateOptimizationRecommendations() {
  console.log('\n💡 GENERATING OPTIMIZATION RECOMMENDATIONS...');
  console.log('===============================================');

  const recommendations = [];

  // Analyze slow queries
  if (testResults.performance.slowQueries.length > 0) {
    recommendations.push({
      category: 'Performance',
      priority: 'High',
      issue: `${testResults.performance.slowQueries.length} slow queries detected`,
      recommendation: 'Review and optimize slow queries, consider adding indexes',
      queries: testResults.performance.slowQueries.map(q => q.queryName)
    });
  }

  // Check average query time
  if (testResults.performance.averageQueryTime > 500) {
    recommendations.push({
      category: 'Performance',
      priority: 'Medium',
      issue: `Average query time is ${testResults.performance.averageQueryTime.toFixed(2)}ms`,
      recommendation: 'Consider database optimization, connection pooling, or caching'
    });
  }

  // Check connection time
  if (testResults.performance.connectionTime > 1000) {
    recommendations.push({
      category: 'Network',
      priority: 'Medium',
      issue: `Database connection time is ${testResults.performance.connectionTime}ms`,
      recommendation: 'Consider connection pooling or geographic proximity optimization'
    });
  }

  // Check for failed tests
  if (testResults.summary.failed > 0) {
    recommendations.push({
      category: 'Reliability',
      priority: 'Critical',
      issue: `${testResults.summary.failed} tests failed`,
      recommendation: 'Address failed tests immediately before production deployment'
    });
  }

  testResults.optimization.recommendations = recommendations;

  console.log('\n🎯 OPTIMIZATION RECOMMENDATIONS:');
  recommendations.forEach((rec, index) => {
    console.log(`\n${index + 1}. [${rec.priority}] ${rec.category}: ${rec.issue}`);
    console.log(`   💡 ${rec.recommendation}`);
    if (rec.queries) {
      console.log(`   📊 Affected queries: ${rec.queries.join(', ')}`);
    }
  });

  return recommendations;
}

// Main execution function
async function runDatabaseOptimizationTests() {
  console.log('🚀 STARTING COMPREHENSIVE DATABASE OPTIMIZATION TESTS');
  console.log('======================================================');
  console.log(`Timestamp: ${testResults.timestamp}`);
  console.log(`Target Database: ${supabaseUrl}`);

  const overallStartTime = Date.now();

  // Run all test suites
  const connected = await testDatabaseConnection();
  if (!connected) {
    console.log('\n❌ Database connection failed. Aborting tests.');
    return;
  }

  await testTableExistence();
  await testQueryPerformance();
  await testDataIntegrity();
  await testDatabaseLoad();
  await testDatabaseSecurity();

  // Calculate summary statistics
  const overallDuration = Date.now() - overallStartTime;
  testResults.performance.averageQueryTime =
    testResults.performance.queryTimes.reduce((sum, q) => sum + q.duration, 0) /
    testResults.performance.queryTimes.length || 0;

  // Generate optimization recommendations
  await generateOptimizationRecommendations();

  // Generate final report
  console.log('\n📊 FINAL TEST SUMMARY');
  console.log('======================');
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`✅ Passed: ${testResults.summary.passed}`);
  console.log(`❌ Failed: ${testResults.summary.failed}`);
  console.log(`⚠️  Warnings: ${testResults.summary.warnings}`);
  console.log(`⏱️  Overall Duration: ${overallDuration}ms`);
  console.log(`📈 Average Query Time: ${testResults.performance.averageQueryTime.toFixed(2)}ms`);
  console.log(`🔌 Connection Time: ${testResults.performance.connectionTime}ms`);

  // Success rate calculation
  const successRate = ((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1);
  console.log(`🎯 Success Rate: ${successRate}%`);

  if (testResults.performance.slowQueries.length > 0) {
    console.log(`\n⚠️  Slow Queries Detected: ${testResults.performance.slowQueries.length}`);
    testResults.performance.slowQueries.forEach(q => {
      console.log(`   - ${q.queryName}: ${q.duration}ms`);
    });
  }

  // Overall health assessment
  let healthStatus = 'EXCELLENT';
  if (testResults.summary.failed > 0) {
    healthStatus = 'CRITICAL';
  } else if (testResults.summary.warnings > 3 || testResults.performance.slowQueries.length > 2) {
    healthStatus = 'NEEDS_ATTENTION';
  } else if (testResults.summary.warnings > 0 || testResults.performance.slowQueries.length > 0) {
    healthStatus = 'GOOD';
  }

  console.log(`\n🏥 Database Health: ${healthStatus}`);

  // Save detailed results to file
  const reportPath = `database-optimization-report-${Date.now()}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);

  // Production readiness assessment
  const productionReady = testResults.summary.failed === 0 &&
                         testResults.performance.slowQueries.length < 3 &&
                         testResults.performance.averageQueryTime < 1000;

  console.log(`\n🚀 Production Ready: ${productionReady ? 'YES' : 'NO'}`);

  if (!productionReady) {
    console.log('\n⚠️  PRODUCTION READINESS ISSUES:');
    if (testResults.summary.failed > 0) {
      console.log(`   - ${testResults.summary.failed} critical test failures`);
    }
    if (testResults.performance.slowQueries.length >= 3) {
      console.log(`   - ${testResults.performance.slowQueries.length} slow queries need optimization`);
    }
    if (testResults.performance.averageQueryTime >= 1000) {
      console.log(`   - Average query time too high: ${testResults.performance.averageQueryTime.toFixed(2)}ms`);
    }
  }

  return testResults;
}

// Export for use in other scripts
export { runDatabaseOptimizationTests, testResults };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDatabaseOptimizationTests().catch(console.error);
}
