-- Usage-Based Billing Engine Schema
-- Advanced metered billing and revenue optimization

-- Billing meters for tracking usage
CREATE TABLE billing_meters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Meter identification
    meter_code VARCHAR(50) UNIQUE NOT NULL, -- 'api_calls', 'storage_gb', 'document_processing'
    meter_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Meter configuration
    unit_type VARCHAR(50) NOT NULL, -- 'count', 'bytes', 'minutes', 'requests'
    unit_label VARCHAR(20) NOT NULL, -- 'calls', 'GB', 'documents', 'minutes'
    
    -- Pricing configuration
    base_rate DECIMAL(10,4) NOT NULL, -- Cost per unit
    free_tier_limit INTEGER DEFAULT 0, -- Free units included
    
    -- Billing rules
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('daily', 'weekly', 'monthly', 'quarterly', 'annual')),
    aggregation_method VARCHAR(20) DEFAULT 'sum' CHECK (aggregation_method IN ('sum', 'max', 'avg', 'last')),
    
    -- Overage handling
    overage_policy VARCHAR(20) DEFAULT 'charge' CHECK (overage_policy IN ('block', 'charge', 'warn')),
    overage_rate DECIMAL(10,4), -- Different rate for overages
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Plan-specific meter configurations
CREATE TABLE plan_meter_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(plan_id) ON DELETE CASCADE,
    meter_code VARCHAR(50) NOT NULL REFERENCES billing_meters(meter_code) ON DELETE CASCADE,
    
    -- Plan-specific overrides
    included_units INTEGER DEFAULT 0, -- Free units for this plan
    rate_override DECIMAL(10,4), -- Plan-specific rate
    overage_rate_override DECIMAL(10,4), -- Plan-specific overage rate
    
    -- Volume discounts
    volume_discounts JSONB DEFAULT '[]', -- Array of {threshold: number, discount: number}
    
    -- Limits
    hard_limit INTEGER, -- Block usage after this limit
    soft_limit INTEGER, -- Warn at this limit
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(plan_id, meter_code)
);

-- Real-time usage tracking
CREATE TABLE usage_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    meter_code VARCHAR(50) NOT NULL REFERENCES billing_meters(meter_code),
    
    -- Event details
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    quantity DECIMAL(15,6) NOT NULL, -- Amount of usage
    
    -- Context
    user_id UUID REFERENCES users(id),
    resource_id VARCHAR(255), -- ID of the resource being used
    resource_type VARCHAR(100), -- Type of resource (claim, document, etc.)
    
    -- Metadata
    metadata JSONB DEFAULT '{}', -- Additional event data
    
    -- Billing
    billable BOOLEAN DEFAULT true,
    rate_applied DECIMAL(10,4), -- Rate used for this event
    cost DECIMAL(10,4), -- Calculated cost
    
    -- Aggregation optimization
    billing_period DATE, -- Pre-calculated billing period
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Aggregated usage for billing periods
CREATE TABLE usage_aggregates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    meter_code VARCHAR(50) NOT NULL REFERENCES billing_meters(meter_code),
    
    -- Period
    billing_period_start TIMESTAMPTZ NOT NULL,
    billing_period_end TIMESTAMPTZ NOT NULL,
    period_type VARCHAR(20) NOT NULL,
    
    -- Usage totals
    total_quantity DECIMAL(15,6) NOT NULL,
    billable_quantity DECIMAL(15,6) NOT NULL,
    included_quantity DECIMAL(15,6) DEFAULT 0, -- Free tier usage
    overage_quantity DECIMAL(15,6) DEFAULT 0, -- Usage beyond included
    
    -- Costs
    included_cost DECIMAL(10,2) DEFAULT 0,
    overage_cost DECIMAL(10,2) DEFAULT 0,
    total_cost DECIMAL(10,2) NOT NULL,
    
    -- Discounts applied
    volume_discount_percent DECIMAL(5,2) DEFAULT 0,
    volume_discount_amount DECIMAL(10,2) DEFAULT 0,
    
    -- Status
    is_finalized BOOLEAN DEFAULT false,
    finalized_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id, meter_code, billing_period_start, period_type)
);

-- Invoice line items for usage charges
CREATE TABLE invoice_line_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID, -- Reference to external billing system
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Line item details
    item_type VARCHAR(50) NOT NULL, -- 'subscription', 'usage', 'overage', 'discount'
    description TEXT NOT NULL,
    
    -- Quantities and rates
    quantity DECIMAL(15,6),
    unit_price DECIMAL(10,4),
    amount DECIMAL(10,2) NOT NULL,
    
    -- Usage-specific
    meter_code VARCHAR(50) REFERENCES billing_meters(meter_code),
    usage_period_start TIMESTAMPTZ,
    usage_period_end TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Revenue optimization rules
CREATE TABLE upsell_triggers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Trigger identification
    trigger_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Conditions
    meter_code VARCHAR(50) REFERENCES billing_meters(meter_code),
    threshold_type VARCHAR(20) NOT NULL CHECK (threshold_type IN ('percentage', 'absolute', 'frequency')),
    threshold_value DECIMAL(15,6) NOT NULL,
    
    -- Time window
    evaluation_period VARCHAR(20) DEFAULT 'monthly',
    consecutive_periods INTEGER DEFAULT 1,
    
    -- Actions
    recommended_plan VARCHAR(50) REFERENCES subscription_plans(plan_id),
    discount_offer DECIMAL(5,2), -- Percentage discount for upgrade
    
    -- Targeting
    current_plans TEXT[], -- Plans this trigger applies to
    exclude_plans TEXT[], -- Plans to exclude
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0, -- Higher priority triggers fire first
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Upsell opportunities generated
CREATE TABLE upsell_opportunities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    trigger_id UUID NOT NULL REFERENCES upsell_triggers(id),
    
    -- Opportunity details
    current_plan VARCHAR(50) NOT NULL,
    recommended_plan VARCHAR(50) NOT NULL,
    potential_savings DECIMAL(10,2),
    
    -- Trigger data
    trigger_metric VARCHAR(100),
    trigger_value DECIMAL(15,6),
    threshold_exceeded DECIMAL(15,6),
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'presented', 'accepted', 'declined', 'expired')),
    presented_at TIMESTAMPTZ,
    responded_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Discount offered
    discount_percent DECIMAL(5,2),
    discount_expires_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Volume discount tiers
CREATE TABLE volume_discount_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(plan_id) ON DELETE CASCADE,
    meter_code VARCHAR(50) NOT NULL REFERENCES billing_meters(meter_code) ON DELETE CASCADE,
    
    -- Tier configuration
    tier_name VARCHAR(100) NOT NULL,
    minimum_quantity DECIMAL(15,6) NOT NULL,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount', 'rate_override')),
    discount_value DECIMAL(10,4) NOT NULL,
    
    -- Conditions
    requires_annual_commitment BOOLEAN DEFAULT false,
    minimum_contract_value DECIMAL(10,2),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(plan_id, meter_code, minimum_quantity)
);

-- Insert default billing meters
INSERT INTO billing_meters (meter_code, meter_name, description, unit_type, unit_label, base_rate, free_tier_limit, overage_policy) VALUES
('api_calls', 'API Calls', 'REST API requests to the platform', 'count', 'calls', 0.01, 1000, 'charge'),
('storage_gb', 'Storage Usage', 'File storage in gigabytes', 'bytes', 'GB', 2.00, 5, 'charge'),
('document_processing', 'Document Processing', 'AI-powered document analysis', 'count', 'documents', 0.25, 50, 'charge'),
('sms_messages', 'SMS Messages', 'SMS notifications sent', 'count', 'messages', 0.05, 100, 'charge'),
('email_sends', 'Email Sends', 'Email notifications sent', 'count', 'emails', 0.02, 500, 'charge'),
('data_exports', 'Data Exports', 'Large data export operations', 'count', 'exports', 1.00, 10, 'charge'),
('advanced_search', 'Advanced Search', 'AI-powered search queries', 'count', 'searches', 0.10, 200, 'charge'),
('report_generation', 'Report Generation', 'Custom report generation', 'count', 'reports', 0.50, 25, 'charge');

-- Configure meters for each plan
INSERT INTO plan_meter_configs (plan_id, meter_code, included_units, rate_override) VALUES
-- Bronze plan
('bronze', 'api_calls', 1000, 0.015),
('bronze', 'storage_gb', 5, 2.50),
('bronze', 'document_processing', 50, 0.30),
('bronze', 'sms_messages', 100, 0.06),
('bronze', 'email_sends', 500, 0.025),

-- Silver plan  
('silver', 'api_calls', 5000, 0.012),
('silver', 'storage_gb', 15, 2.25),
('silver', 'document_processing', 200, 0.28),
('silver', 'sms_messages', 300, 0.055),
('silver', 'email_sends', 1500, 0.022),
('silver', 'data_exports', 25, 0.90),

-- Gold plan
('gold', 'api_calls', 15000, 0.010),
('gold', 'storage_gb', 50, 2.00),
('gold', 'document_processing', 500, 0.25),
('gold', 'sms_messages', 1000, 0.05),
('gold', 'email_sends', 5000, 0.02),
('gold', 'data_exports', 50, 0.80),
('gold', 'advanced_search', 1000, 0.08),
('gold', 'report_generation', 100, 0.40),

-- Topaz plan
('topaz', 'api_calls', 50000, 0.008),
('topaz', 'storage_gb', 200, 1.75),
('topaz', 'document_processing', 2000, 0.22),
('topaz', 'sms_messages', 3000, 0.045),
('topaz', 'email_sends', 15000, 0.018),
('topaz', 'data_exports', 150, 0.70),
('topaz', 'advanced_search', 5000, 0.06),
('topaz', 'report_generation', 300, 0.35);

-- Insert default upsell triggers
INSERT INTO upsell_triggers (trigger_name, description, meter_code, threshold_type, threshold_value, recommended_plan, discount_offer, current_plans) VALUES
('API Usage High', 'Customer consistently using >80% of API quota', 'api_calls', 'percentage', 80, 'silver', 10, ARRAY['bronze']),
('Storage Near Limit', 'Storage usage approaching plan limit', 'storage_gb', 'percentage', 85, 'gold', 15, ARRAY['bronze', 'silver']),
('Heavy Document Processing', 'High document processing usage', 'document_processing', 'percentage', 90, 'topaz', 20, ARRAY['bronze', 'silver', 'gold']),
('Frequent Exports', 'Customer doing many data exports', 'data_exports', 'absolute', 20, 'gold', 12, ARRAY['bronze', 'silver']),
('Power User Pattern', 'Using advanced features heavily', 'advanced_search', 'percentage', 75, 'ruby', 25, ARRAY['gold', 'topaz']);

-- Create indexes for performance
CREATE INDEX idx_usage_events_org_meter_period ON usage_events(organization_id, meter_code, billing_period);
CREATE INDEX idx_usage_events_timestamp ON usage_events(event_timestamp);
CREATE INDEX idx_usage_aggregates_org_period ON usage_aggregates(organization_id, billing_period_start);
CREATE INDEX idx_upsell_opportunities_org_status ON upsell_opportunities(organization_id, status);
CREATE INDEX idx_invoice_line_items_org_period ON invoice_line_items(organization_id, usage_period_start);

-- Functions for usage tracking
CREATE OR REPLACE FUNCTION record_usage_event(
    p_organization_id UUID,
    p_meter_code VARCHAR(50),
    p_quantity DECIMAL(15,6),
    p_user_id UUID DEFAULT NULL,
    p_resource_id VARCHAR(255) DEFAULT NULL,
    p_resource_type VARCHAR(100) DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    v_event_id UUID;
    v_rate DECIMAL(10,4);
    v_cost DECIMAL(10,4);
    v_billing_period DATE;
BEGIN
    -- Calculate billing period (start of current month)
    v_billing_period := date_trunc('month', NOW())::DATE;
    
    -- Get rate for this organization's plan
    SELECT COALESCE(pmc.rate_override, bm.base_rate) INTO v_rate
    FROM billing_meters bm
    LEFT JOIN organization_subscriptions os ON os.organization_id = p_organization_id
    LEFT JOIN plan_meter_configs pmc ON pmc.plan_id = os.plan_id AND pmc.meter_code = bm.meter_code
    WHERE bm.meter_code = p_meter_code;
    
    -- Calculate cost
    v_cost := p_quantity * v_rate;
    
    -- Insert usage event
    INSERT INTO usage_events (
        organization_id, meter_code, quantity, user_id, resource_id, 
        resource_type, metadata, rate_applied, cost, billing_period
    ) VALUES (
        p_organization_id, p_meter_code, p_quantity, p_user_id, p_resource_id,
        p_resource_type, p_metadata, v_rate, v_cost, v_billing_period
    ) RETURNING id INTO v_event_id;
    
    -- Update real-time aggregates (async in production)
    PERFORM update_usage_aggregates(p_organization_id, p_meter_code, v_billing_period);
    
    RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update usage aggregates
CREATE OR REPLACE FUNCTION update_usage_aggregates(
    p_organization_id UUID,
    p_meter_code VARCHAR(50),
    p_billing_period DATE
) RETURNS VOID AS $$
DECLARE
    v_total_quantity DECIMAL(15,6);
    v_total_cost DECIMAL(10,2);
    v_period_start TIMESTAMPTZ;
    v_period_end TIMESTAMPTZ;
BEGIN
    -- Calculate period boundaries
    v_period_start := p_billing_period::TIMESTAMPTZ;
    v_period_end := (p_billing_period + INTERVAL '1 month')::TIMESTAMPTZ;
    
    -- Calculate totals for the period
    SELECT 
        COALESCE(SUM(quantity), 0),
        COALESCE(SUM(cost), 0)
    INTO v_total_quantity, v_total_cost
    FROM usage_events
    WHERE organization_id = p_organization_id
    AND meter_code = p_meter_code
    AND billing_period = p_billing_period
    AND billable = true;
    
    -- Upsert aggregate record
    INSERT INTO usage_aggregates (
        organization_id, meter_code, billing_period_start, billing_period_end,
        period_type, total_quantity, billable_quantity, total_cost
    ) VALUES (
        p_organization_id, p_meter_code, v_period_start, v_period_end,
        'monthly', v_total_quantity, v_total_quantity, v_total_cost
    )
    ON CONFLICT (organization_id, meter_code, billing_period_start, period_type)
    DO UPDATE SET
        total_quantity = v_total_quantity,
        billable_quantity = v_total_quantity,
        total_cost = v_total_cost,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON billing_meters TO authenticated;
GRANT SELECT ON plan_meter_configs TO authenticated;
GRANT SELECT, INSERT ON usage_events TO authenticated;
GRANT SELECT ON usage_aggregates TO authenticated;
GRANT SELECT ON invoice_line_items TO authenticated;
GRANT SELECT ON upsell_triggers TO authenticated;
GRANT SELECT, INSERT, UPDATE ON upsell_opportunities TO authenticated;
GRANT SELECT ON volume_discount_tiers TO authenticated;

-- Row Level Security
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_aggregates ENABLE ROW LEVEL SECURITY;
ALTER TABLE upsell_opportunities ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their organization's usage events" ON usage_events
    FOR SELECT TO authenticated
    USING (organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid()));

CREATE POLICY "Users can view their organization's usage aggregates" ON usage_aggregates
    FOR SELECT TO authenticated
    USING (organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid()));

CREATE POLICY "Users can view their organization's upsell opportunities" ON upsell_opportunities
    FOR ALL TO authenticated
    USING (organization_id IN (SELECT organization_id FROM users WHERE id = auth.uid()));
