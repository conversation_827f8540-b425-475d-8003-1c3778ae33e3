{"name": "AssetHunterPro", "short_name": "AssetHunterPro", "description": "Professional Asset Recovery Platform", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3B82F6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["business", "finance", "productivity"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View main dashboard", "url": "/dashboard", "icons": [{"src": "/icons/shortcut-dashboard.png", "sizes": "96x96"}]}, {"name": "<PERSON><PERSON><PERSON>", "short_name": "<PERSON><PERSON><PERSON>", "description": "Manage claims", "url": "/claims", "icons": [{"src": "/icons/shortcut-claims.png", "sizes": "96x96"}]}, {"name": "Search", "short_name": "Search", "description": "Search claims and claimants", "url": "/search", "icons": [{"src": "/icons/shortcut-search.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Dashboard view on desktop"}, {"src": "/screenshots/mobile-dashboard.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Dashboard view on mobile"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}