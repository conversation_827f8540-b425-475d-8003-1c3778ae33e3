import React, { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Target, 
  AlertTriangle,
  BarChart3,
  PieChart,
  Calendar,
  Download,
  RefreshCw,
  Zap,
  Award,
  Clock,
  Activity
} from 'lucide-react'

interface ExecutiveMetrics {
  revenue: {
    current_month: number
    previous_month: number
    ytd: number
    projected_annual: number
    growth_rate: number
  }
  recovery: {
    total_recovered: number
    recovery_rate: number
    avg_recovery_time: number
    success_rate: number
  }
  customers: {
    total_customers: number
    new_customers: number
    churn_rate: number
    customer_satisfaction: number
  }
  operations: {
    active_claims: number
    claims_processed: number
    team_efficiency: number
    avg_resolution_time: number
  }
  forecasts: {
    next_month_revenue: number
    next_quarter_recovery: number
    capacity_utilization: number
    risk_score: number
  }
}

interface KPICard {
  title: string
  value: string | number
  change: number
  trend: 'up' | 'down' | 'stable'
  format: 'currency' | 'percentage' | 'number' | 'days'
  icon: React.ComponentType<any>
  color: string
}

export const ExecutiveDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<ExecutiveMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  useEffect(() => {
    loadExecutiveMetrics()
    const interval = setInterval(loadExecutiveMetrics, 300000) // Refresh every 5 minutes
    return () => clearInterval(interval)
  }, [selectedPeriod])

  const loadExecutiveMetrics = async () => {
    setIsLoading(true)
    try {
      // Mock executive metrics - in production, this would come from your analytics service
      const mockMetrics: ExecutiveMetrics = {
        revenue: {
          current_month: 127500,
          previous_month: 118200,
          ytd: 1425000,
          projected_annual: 1680000,
          growth_rate: 7.9
        },
        recovery: {
          total_recovered: 2850000,
          recovery_rate: 73.2,
          avg_recovery_time: 45,
          success_rate: 89.4
        },
        customers: {
          total_customers: 156,
          new_customers: 12,
          churn_rate: 2.1,
          customer_satisfaction: 4.7
        },
        operations: {
          active_claims: 1247,
          claims_processed: 3456,
          team_efficiency: 87.3,
          avg_resolution_time: 38
        },
        forecasts: {
          next_month_revenue: 135000,
          next_quarter_recovery: 950000,
          capacity_utilization: 82.5,
          risk_score: 15.2
        }
      }

      setMetrics(mockMetrics)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error loading executive metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatValue = (value: number, format: string): string => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value)
      case 'percentage':
        return `${value.toFixed(1)}%`
      case 'days':
        return `${value.toFixed(0)} days`
      case 'number':
        return value.toLocaleString()
      default:
        return value.toString()
    }
  }

  const getTrendIcon = (trend: string, change: number) => {
    if (trend === 'up' || change > 0) {
      return <TrendingUp className="h-4 w-4 text-green-500" />
    } else if (trend === 'down' || change < 0) {
      return <TrendingDown className="h-4 w-4 text-red-500" />
    }
    return <Activity className="h-4 w-4 text-gray-500" />
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600 dark:text-green-400'
    if (change < 0) return 'text-red-600 dark:text-red-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Unable to load executive metrics</p>
      </div>
    )
  }

  const kpiCards: KPICard[] = [
    {
      title: 'Monthly Revenue',
      value: metrics.revenue.current_month,
      change: metrics.revenue.growth_rate,
      trend: 'up',
      format: 'currency',
      icon: DollarSign,
      color: 'green'
    },
    {
      title: 'Recovery Rate',
      value: metrics.recovery.recovery_rate,
      change: 2.3,
      trend: 'up',
      format: 'percentage',
      icon: Target,
      color: 'blue'
    },
    {
      title: 'Total Customers',
      value: metrics.customers.total_customers,
      change: metrics.customers.new_customers,
      trend: 'up',
      format: 'number',
      icon: Users,
      color: 'purple'
    },
    {
      title: 'Team Efficiency',
      value: metrics.operations.team_efficiency,
      change: 4.2,
      trend: 'up',
      format: 'percentage',
      icon: Zap,
      color: 'orange'
    },
    {
      title: 'Avg Resolution Time',
      value: metrics.operations.avg_resolution_time,
      change: -3.5,
      trend: 'down',
      format: 'days',
      icon: Clock,
      color: 'indigo'
    },
    {
      title: 'Customer Satisfaction',
      value: metrics.customers.customer_satisfaction,
      change: 0.2,
      trend: 'up',
      format: 'number',
      icon: Award,
      color: 'pink'
    }
  ]

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Executive Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time business intelligence and key performance indicators
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>

          <button
            onClick={loadExecutiveMetrics}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {kpiCards.map((kpi, index) => {
          const Icon = kpi.icon
          return (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {kpi.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
                    {formatValue(Number(kpi.value), kpi.format)}
                  </p>
                  <div className="flex items-center mt-2">
                    {getTrendIcon(kpi.trend, kpi.change)}
                    <span className={`text-sm ml-1 ${getChangeColor(kpi.change)}`}>
                      {kpi.change > 0 ? '+' : ''}{kpi.change}
                      {kpi.format === 'percentage' ? 'pp' : kpi.format === 'number' ? '' : '%'}
                    </span>
                  </div>
                </div>
                <div className={`w-12 h-12 bg-${kpi.color}-100 dark:bg-${kpi.color}-900/20 rounded-lg flex items-center justify-center`}>
                  <Icon className={`h-6 w-6 text-${kpi.color}-600 dark:text-${kpi.color}-400`} />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Revenue and Recovery Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Revenue Performance
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Year to Date</span>
              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {formatValue(metrics.revenue.ytd, 'currency')}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Projected Annual</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {formatValue(metrics.revenue.projected_annual, 'currency')}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Growth Rate</span>
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {formatValue(metrics.revenue.growth_rate, 'percentage')}
              </span>
            </div>

            {/* Revenue Progress Bar */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600 dark:text-gray-400">Annual Target Progress</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {((metrics.revenue.ytd / metrics.revenue.projected_annual) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="bg-green-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${(metrics.revenue.ytd / metrics.revenue.projected_annual) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Recovery Metrics
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Recovered</span>
              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {formatValue(metrics.recovery.total_recovered, 'currency')}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Success Rate</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {formatValue(metrics.recovery.success_rate, 'percentage')}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg Recovery Time</span>
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {formatValue(metrics.recovery.avg_recovery_time, 'days')}
              </span>
            </div>

            {/* Recovery Rate Progress */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600 dark:text-gray-400">Recovery Rate Target</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {formatValue(metrics.recovery.recovery_rate, 'percentage')} / 75%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${(metrics.recovery.recovery_rate / 75) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Forecasts and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Predictive Forecasts
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Next Month Revenue
                </h4>
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {formatValue(metrics.forecasts.next_month_revenue, 'currency')}
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                +5.9% vs current month
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Next Quarter Recovery
                </h4>
                <Target className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                {formatValue(metrics.forecasts.next_quarter_recovery, 'currency')}
              </p>
              <p className="text-sm text-green-700 dark:text-green-300">
                Based on current trends
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-purple-800 dark:text-purple-200">
                  Capacity Utilization
                </h4>
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {formatValue(metrics.forecasts.capacity_utilization, 'percentage')}
              </p>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Optimal range: 75-85%
              </p>
            </div>

            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Risk Score
                </h4>
                <AlertTriangle className="h-4 w-4 text-orange-600" />
              </div>
              <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {formatValue(metrics.forecasts.risk_score, 'percentage')}
              </p>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                Low risk threshold
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Quick Actions
          </h3>
          
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
              <div className="flex items-center space-x-3">
                <Download className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Export Monthly Report
                </span>
              </div>
            </button>

            <button className="w-full flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
              <div className="flex items-center space-x-3">
                <BarChart3 className="h-5 w-5 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  View Detailed Analytics
                </span>
              </div>
            </button>

            <button className="w-full flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Schedule Review Meeting
                </span>
              </div>
            </button>

            <button className="w-full flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
              <div className="flex items-center space-x-3">
                <Zap className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  AI Optimization
                </span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
