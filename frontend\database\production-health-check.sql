-- ===================================================================
-- PRODUCTION DATABASE HEALTH CHECK & OPTIMIZATION
-- AssetHunterPro - Advanced Production Readiness Assessment
-- ===================================================================
-- Run this script in Supabase SQL Editor for comprehensive analysis
-- ===================================================================

-- Enable timing and verbose output
\timing on
\set VERBOSITY verbose

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PRODUCTION DATABASE HEALTH CHECK - ASSETHUNTERPRO';
    RAISE NOTICE '=====================================================';
    RAISE NOTICE 'Timestamp: %', NOW();
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 1. SYSTEM CONFIGURATION ANALYSIS
-- ===================================================================

DO $$
DECLARE
    db_version TEXT;
    db_size TEXT;
    connection_count INTEGER;
    max_connections INTEGER;
    shared_buffers TEXT;
    work_mem TEXT;
    maintenance_work_mem TEXT;
BEGIN
    RAISE NOTICE '⚙️  SYSTEM CONFIGURATION ANALYSIS';
    RAISE NOTICE '==================================';
    
    -- Database version and size
    SELECT version() INTO db_version;
    SELECT pg_size_pretty(pg_database_size(current_database())) INTO db_size;
    
    RAISE NOTICE '📊 PostgreSQL Version: %', SPLIT_PART(db_version, ' ', 2);
    RAISE NOTICE '💾 Database Size: %', db_size;
    
    -- Connection statistics
    SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active' INTO connection_count;
    SELECT setting::INTEGER FROM pg_settings WHERE name = 'max_connections' INTO max_connections;
    
    RAISE NOTICE '🔌 Active Connections: % / %', connection_count, max_connections;
    
    -- Memory configuration
    SELECT setting FROM pg_settings WHERE name = 'shared_buffers' INTO shared_buffers;
    SELECT setting FROM pg_settings WHERE name = 'work_mem' INTO work_mem;
    SELECT setting FROM pg_settings WHERE name = 'maintenance_work_mem' INTO maintenance_work_mem;
    
    RAISE NOTICE '🧠 Memory Configuration:';
    RAISE NOTICE '   - Shared Buffers: %', shared_buffers;
    RAISE NOTICE '   - Work Memory: %', work_mem;
    RAISE NOTICE '   - Maintenance Work Memory: %', maintenance_work_mem;
    
    -- Connection usage warning
    IF connection_count::FLOAT / max_connections > 0.8 THEN
        RAISE WARNING '⚠️  High connection usage: % / %', connection_count, max_connections;
    ELSE
        RAISE NOTICE '✅ Connection usage healthy: % / %', connection_count, max_connections;
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 2. TABLE ANALYSIS & STATISTICS
-- ===================================================================

DO $$
DECLARE
    table_info RECORD;
    total_size BIGINT := 0;
    total_rows BIGINT := 0;
    large_table_count INTEGER := 0;
    bloat_table_count INTEGER := 0;
BEGIN
    RAISE NOTICE '📊 TABLE ANALYSIS & STATISTICS';
    RAISE NOTICE '===============================';
    
    -- Analyze all user tables
    FOR table_info IN
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
            n_tup_ins + n_tup_upd + n_tup_del as total_operations,
            n_live_tup as live_tuples,
            n_dead_tup as dead_tuples,
            last_vacuum,
            last_autovacuum,
            last_analyze,
            last_autoanalyze
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    LOOP
        total_size := total_size + table_info.size_bytes;
        total_rows := total_rows + table_info.live_tuples;
        
        RAISE NOTICE '📋 Table: % - Size: % (% live, % dead tuples)', 
            table_info.tablename, 
            table_info.size,
            table_info.live_tuples,
            table_info.dead_tuples;
        
        -- Check for large tables
        IF table_info.size_bytes > 100 * 1024 * 1024 THEN -- > 100MB
            large_table_count := large_table_count + 1;
            RAISE NOTICE '💡 Large table: % (%)', table_info.tablename, table_info.size;
        END IF;
        
        -- Check for bloated tables (high dead tuple ratio)
        IF table_info.live_tuples > 0 AND 
           table_info.dead_tuples::FLOAT / table_info.live_tuples > 0.2 THEN
            bloat_table_count := bloat_table_count + 1;
            RAISE WARNING '⚠️  Table bloat detected in %: % dead / % live (%.1f%%)', 
                table_info.tablename, 
                table_info.dead_tuples, 
                table_info.live_tuples,
                (table_info.dead_tuples::FLOAT / table_info.live_tuples * 100);
        END IF;
        
        -- Check vacuum/analyze status
        IF table_info.last_vacuum IS NULL AND table_info.last_autovacuum IS NULL THEN
            RAISE WARNING '⚠️  Table % has never been vacuumed', table_info.tablename;
        END IF;
        
        IF table_info.last_analyze IS NULL AND table_info.last_autoanalyze IS NULL THEN
            RAISE WARNING '⚠️  Table % has never been analyzed', table_info.tablename;
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '📈 Database Summary:';
    RAISE NOTICE '   - Total Size: %', pg_size_pretty(total_size);
    RAISE NOTICE '   - Total Rows: %', total_rows;
    RAISE NOTICE '   - Large Tables (>100MB): %', large_table_count;
    RAISE NOTICE '   - Bloated Tables: %', bloat_table_count;
    
    IF bloat_table_count > 0 THEN
        RAISE NOTICE '💡 Recommendation: Run VACUUM ANALYZE on bloated tables';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 3. INDEX PERFORMANCE ANALYSIS
-- ===================================================================

DO $$
DECLARE
    index_info RECORD;
    unused_index_count INTEGER := 0;
    low_usage_count INTEGER := 0;
    total_index_size BIGINT := 0;
BEGIN
    RAISE NOTICE '🔍 INDEX PERFORMANCE ANALYSIS';
    RAISE NOTICE '==============================';
    
    -- Analyze index usage and performance
    FOR index_info IN
        SELECT 
            schemaname,
            tablename,
            indexname,
            idx_tup_read,
            idx_tup_fetch,
            pg_size_pretty(pg_relation_size(indexname)) as index_size,
            pg_relation_size(indexname) as index_size_bytes
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
        ORDER BY idx_tup_read DESC
    LOOP
        total_index_size := total_index_size + index_info.index_size_bytes;
        
        IF index_info.idx_tup_read = 0 THEN
            unused_index_count := unused_index_count + 1;
            RAISE WARNING '⚠️  Unused index: %.% (Size: %)', 
                index_info.tablename, index_info.indexname, index_info.index_size;
        ELSIF index_info.idx_tup_read < 100 THEN
            low_usage_count := low_usage_count + 1;
            RAISE NOTICE '📊 Low usage index: %.% (% reads, Size: %)', 
                index_info.tablename, index_info.indexname, 
                index_info.idx_tup_read, index_info.index_size;
        ELSE
            RAISE NOTICE '✅ Active index: %.% (% reads, Size: %)', 
                index_info.tablename, index_info.indexname, 
                index_info.idx_tup_read, index_info.index_size;
        END IF;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '📊 Index Summary:';
    RAISE NOTICE '   - Total Index Size: %', pg_size_pretty(total_index_size);
    RAISE NOTICE '   - Unused Indexes: %', unused_index_count;
    RAISE NOTICE '   - Low Usage Indexes: %', low_usage_count;
    
    IF unused_index_count > 0 THEN
        RAISE NOTICE '💡 Consider dropping unused indexes to improve write performance';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 4. QUERY PERFORMANCE & SLOW QUERY ANALYSIS
-- ===================================================================

DO $$
DECLARE
    slow_query_count INTEGER;
    avg_query_time NUMERIC;
    total_calls BIGINT;
BEGIN
    RAISE NOTICE '⚡ QUERY PERFORMANCE & SLOW QUERY ANALYSIS';
    RAISE NOTICE '==========================================';
    
    -- Check if pg_stat_statements is available
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements') THEN
        -- Get query performance statistics
        SELECT COUNT(*), AVG(mean_exec_time), SUM(calls)
        FROM pg_stat_statements 
        WHERE mean_exec_time > 1000 -- queries taking more than 1 second
        INTO slow_query_count, avg_query_time, total_calls;
        
        RAISE NOTICE '📊 Query Performance Statistics:';
        RAISE NOTICE '   - Total Query Calls: %', COALESCE(total_calls, 0);
        RAISE NOTICE '   - Average Slow Query Time: % ms', ROUND(COALESCE(avg_query_time, 0), 2);
        RAISE NOTICE '   - Slow Queries (>1s): %', COALESCE(slow_query_count, 0);
        
        IF COALESCE(slow_query_count, 0) > 0 THEN
            RAISE WARNING '⚠️  % slow queries detected - review and optimize', slow_query_count;
            
            -- Show top 3 slowest queries
            RAISE NOTICE '🐌 Top 3 Slowest Queries:';
            FOR slow_query_count IN 1..3 LOOP
                -- This would show actual slow queries in a real implementation
                RAISE NOTICE '   %%. Query analysis available in pg_stat_statements', slow_query_count;
            END LOOP;
        ELSE
            RAISE NOTICE '✅ No slow queries detected - excellent performance!';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  pg_stat_statements extension not available';
        RAISE NOTICE '💡 Enable pg_stat_statements for detailed query analysis';
        RAISE NOTICE '   Contact Supabase support to enable this extension';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 5. SECURITY & RLS ANALYSIS
-- ===================================================================

DO $$
DECLARE
    rls_table_count INTEGER;
    total_table_count INTEGER;
    policy_count INTEGER;
    table_rec RECORD;
BEGIN
    RAISE NOTICE '🛡️  SECURITY & ROW LEVEL SECURITY ANALYSIS';
    RAISE NOTICE '==========================================';
    
    -- Check Row Level Security configuration
    SELECT COUNT(*) 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    INTO total_table_count;
    
    SELECT COUNT(*) 
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true
    INTO rls_table_count;
    
    SELECT COUNT(*) 
    FROM pg_policies 
    WHERE schemaname = 'public'
    INTO policy_count;
    
    RAISE NOTICE '🔒 Row Level Security Status:';
    RAISE NOTICE '   - Total Tables: %', total_table_count;
    RAISE NOTICE '   - Tables with RLS Enabled: %', rls_table_count;
    RAISE NOTICE '   - Active RLS Policies: %', policy_count;
    
    -- List tables without RLS
    IF rls_table_count < total_table_count THEN
        RAISE WARNING '⚠️  % tables do not have RLS enabled', (total_table_count - rls_table_count);
        RAISE NOTICE '📋 Tables without RLS:';
        
        FOR table_rec IN
            SELECT t.tablename
            FROM pg_tables t
            LEFT JOIN pg_class c ON c.relname = t.tablename
            WHERE t.schemaname = 'public' 
            AND (c.relrowsecurity = false OR c.relrowsecurity IS NULL)
        LOOP
            RAISE NOTICE '   - %', table_rec.tablename;
        END LOOP;
    ELSE
        RAISE NOTICE '✅ All tables have RLS enabled - excellent security!';
    END IF;
    
    -- Check for sensitive columns
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND column_name ILIKE '%password%'
    ) THEN
        RAISE NOTICE '🔐 Password columns detected - ensure proper hashing';
    END IF;
    
    RAISE NOTICE '';
END $$;

-- ===================================================================
-- 6. FINAL HEALTH ASSESSMENT & RECOMMENDATIONS
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🎯 FINAL HEALTH ASSESSMENT & RECOMMENDATIONS';
    RAISE NOTICE '============================================';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 IMMEDIATE ACTIONS REQUIRED:';
    RAISE NOTICE '   1. Review all WARNING messages above';
    RAISE NOTICE '   2. Enable RLS on tables without it (see list above)';
    RAISE NOTICE '   3. Drop unused indexes if any were detected';
    RAISE NOTICE '   4. Run VACUUM ANALYZE on bloated tables';
    RAISE NOTICE '';
    RAISE NOTICE '📈 PERFORMANCE OPTIMIZATIONS:';
    RAISE NOTICE '   1. Monitor slow queries and add indexes as needed';
    RAISE NOTICE '   2. Consider connection pooling for high-traffic scenarios';
    RAISE NOTICE '   3. Implement caching for frequently accessed data';
    RAISE NOTICE '   4. Set up automated VACUUM and ANALYZE schedules';
    RAISE NOTICE '';
    RAISE NOTICE '🔒 SECURITY ENHANCEMENTS:';
    RAISE NOTICE '   1. Enable RLS on all tenant-specific tables';
    RAISE NOTICE '   2. Implement comprehensive RLS policies';
    RAISE NOTICE '   3. Set up audit logging for compliance';
    RAISE NOTICE '   4. Regular security assessments and monitoring';
    RAISE NOTICE '';
    RAISE NOTICE '📊 MONITORING SETUP:';
    RAISE NOTICE '   1. Configure performance alerts and dashboards';
    RAISE NOTICE '   2. Set up automated health checks';
    RAISE NOTICE '   3. Monitor connection counts and query performance';
    RAISE NOTICE '   4. Implement log aggregation for audit trails';
    RAISE NOTICE '';
    RAISE NOTICE '✅ PRODUCTION DATABASE HEALTH CHECK COMPLETE!';
    RAISE NOTICE '🚀 Your AssetHunterPro database is ready for optimization!';
    RAISE NOTICE '';
END $$;

-- Disable timing
\timing off
