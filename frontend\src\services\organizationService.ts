import { supabase } from '../lib/supabase'
import type { Organization } from '../types/database'

export interface OrganizationUpdateData {
  name?: string
  subdomain?: string
  industry?: string
  size?: 'small' | 'medium' | 'large' | 'enterprise'
  established_date?: string
  street_address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string
  primary_email?: string
  primary_phone?: string
  website?: string
  primary_color?: string
  secondary_color?: string
  accent_color?: string
  timezone?: string
  date_format?: string
  currency?: string
  language?: string
}

export interface LogoUploadResult {
  success: boolean
  logo_url?: string
  logo_file_name?: string
  error?: string
}

class OrganizationService {
  /**
   * Get organization by ID
   */
  async getOrganization(organizationId: string): Promise<Organization | null> {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', organizationId)
        .single()

      if (error) {
        console.error('Error fetching organization:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getOrganization:', error)
      return null
    }
  }

  /**
   * Get current user's organization
   */
  async getCurrentUserOrganization(): Promise<Organization | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('organization_id')
        .eq('id', user.id)
        .single()

      if (userError || !userData) {
        console.error('Error fetching user data:', userError)
        return null
      }

      return this.getOrganization(userData.organization_id)
    } catch (error) {
      console.error('Error in getCurrentUserOrganization:', error)
      return null
    }
  }

  /**
   * Update organization details
   */
  async updateOrganization(
    organizationId: string, 
    updates: OrganizationUpdateData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('organizations')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', organizationId)

      if (error) {
        console.error('Error updating organization:', error)
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      console.error('Error in updateOrganization:', error)
      return { success: false, error: 'Failed to update organization' }
    }
  }

  /**
   * Upload organization logo
   */
  async uploadLogo(
    organizationId: string, 
    file: File
  ): Promise<LogoUploadResult> {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      if (!allowedTypes.includes(file.type)) {
        return {
          success: false,
          error: 'Invalid file type. Please upload a JPEG, PNG, GIF, WebP, or SVG image.'
        }
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return {
          success: false,
          error: 'File size too large. Please upload an image smaller than 5MB.'
        }
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${organizationId}/logo-${Date.now()}.${fileExt}`

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('company-logos')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        })

      if (uploadError) {
        console.error('Error uploading logo:', uploadError)
        return {
          success: false,
          error: 'Failed to upload logo. Please try again.'
        }
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('company-logos')
        .getPublicUrl(fileName)

      // Update organization with new logo URL
      const updateResult = await this.updateOrganization(organizationId, {
        logo_url: publicUrl,
        logo_file_name: fileName
      })

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Logo uploaded but failed to update organization record.'
        }
      }

      return {
        success: true,
        logo_url: publicUrl,
        logo_file_name: fileName
      }
    } catch (error) {
      console.error('Error in uploadLogo:', error)
      return {
        success: false,
        error: 'Failed to upload logo. Please try again.'
      }
    }
  }

  /**
   * Remove organization logo
   */
  async removeLogo(organizationId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get current organization to find logo file
      const organization = await this.getOrganization(organizationId)
      if (!organization) {
        return { success: false, error: 'Organization not found' }
      }

      // Remove file from storage if it exists
      if (organization.logo_file_name) {
        const { error: deleteError } = await supabase.storage
          .from('company-logos')
          .remove([organization.logo_file_name])

        if (deleteError) {
          console.error('Error deleting logo file:', deleteError)
          // Continue anyway to clear the database record
        }
      }

      // Update organization to remove logo references
      const updateResult = await this.updateOrganization(organizationId, {
        logo_url: undefined,
        logo_file_name: undefined
      })

      return updateResult
    } catch (error) {
      console.error('Error in removeLogo:', error)
      return { success: false, error: 'Failed to remove logo' }
    }
  }

  /**
   * Check if user can manage organization (admin or senior_agent)
   */
  async canManageOrganization(organizationId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return false

      const { data: userData, error } = await supabase
        .from('users')
        .select('role, organization_id')
        .eq('id', user.id)
        .single()

      if (error || !userData) return false

      return userData.organization_id === organizationId && 
             ['admin', 'senior_agent'].includes(userData.role)
    } catch (error) {
      console.error('Error checking organization permissions:', error)
      return false
    }
  }
}

export const organizationService = new OrganizationService()
