import React, { useState, useEffect, useRef } from 'react'
import { Search, Command, ArrowRight, Clock, Star, Hash, User, FileText, Settings } from 'lucide-react'

interface CommandItem {
  id: string
  title: string
  subtitle?: string
  icon: React.ComponentType<{ className?: string }>
  category: 'navigation' | 'actions' | 'search' | 'recent'
  action: () => void
  keywords?: string[]
}

interface CommandPaletteProps {
  isOpen: boolean
  onClose: () => void
  onNavigate: (view: string) => void
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  onNavigate
}) => {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [filteredCommands, setFilteredCommands] = useState<CommandItem[]>([])
  const inputRef = useRef<HTMLInputElement>(null)

  const commands: CommandItem[] = [
    // Navigation
    {
      id: 'nav-dashboard',
      title: 'Go to Dashboard',
      subtitle: 'View main dashboard',
      icon: Hash,
      category: 'navigation',
      action: () => onNavigate('dashboard'),
      keywords: ['dashboard', 'home', 'overview']
    },
    {
      id: 'nav-claims',
      title: 'Go to Claims',
      subtitle: 'Manage claims',
      icon: FileText,
      category: 'navigation',
      action: () => onNavigate('claims'),
      keywords: ['claims', 'cases', 'manage']
    },
    {
      id: 'nav-analytics',
      title: 'Go to Analytics',
      subtitle: 'View reports and analytics',
      icon: Hash,
      category: 'navigation',
      action: () => onNavigate('analytics'),
      keywords: ['analytics', 'reports', 'stats', 'data']
    },
    {
      id: 'nav-users',
      title: 'Go to Users',
      subtitle: 'Manage team members',
      icon: User,
      category: 'navigation',
      action: () => onNavigate('users'),
      keywords: ['users', 'team', 'members', 'agents']
    },
    {
      id: 'nav-settings',
      title: 'Go to Settings',
      subtitle: 'System configuration',
      icon: Settings,
      category: 'navigation',
      action: () => onNavigate('settings'),
      keywords: ['settings', 'config', 'preferences']
    },
    {
      id: 'nav-organization',
      title: 'Go to Organization',
      subtitle: 'Manage organization settings',
      icon: Hash,
      category: 'navigation',
      action: () => onNavigate('organization'),
      keywords: ['organization', 'company', 'branding', 'logo']
    },

    // Actions
    {
      id: 'action-new-claim',
      title: 'Create New Claim',
      subtitle: 'Start a new claim',
      icon: FileText,
      category: 'actions',
      action: () => {
        onNavigate('claims')
        // Trigger new claim modal
      },
      keywords: ['new', 'create', 'claim', 'add']
    },
    {
      id: 'action-import-batch',
      title: 'Import Batch',
      subtitle: 'Upload CSV file',
      icon: Hash,
      category: 'actions',
      action: () => onNavigate('batch-import'),
      keywords: ['import', 'batch', 'csv', 'upload']
    },
    {
      id: 'action-search-ai',
      title: 'AI Search',
      subtitle: 'Search with AI assistance',
      icon: Search,
      category: 'actions',
      action: () => onNavigate('agent-ai-search'),
      keywords: ['ai', 'search', 'intelligent', 'find']
    },

    // Recent (would be populated from user activity)
    {
      id: 'recent-claim-1234',
      title: 'Claim #1234',
      subtitle: 'John Doe - $5,000',
      icon: FileText,
      category: 'recent',
      action: () => {
        // Navigate to specific claim
      },
      keywords: ['claim', '1234', 'john', 'doe']
    }
  ]

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  useEffect(() => {
    if (!query.trim()) {
      // Show all commands when no query
      setFilteredCommands(commands)
    } else {
      // Filter commands based on query
      const filtered = commands.filter(command => {
        const searchText = query.toLowerCase()
        return (
          command.title.toLowerCase().includes(searchText) ||
          command.subtitle?.toLowerCase().includes(searchText) ||
          command.keywords?.some(keyword => keyword.toLowerCase().includes(searchText))
        )
      })
      setFilteredCommands(filtered)
    }
    setSelectedIndex(0)
  }, [query])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          )
          break
        case 'Enter':
          e.preventDefault()
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action()
            onClose()
          }
          break
        case 'Escape':
          e.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredCommands, selectedIndex, onClose])

  if (!isOpen) return null

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'recent': return Clock
      case 'actions': return Star
      case 'search': return Search
      default: return Hash
    }
  }

  const groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = []
    }
    acc[command.category].push(command)
    return acc
  }, {} as Record<string, CommandItem[]>)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-20 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4">
        {/* Search Input */}
        <div className="flex items-center border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <Search className="h-5 w-5 text-gray-400 mr-3" />
          <input
            ref={inputRef}
            type="text"
            placeholder="Type a command or search..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500"
          />
          <div className="flex items-center space-x-1 text-xs text-gray-400">
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">↑↓</kbd>
            <span>navigate</span>
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">↵</kbd>
            <span>select</span>
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">esc</kbd>
            <span>close</span>
          </div>
        </div>

        {/* Results */}
        <div className="max-h-96 overflow-y-auto">
          {Object.keys(groupedCommands).length === 0 ? (
            <div className="p-8 text-center text-gray-500 dark:text-gray-400">
              No commands found for "{query}"
            </div>
          ) : (
            Object.entries(groupedCommands).map(([category, commands]) => {
              const CategoryIcon = getCategoryIcon(category)
              return (
                <div key={category}>
                  <div className="flex items-center px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide bg-gray-50 dark:bg-gray-700/50">
                    <CategoryIcon className="h-3 w-3 mr-2" />
                    {category}
                  </div>
                  {commands.map((command, index) => {
                    const globalIndex = Object.values(groupedCommands)
                      .flat()
                      .indexOf(command)
                    const isSelected = globalIndex === selectedIndex
                    const Icon = command.icon

                    return (
                      <div
                        key={command.id}
                        className={`flex items-center px-4 py-3 cursor-pointer ${
                          isSelected
                            ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500'
                            : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
                        }`}
                        onClick={() => {
                          command.action()
                          onClose()
                        }}
                      >
                        <Icon className={`h-5 w-5 mr-3 ${
                          isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <div className={`text-sm font-medium ${
                            isSelected 
                              ? 'text-blue-900 dark:text-blue-100' 
                              : 'text-gray-900 dark:text-gray-100'
                          }`}>
                            {command.title}
                          </div>
                          {command.subtitle && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                              {command.subtitle}
                            </div>
                          )}
                        </div>
                        {isSelected && (
                          <ArrowRight className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        )}
                      </div>
                    )
                  })}
                </div>
              )
            })
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center justify-between">
            <span>Tip: Use Ctrl+K (Cmd+K on Mac) to open command palette</span>
            <div className="flex items-center space-x-1">
              <Command className="h-3 w-3" />
              <span>+</span>
              <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">K</kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
