-- Complete Role-Based Access Control and Pricing Tiers Schema
-- This consolidates and enhances our RBAC and subscription system

-- Subscription plans and pricing tiers
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Plan identification
    plan_id VARCHAR(50) UNIQUE NOT NULL, -- 'bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond', 'enterprise'
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Pricing
    monthly_price DECIMAL(10,2) NOT NULL,
    annual_price DECIMAL(10,2), -- NULL for pay-as-you-go plans
    setup_fee DECIMAL(10,2) DEFAULT 0,
    
    -- Limits and quotas
    max_users INTEGER DEFAULT 1,
    max_claims INTEGER DEFAULT 100,
    max_storage_gb INTEGER DEFAULT 10,
    max_api_calls_monthly INTEGER DEFAULT 1000,
    max_documents INTEGER DEFAULT 500,
    max_exports_monthly INTEGER DEFAULT 10,
    
    -- Feature access
    features JSONB DEFAULT '{}', -- Feature flags and configurations
    
    -- State licensing
    included_states INTEGER DEFAULT 1, -- Number of states included, -1 for unlimited
    additional_state_cost DECIMAL(10,2) DEFAULT 50.00,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT true, -- Whether plan is publicly available
    sort_order INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization subscriptions
CREATE TABLE organization_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(plan_id),
    
    -- Subscription details
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('trial', 'active', 'suspended', 'cancelled', 'expired')),
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'annual', 'one-time')),
    
    -- Dates
    trial_start_date TIMESTAMPTZ,
    trial_end_date TIMESTAMPTZ,
    subscription_start_date TIMESTAMPTZ DEFAULT NOW(),
    subscription_end_date TIMESTAMPTZ,
    next_billing_date TIMESTAMPTZ,
    
    -- Usage tracking
    current_users INTEGER DEFAULT 0,
    current_claims INTEGER DEFAULT 0,
    current_storage_gb DECIMAL(10,2) DEFAULT 0,
    current_api_calls_monthly INTEGER DEFAULT 0,
    current_documents INTEGER DEFAULT 0,
    current_exports_monthly INTEGER DEFAULT 0,
    
    -- State licensing
    active_state_licenses TEXT[] DEFAULT '{}', -- Array of state codes
    
    -- Billing
    last_payment_date TIMESTAMPTZ,
    last_payment_amount DECIMAL(10,2),
    payment_method_id VARCHAR(255), -- Stripe payment method ID
    
    -- Add-ons and overages
    addon_costs JSONB DEFAULT '{}', -- Additional features or overages
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id)
);

-- Enhanced user roles with hierarchical permissions
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Role identification
    role_code VARCHAR(50) UNIQUE NOT NULL, -- 'admin', 'senior_agent', etc.
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Hierarchy
    role_level INTEGER NOT NULL, -- 1=lowest, 10=highest
    parent_role_code VARCHAR(50) REFERENCES user_roles(role_code),
    
    -- Plan requirements
    minimum_plan_level INTEGER DEFAULT 1, -- Minimum subscription level required
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_system_role BOOLEAN DEFAULT true, -- Cannot be deleted
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Granular permissions system
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Permission identification
    permission_code VARCHAR(100) UNIQUE NOT NULL, -- 'claims:view_all', 'users:create', etc.
    permission_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Categorization
    category VARCHAR(50) NOT NULL, -- 'claims', 'users', 'reports', 'system', etc.
    subcategory VARCHAR(50),
    
    -- Access level
    access_level VARCHAR(20) NOT NULL CHECK (access_level IN ('read', 'write', 'admin', 'owner')),
    
    -- Plan requirements
    minimum_plan_level INTEGER DEFAULT 1,
    requires_feature VARCHAR(100), -- Feature flag that must be enabled
    
    -- Risk level for audit purposes
    risk_level VARCHAR(20) DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Role-permission mappings
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_code VARCHAR(50) NOT NULL REFERENCES user_roles(role_code) ON DELETE CASCADE,
    permission_code VARCHAR(100) NOT NULL REFERENCES permissions(permission_code) ON DELETE CASCADE,
    
    -- Conditional access
    conditions JSONB DEFAULT '{}', -- Additional conditions for this permission
    
    -- Grant details
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(role_code, permission_code)
);

-- User-specific permission overrides
CREATE TABLE user_permission_overrides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_code VARCHAR(100) NOT NULL REFERENCES permissions(permission_code) ON DELETE CASCADE,
    
    -- Override type
    override_type VARCHAR(20) NOT NULL CHECK (override_type IN ('grant', 'deny')),
    
    -- Conditions and expiry
    conditions JSONB DEFAULT '{}',
    expires_at TIMESTAMPTZ,
    
    -- Audit
    granted_by UUID REFERENCES users(id),
    reason TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, permission_code)
);

-- Feature flags tied to subscription plans
CREATE TABLE plan_features (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_id VARCHAR(50) NOT NULL REFERENCES subscription_plans(plan_id) ON DELETE CASCADE,
    
    -- Feature details
    feature_code VARCHAR(100) NOT NULL,
    feature_name VARCHAR(200) NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    
    -- Configuration
    config JSONB DEFAULT '{}', -- Feature-specific configuration
    
    -- Limits specific to this feature
    usage_limit INTEGER, -- NULL for unlimited
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(plan_id, feature_code)
);

-- Usage tracking for billing and limits
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Time period
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'monthly', 'annual')),
    
    -- Usage metrics
    users_count INTEGER DEFAULT 0,
    claims_count INTEGER DEFAULT 0,
    storage_gb DECIMAL(10,2) DEFAULT 0,
    api_calls_count INTEGER DEFAULT 0,
    documents_count INTEGER DEFAULT 0,
    exports_count INTEGER DEFAULT 0,
    
    -- Feature usage
    feature_usage JSONB DEFAULT '{}', -- Track usage of specific features
    
    -- Billing
    overage_charges DECIMAL(10,2) DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id, period_start, period_type)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (plan_id, name, description, monthly_price, annual_price, max_users, max_claims, max_storage_gb, max_api_calls_monthly, features, included_states) VALUES
('bronze', 'Bronze', 'Perfect for individual agents getting started', 29.00, 290.00, 1, 100, 5, 500, '{"basic_search": true, "email_templates": true, "basic_reports": true}', 1),
('silver', 'Silver', 'Ideal for small teams', 79.00, 790.00, 3, 500, 15, 2000, '{"basic_search": true, "email_templates": true, "basic_reports": true, "team_collaboration": true}', 2),
('gold', 'Gold', 'Great for growing agencies', 149.00, 1490.00, 10, 2000, 50, 5000, '{"basic_search": true, "email_templates": true, "basic_reports": true, "team_collaboration": true, "advanced_search": true, "bulk_operations": true}', 5),
('topaz', 'Topaz', 'For established businesses', 299.00, 2990.00, 25, 10000, 200, 15000, '{"basic_search": true, "email_templates": true, "basic_reports": true, "team_collaboration": true, "advanced_search": true, "bulk_operations": true, "advanced_analytics": true, "api_access": true}', 15),
('ruby', 'Ruby', 'For large organizations', 599.00, 5990.00, 100, 50000, 1000, 50000, '{"basic_search": true, "email_templates": true, "basic_reports": true, "team_collaboration": true, "advanced_search": true, "bulk_operations": true, "advanced_analytics": true, "api_access": true, "white_label": true, "priority_support": true}', -1),
('diamond', 'Diamond', 'Enterprise solution', 1299.00, 12990.00, -1, -1, -1, -1, '{"basic_search": true, "email_templates": true, "basic_reports": true, "team_collaboration": true, "advanced_search": true, "bulk_operations": true, "advanced_analytics": true, "api_access": true, "white_label": true, "priority_support": true, "custom_integrations": true, "dedicated_support": true}', -1),
('enterprise', 'Enterprise Custom', 'Custom enterprise solution', 0.00, NULL, -1, -1, -1, -1, '{"everything": true}', -1);

-- Insert default user roles
INSERT INTO user_roles (role_code, role_name, description, role_level, minimum_plan_level) VALUES
('junior_agent', 'Junior Agent', 'Entry-level claim processor', 1, 1),
('senior_agent', 'Senior Agent', 'Experienced agent with team oversight', 3, 2),
('team_lead', 'Team Lead', 'Manages a team of agents', 5, 3),
('manager', 'Manager', 'Department manager with full access', 7, 4),
('admin', 'Administrator', 'Full system administrator', 9, 1),
('compliance', 'Compliance Officer', 'Compliance and audit specialist', 6, 3),
('finance', 'Finance Manager', 'Financial operations manager', 6, 3),
('contractor', 'Contractor', 'External contractor with limited access', 2, 1);

-- Insert comprehensive permissions
INSERT INTO permissions (permission_code, permission_name, category, access_level, minimum_plan_level, risk_level) VALUES
-- Claims permissions
('claims:view_own', 'View Own Claims', 'claims', 'read', 1, 'low'),
('claims:view_team', 'View Team Claims', 'claims', 'read', 2, 'low'),
('claims:view_all', 'View All Claims', 'claims', 'read', 3, 'medium'),
('claims:create', 'Create Claims', 'claims', 'write', 1, 'medium'),
('claims:update_own', 'Update Own Claims', 'claims', 'write', 1, 'medium'),
('claims:update_team', 'Update Team Claims', 'claims', 'write', 2, 'medium'),
('claims:update_all', 'Update All Claims', 'claims', 'write', 3, 'high'),
('claims:delete', 'Delete Claims', 'claims', 'admin', 4, 'critical'),
('claims:assign', 'Assign Claims', 'claims', 'write', 2, 'medium'),
('claims:export', 'Export Claims', 'claims', 'read', 2, 'medium'),
('claims:bulk_operations', 'Bulk Claim Operations', 'claims', 'write', 3, 'high'),

-- User management permissions
('users:view', 'View Users', 'users', 'read', 2, 'low'),
('users:create', 'Create Users', 'users', 'write', 3, 'high'),
('users:update', 'Update Users', 'users', 'write', 3, 'high'),
('users:delete', 'Delete Users', 'users', 'admin', 4, 'critical'),
('users:manage_roles', 'Manage User Roles', 'users', 'admin', 4, 'critical'),

-- Analytics and reporting
('analytics:basic', 'Basic Analytics', 'analytics', 'read', 1, 'low'),
('analytics:advanced', 'Advanced Analytics', 'analytics', 'read', 4, 'low'),
('reports:generate', 'Generate Reports', 'reports', 'read', 2, 'low'),
('reports:schedule', 'Schedule Reports', 'reports', 'write', 3, 'low'),
('reports:custom', 'Custom Report Builder', 'reports', 'write', 4, 'low'),

-- System administration
('system:configure', 'System Configuration', 'system', 'admin', 4, 'critical'),
('system:audit', 'View Audit Logs', 'system', 'read', 3, 'medium'),
('system:backup', 'System Backup', 'system', 'admin', 4, 'critical'),

-- API access
('api:read', 'API Read Access', 'api', 'read', 4, 'medium'),
('api:write', 'API Write Access', 'api', 'write', 4, 'high'),
('api:admin', 'API Administration', 'api', 'admin', 5, 'critical'),

-- Financial operations
('finance:view_payments', 'View Payments', 'finance', 'read', 3, 'medium'),
('finance:process_payments', 'Process Payments', 'finance', 'write', 4, 'high'),
('finance:view_invoices', 'View Invoices', 'finance', 'read', 3, 'medium'),

-- Compliance
('compliance:view_all', 'View All Compliance Data', 'compliance', 'read', 3, 'medium'),
('compliance:audit', 'Perform Audits', 'compliance', 'write', 3, 'medium'),
('compliance:export', 'Export Compliance Data', 'compliance', 'read', 3, 'high');

-- Insert role-permission mappings
INSERT INTO role_permissions (role_code, permission_code) VALUES
-- Junior Agent permissions
('junior_agent', 'claims:view_own'),
('junior_agent', 'claims:create'),
('junior_agent', 'claims:update_own'),
('junior_agent', 'analytics:basic'),

-- Senior Agent permissions (includes junior + additional)
('senior_agent', 'claims:view_own'),
('senior_agent', 'claims:view_team'),
('senior_agent', 'claims:create'),
('senior_agent', 'claims:update_own'),
('senior_agent', 'claims:update_team'),
('senior_agent', 'claims:assign'),
('senior_agent', 'claims:export'),
('senior_agent', 'analytics:basic'),
('senior_agent', 'reports:generate'),
('senior_agent', 'users:view'),

-- Team Lead permissions
('team_lead', 'claims:view_all'),
('team_lead', 'claims:create'),
('team_lead', 'claims:update_all'),
('team_lead', 'claims:assign'),
('team_lead', 'claims:export'),
('team_lead', 'claims:bulk_operations'),
('team_lead', 'analytics:basic'),
('team_lead', 'analytics:advanced'),
('team_lead', 'reports:generate'),
('team_lead', 'reports:schedule'),
('team_lead', 'users:view'),
('team_lead', 'users:create'),

-- Manager permissions
('manager', 'claims:view_all'),
('manager', 'claims:create'),
('manager', 'claims:update_all'),
('manager', 'claims:delete'),
('manager', 'claims:assign'),
('manager', 'claims:export'),
('manager', 'claims:bulk_operations'),
('manager', 'analytics:basic'),
('manager', 'analytics:advanced'),
('manager', 'reports:generate'),
('manager', 'reports:schedule'),
('manager', 'reports:custom'),
('manager', 'users:view'),
('manager', 'users:create'),
('manager', 'users:update'),
('manager', 'users:manage_roles'),
('manager', 'system:audit'),
('manager', 'api:read'),

-- Admin permissions (all permissions)
('admin', 'claims:view_all'),
('admin', 'claims:create'),
('admin', 'claims:update_all'),
('admin', 'claims:delete'),
('admin', 'claims:assign'),
('admin', 'claims:export'),
('admin', 'claims:bulk_operations'),
('admin', 'analytics:basic'),
('admin', 'analytics:advanced'),
('admin', 'reports:generate'),
('admin', 'reports:schedule'),
('admin', 'reports:custom'),
('admin', 'users:view'),
('admin', 'users:create'),
('admin', 'users:update'),
('admin', 'users:delete'),
('admin', 'users:manage_roles'),
('admin', 'system:configure'),
('admin', 'system:audit'),
('admin', 'system:backup'),
('admin', 'api:read'),
('admin', 'api:write'),
('admin', 'api:admin'),

-- Compliance Officer permissions
('compliance', 'claims:view_all'),
('compliance', 'analytics:basic'),
('compliance', 'analytics:advanced'),
('compliance', 'reports:generate'),
('compliance', 'compliance:view_all'),
('compliance', 'compliance:audit'),
('compliance', 'compliance:export'),
('compliance', 'system:audit'),

-- Finance Manager permissions
('finance', 'claims:view_all'),
('finance', 'analytics:basic'),
('finance', 'reports:generate'),
('finance', 'finance:view_payments'),
('finance', 'finance:process_payments'),
('finance', 'finance:view_invoices'),

-- Contractor permissions (limited)
('contractor', 'claims:view_own'),
('contractor', 'claims:update_own'),
('contractor', 'analytics:basic');

-- Create indexes for performance
CREATE INDEX idx_organization_subscriptions_org_id ON organization_subscriptions(organization_id);
CREATE INDEX idx_organization_subscriptions_plan_id ON organization_subscriptions(plan_id);
CREATE INDEX idx_organization_subscriptions_status ON organization_subscriptions(status);
CREATE INDEX idx_role_permissions_role ON role_permissions(role_code);
CREATE INDEX idx_role_permissions_permission ON role_permissions(permission_code);
CREATE INDEX idx_user_permission_overrides_user ON user_permission_overrides(user_id);
CREATE INDEX idx_usage_tracking_org_period ON usage_tracking(organization_id, period_start);
CREATE INDEX idx_permissions_category ON permissions(category);
CREATE INDEX idx_permissions_plan_level ON permissions(minimum_plan_level);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON subscription_plans TO authenticated;
GRANT SELECT, INSERT, UPDATE ON organization_subscriptions TO authenticated;
GRANT SELECT ON user_roles TO authenticated;
GRANT SELECT ON permissions TO authenticated;
GRANT SELECT ON role_permissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_permission_overrides TO authenticated;
GRANT SELECT ON plan_features TO authenticated;
GRANT SELECT, INSERT, UPDATE ON usage_tracking TO authenticated;

-- Row Level Security
ALTER TABLE organization_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permission_overrides ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their organization's subscription" ON organization_subscriptions
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM users WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can view their own permission overrides" ON user_permission_overrides
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can view their organization's usage" ON usage_tracking
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM users WHERE id = auth.uid()
        )
    );
