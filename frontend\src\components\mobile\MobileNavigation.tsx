import React, { useState } from 'react'
import { 
  Home, 
  FileText, 
  Search, 
  Users, 
  BarChart3, 
  Menu, 
  X,
  Bell,
  Settings,
  User
} from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'

interface MobileNavigationProps {
  currentView: string
  onNavigate: (view: string) => void
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  currentView,
  onNavigate
}) => {
  const [showMenu, setShowMenu] = useState(false)
  const { user } = useAuth()

  const mainNavItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'claims', label: 'Claims', icon: FileText },
    { id: 'search', label: 'Search', icon: Search },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 }
  ]

  const menuItems = [
    { id: 'users', label: 'Users', icon: Users },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'organization', label: 'Organization', icon: User }
  ]

  const handleNavigate = (view: string) => {
    onNavigate(view)
    setShowMenu(false)
  }

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AH</span>
            </div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              AssetHunterPro
            </h1>
          </div>
          
          <div className="flex items-center space-x-3">
            <button className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
              <Bell className="h-5 w-5" />
            </button>
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
            >
              {showMenu ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-40">
        <div className="grid grid-cols-4 gap-1">
          {mainNavItems.map((item) => {
            const Icon = item.icon
            const isActive = currentView === item.id
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavigate(item.id)}
                className={`flex flex-col items-center justify-center py-2 px-1 text-xs transition-colors ${
                  isActive
                    ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="truncate">{item.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {showMenu && (
        <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="absolute top-0 right-0 w-80 max-w-full h-full bg-white dark:bg-gray-800 shadow-xl">
            {/* Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-gray-600 dark:text-gray-300" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {user?.first_name} {user?.last_name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {user?.email}
                  </div>
                </div>
              </div>
              <button
                onClick={() => setShowMenu(false)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Menu Items */}
            <div className="py-4">
              <div className="px-4 py-2">
                <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Navigation
                </h3>
              </div>
              
              {mainNavItems.map((item) => {
                const Icon = item.icon
                const isActive = currentView === item.id
                
                return (
                  <button
                    key={item.id}
                    onClick={() => handleNavigate(item.id)}
                    className={`w-full flex items-center px-4 py-3 text-left transition-colors ${
                      isActive
                        ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-600'
                        : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </button>
                )
              })}

              <div className="px-4 py-2 mt-4">
                <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Management
                </h3>
              </div>

              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = currentView === item.id
                
                return (
                  <button
                    key={item.id}
                    onClick={() => handleNavigate(item.id)}
                    className={`w-full flex items-center px-4 py-3 text-left transition-colors ${
                      isActive
                        ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-600'
                        : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </button>
                )
              })}
            </div>

            {/* Menu Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
              <button className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors">
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Spacer for bottom navigation */}
      <div className="lg:hidden h-16"></div>
    </>
  )
}

// Mobile-optimized card component
export const MobileCard: React.FC<{
  title: string
  subtitle?: string
  children: React.ReactNode
  onClick?: () => void
  className?: string
}> = ({ title, subtitle, children, onClick, className = '' }) => {
  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${
        onClick ? 'cursor-pointer hover:shadow-md' : ''
      } ${className}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-3">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {title}
          </h3>
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </div>
      </div>
      {children}
    </div>
  )
}

// Mobile-optimized list component
export const MobileList: React.FC<{
  items: Array<{
    id: string
    title: string
    subtitle?: string
    badge?: string
    onClick?: () => void
  }>
}> = ({ items }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
      {items.map((item) => (
        <div
          key={item.id}
          className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          onClick={item.onClick}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {item.title}
              </h4>
              {item.subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {item.subtitle}
                </p>
              )}
            </div>
            {item.badge && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                {item.badge}
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}

// Mobile-optimized stats grid
export const MobileStatsGrid: React.FC<{
  stats: Array<{
    label: string
    value: string | number
    change?: string
    icon?: React.ComponentType<{ className?: string }>
  }>
}> = ({ stats }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.label}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stat.value}
                </p>
                {stat.change && (
                  <p className="text-xs text-green-600 dark:text-green-400">
                    {stat.change}
                  </p>
                )}
              </div>
              {Icon && (
                <Icon className="h-8 w-8 text-gray-400" />
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
