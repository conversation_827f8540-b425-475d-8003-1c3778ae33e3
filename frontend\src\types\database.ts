// Complete TypeScript interfaces for Asset Recovery Pro Database Schema
// Matches database/schema.sql exactly

// ===================================
// CORE ORGANIZATION & USER MANAGEMENT
// ===================================

export interface Organization {
  id: string
  name: string
  subdomain?: string
  industry?: string
  size?: 'small' | 'medium' | 'large' | 'enterprise'
  established_date?: string

  // Address information
  street_address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string

  // Contact information
  primary_email?: string
  primary_phone?: string
  website?: string

  // Branding configuration
  logo_url?: string
  logo_file_name?: string
  primary_color?: string
  secondary_color?: string
  accent_color?: string

  // Settings
  timezone?: string
  date_format?: string
  currency?: string
  language?: string

  // Status and metadata
  is_active: boolean
  subscription_plan?: string
  created_at: string
  updated_at: string
}

export interface User {
  id: string
  organization_id: string
  email: string
  password_hash: string
  first_name: string
  last_name: string
  role: 'junior_agent' | 'senior_agent' | 'admin' | 'compliance' | 'finance'
  employee_id?: string
  department?: string
  manager_id?: string
  team_id?: string
  is_active: boolean
  last_login_at?: string
  created_at: string
  updated_at: string

  // Populated relations
  organization?: Organization
}

export interface Team {
  id: string
  name: string
  description?: string
  manager_id?: string
  is_active: boolean
  settings: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Permission {
  id: string
  role: string
  resource: string
  actions_allowed: string[]
  created_at: string
}

// ===================================
// CLAIMS MANAGEMENT CORE
// ===================================

export interface Claim {
  id: string
  property_id?: string
  owner_name: string
  owner_first_name?: string
  owner_last_name?: string
  owner_business_name?: string
  amount: number
  property_type?: string
  status: 'new' | 'assigned' | 'contacted' | 'in_progress' | 'documents_requested' | 'under_review' | 'approved' | 'completed' | 'on_hold' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assigned_agent_id?: string
  state: string
  
  // Holder information
  holder_name?: string
  holder_address?: string
  holder_city?: string
  holder_state?: string
  holder_zip?: string
  
  // Owner address information
  owner_address?: string
  owner_city?: string
  owner_state?: string
  owner_zip?: string
  
  // Additional metadata
  report_date?: string
  shares_reported?: number
  securities_name?: string
  cusip?: string
  
  // Processing information
  import_batch_id?: string
  complexity_score: number
  estimated_recovery_amount?: number
  actual_recovery_amount?: number
  commission_rate: number
  commission_amount?: number
  
  // Compliance and audit
  compliance_status: string
  last_contact_date?: string
  next_followup_date?: string
  
  // Timestamps
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface ClaimContact {
  id: string
  claim_id: string
  contact_type: 'phone' | 'email' | 'address'
  contact_value: string
  label?: string
  is_primary: boolean
  is_valid: boolean
  last_verified_at?: string
  verification_notes?: string
  created_at: string
  updated_at: string
}

export interface ClaimActivity {
  id: string
  claim_id: string
  agent_id: string
  activity_type: 'call' | 'email' | 'sms' | 'note' | 'status_change' | 'document_upload' | 'payment' | 'other'
  outcome?: string
  title: string
  description?: string
  contact_method_id?: string
  scheduled_followup_at?: string
  duration_minutes?: number
  attachments: any[]
  metadata: Record<string, any>
  created_at: string
}

export interface ClaimDocument {
  id: string
  claim_id: string
  file_name: string
  file_size?: number
  file_type?: string
  category: 'id_documents' | 'contracts' | 'correspondence' | 'state_forms' | 'signatures' | 'other'
  description?: string
  file_path?: string
  file_url?: string
  uploaded_by: string
  permissions: 'internal' | 'shareable'
  is_signed: boolean
  signature_status?: string
  created_at: string
  updated_at: string
}

export interface ClaimRelationship {
  id: string
  claim_id: string
  person_name: string
  relationship_type: 'spouse' | 'business_partner' | 'heir' | 'legal_representative' | 'power_of_attorney' | 'other'
  contact_info: Record<string, any>
  is_primary_contact: boolean
  documentation_provided: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface ClaimReference {
  id: string
  claim_id: string
  url: string
  description?: string
  category: 'research' | 'verification' | 'contact_info' | 'legal' | 'other'
  added_by: string
  created_at: string
}

// ===================================
// BATCH PROCESSING & DATA MANAGEMENT
// ===================================

export interface ImportBatch {
  id: string
  batch_id: string
  file_name: string
  file_size: number
  state: string
  uploaded_by: string
  total_records: number
  processed_records: number
  valid_records: number
  error_records: number
  duplicate_records: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  storage_type: 'regular' | 'chunked'
  processing_notes?: string
  error_details: any[]
  field_mappings: Record<string, any>
  created_at: string
  completed_at?: string
}

export interface BatchRecord {
  id: string
  batch_id: string
  claim_id?: string
  row_number: number
  raw_data: Record<string, any>
  mapped_data: Record<string, any>
  status: 'pending' | 'processed' | 'error' | 'duplicate'
  error_message?: string
  created_at: string
  processed_at?: string
}

// ===================================
// COMMUNICATION MANAGEMENT
// ===================================

export interface EmailTemplate {
  id: string
  name: string
  subject: string
  body_text: string
  body_html?: string
  state_code?: string
  template_type: 'initial_contact' | 'follow_up' | 'document_request' | 'completion' | 'other'
  is_active: boolean
  version: number
  created_by: string
  created_at: string
  updated_at: string
}

export interface SmsTemplate {
  id: string
  name: string
  content: string
  state_code?: string
  template_type: string
  is_active: boolean
  compliance_approved: boolean
  created_by: string
  created_at: string
}

export interface DocumentTemplate {
  id: string
  name: string
  template_type: 'engagement_letter' | 'power_of_attorney' | 'state_form' | 'contract' | 'other'
  state_code?: string
  content: string
  file_format: string
  version: number
  is_active: boolean
  created_by: string
  created_at: string
  updated_at: string
}

export interface CommunicationLog {
  id: string
  claim_id: string
  agent_id: string
  communication_type: 'call' | 'email' | 'sms'
  direction: 'outbound' | 'inbound'
  recipient_contact?: string
  subject?: string
  content?: string
  template_id?: string
  status: 'sent' | 'delivered' | 'read' | 'failed' | 'bounced'
  external_id?: string
  metadata: Record<string, any>
  sent_at: string
  delivered_at?: string
  read_at?: string
}

// ===================================
// BUSINESS RULES & COMPLIANCE
// ===================================

export interface BusinessRule {
  id: string
  rule_name: string
  rule_type: 'validation' | 'workflow' | 'compliance' | 'fee_cap' | 'auto_assignment'
  state_code?: string
  configuration: Record<string, any>
  is_active: boolean
  priority: number
  created_by: string
  created_at: string
  updated_at: string
}

export interface ComplianceLog {
  id: string
  user_id?: string
  claim_id?: string
  action: string
  entity_type: string
  entity_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
  session_id?: string
  timestamp: string
}

// ===================================
// PERFORMANCE & ANALYTICS
// ===================================

export interface PerformanceMetric {
  id: string
  agent_id?: string
  team_id?: string
  metric_name: string
  metric_value: number
  metric_type: 'count' | 'percentage' | 'amount' | 'duration'
  period_type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  period_start: string
  period_end: string
  metadata: Record<string, any>
  created_at: string
}

export interface SystemLog {
  id: string
  log_level: 'debug' | 'info' | 'warning' | 'error' | 'critical'
  component: string
  action: string
  user_id?: string
  details: Record<string, any>
  execution_time_ms?: number
  timestamp: string
}

// ===================================
// FINANCIAL MANAGEMENT
// ===================================

export interface Payment {
  id: string
  claim_id: string
  payment_type: 'claimant_payout' | 'commission' | 'fee' | 'refund'
  amount: number
  currency: string
  status: 'pending' | 'approved' | 'processed' | 'completed' | 'failed' | 'cancelled'
  payment_method?: string
  external_transaction_id?: string
  recipient_info: Record<string, any>
  processed_by?: string
  approved_by?: string
  scheduled_date?: string
  processed_at?: string
  created_at: string
}

export interface Invoice {
  id: string
  invoice_number: string
  claim_id: string
  amount: number
  tax_amount: number
  total_amount: number
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  due_date?: string
  paid_at?: string
  created_by: string
  created_at: string
  updated_at: string
}

// ===================================
// INTEGRATIONS & EXTERNAL SERVICES
// ===================================

export interface Integration {
  id: string
  service_name: string
  service_type: 'email' | 'sms' | 'payment' | 'document' | 'crm' | 'skip_trace' | 'other'
  configuration: Record<string, any>
  is_active: boolean
  last_sync_at?: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface ApiRequestLog {
  id: string
  integration_id?: string
  request_method: string
  request_url: string
  request_headers?: Record<string, any>
  request_body?: Record<string, any>
  response_status?: number
  response_headers?: Record<string, any>
  response_body?: Record<string, any>
  execution_time_ms?: number
  created_at: string
}

// ===================================
// VIEWS & COMPUTED TYPES
// ===================================

export interface ActiveClaim extends Claim {
  agent_name?: string
  agent_email?: string
  team_name?: string
  activity_count: number
  last_activity_at?: string
}

export interface TeamPerformance {
  team_id: string
  team_name: string
  manager_name?: string
  team_size: number
  total_claims: number
  completed_claims: number
  total_recovery: number
  avg_recovery_per_claim: number
}

// ===================================
// API RESPONSE TYPES
// ===================================

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ClaimWithDetails extends Claim {
  contacts: ClaimContact[]
  activities: ClaimActivity[]
  documents: ClaimDocument[]
  relationships: ClaimRelationship[]
  references: ClaimReference[]
  agent?: User
  assigned_agent?: User
}

// ===================================
// SEARCH & FILTER TYPES
// ===================================

export interface ClaimFilters {
  status?: string[]
  priority?: string[]
  state?: string[]
  assigned_agent_id?: string
  created_after?: string
  created_before?: string
  amount_min?: number
  amount_max?: number
  search_query?: string
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface ActivityFilters {
  claim_id?: string
  agent_id?: string
  activity_type?: string[]
  created_after?: string
  created_before?: string
  outcome?: string[]
}

// ===================================
// FORM DATA TYPES
// ===================================

export interface CreateClaimData {
  owner_name: string
  amount: number
  property_type?: string
  state: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  owner_address?: string
  owner_city?: string
  owner_state?: string
  owner_zip?: string
  estimated_recovery_amount?: number
  notes?: string
}

export interface UpdateClaimData extends Partial<CreateClaimData> {
  status?: Claim['status']
  assigned_agent_id?: string
  next_followup_date?: string
  actual_recovery_amount?: number
  commission_amount?: number
}

export interface CreateActivityData {
  claim_id: string
  activity_type: ClaimActivity['activity_type']
  title: string
  description?: string
  outcome?: string
  contact_method_id?: string
  scheduled_followup_at?: string
  duration_minutes?: number
  metadata?: Record<string, any>
}

// ===================================
// DASHBOARD & ANALYTICS TYPES
// ===================================

export interface DashboardStats {
  total_claims: number
  active_claims: number
  completed_claims: number
  total_recovery: number
  success_rate: number
  team_performance?: number
  recent_activities: ClaimActivity[]
  upcoming_followups: Claim[]
}

export interface AgentPerformance {
  agent_id: string
  agent_name: string
  total_claims: number
  completed_claims: number
  success_rate: number
  total_recovery: number
  avg_days_to_completion: number
  activities_logged: number
  last_activity_date?: string
}

export interface TeamDashboard {
  team: Team
  manager: User
  agents: User[]
  team_stats: DashboardStats
  agent_performance: AgentPerformance[]
  recent_activities: ClaimActivity[]
}

// ===================================
// EXPORT ALL TYPES
// ===================================

export type DatabaseEntity = 
  | User | Team | Permission 
  | Claim | ClaimContact | ClaimActivity | ClaimDocument | ClaimRelationship | ClaimReference
  | ImportBatch | BatchRecord
  | EmailTemplate | SmsTemplate | DocumentTemplate | CommunicationLog
  | BusinessRule | ComplianceLog
  | PerformanceMetric | SystemLog
  | Payment | Invoice
  | Integration | ApiRequestLog 