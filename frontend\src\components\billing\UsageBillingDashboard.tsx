import React, { useState, useEffect } from 'react'
import { 
  DollarSign, 
  TrendingUp, 
  Activity, 
  AlertTriangle, 
  Download,
  Calendar,
  BarChart3,
  Zap,
  Database,
  Mail,
  MessageSquare,
  Search,
  FileText,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { usageBillingService, type UsageAggregate, type UpsellOpportunity } from '../../services/usageBillingService'
import { useAuth } from '../../contexts/AuthContext'

interface UsageDashboardData {
  currentUsage: UsageAggregate[]
  projectedCost: number
  upsellOpportunities: UpsellOpportunity[]
  usageHistory: UsageAggregate[]
}

export const UsageBillingDashboard: React.FC = () => {
  const { user } = useAuth()
  const [data, setData] = useState<UsageDashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('current')
  const [showProjections, setShowProjections] = useState(false)

  useEffect(() => {
    if (user?.organization_id) {
      loadUsageData()
    }
  }, [user?.organization_id, selectedPeriod])

  const loadUsageData = async () => {
    if (!user?.organization_id) return

    setIsLoading(true)
    try {
      const [currentUsage, upsellOpportunities, usageHistory] = await Promise.all([
        usageBillingService.getCurrentUsage(user.organization_id),
        usageBillingService.getUpsellOpportunities(user.organization_id),
        usageBillingService.getUsageHistory(user.organization_id, undefined, 6)
      ])

      // Calculate projected cost for current month
      const projectedUsage = currentUsage.reduce((acc, usage) => {
        acc[usage.meter_code] = usage.total_quantity
        return acc
      }, {} as Record<string, number>)

      const { total_cost: projectedCost } = await usageBillingService.calculateProjectedCosts(
        user.organization_id,
        projectedUsage
      )

      setData({
        currentUsage,
        projectedCost,
        upsellOpportunities,
        usageHistory
      })
    } catch (error) {
      console.error('Error loading usage data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getMeterIcon = (meterCode: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      api_calls: Activity,
      storage_gb: Database,
      document_processing: FileText,
      sms_messages: MessageSquare,
      email_sends: Mail,
      data_exports: Download,
      advanced_search: Search,
      report_generation: BarChart3
    }
    return icons[meterCode] || Zap
  }

  const getMeterColor = (meterCode: string) => {
    const colors: Record<string, string> = {
      api_calls: 'blue',
      storage_gb: 'purple',
      document_processing: 'green',
      sms_messages: 'orange',
      email_sends: 'pink',
      data_exports: 'indigo',
      advanced_search: 'yellow',
      report_generation: 'red'
    }
    return colors[meterCode] || 'gray'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatUsage = (quantity: number, meterCode: string) => {
    const units: Record<string, string> = {
      api_calls: 'calls',
      storage_gb: 'GB',
      document_processing: 'docs',
      sms_messages: 'SMS',
      email_sends: 'emails',
      data_exports: 'exports',
      advanced_search: 'searches',
      report_generation: 'reports'
    }

    const unit = units[meterCode] || 'units'
    
    if (quantity >= 1000000) {
      return `${(quantity / 1000000).toFixed(1)}M ${unit}`
    } else if (quantity >= 1000) {
      return `${(quantity / 1000).toFixed(1)}K ${unit}`
    } else {
      return `${quantity.toFixed(meterCode === 'storage_gb' ? 2 : 0)} ${unit}`
    }
  }

  const getUsagePercentage = (current: number, included: number) => {
    if (included <= 0) return 0
    return Math.min((current / included) * 100, 100)
  }

  const handleUpsellResponse = async (opportunityId: string, response: 'accepted' | 'declined') => {
    const success = await usageBillingService.respondToUpsell(opportunityId, response)
    if (success) {
      loadUsageData() // Refresh data
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">No usage data available</p>
      </div>
    )
  }

  const totalCurrentCost = data.currentUsage.reduce((sum, usage) => sum + usage.total_cost, 0)

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Usage & Billing
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor your usage and optimize costs
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="current">Current Month</option>
            <option value="last">Last Month</option>
            <option value="quarter">This Quarter</option>
          </select>

          <button
            onClick={() => setShowProjections(!showProjections)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            {showProjections ? 'Hide' : 'Show'} Projections
          </button>
        </div>
      </div>

      {/* Cost Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Month</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatCurrency(totalCurrentCost)}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Projected Month</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatCurrency(data.projectedCost)}
              </p>
              <div className="flex items-center mt-1">
                {data.projectedCost > totalCurrentCost ? (
                  <ArrowUp className="h-4 w-4 text-red-500 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 text-green-500 mr-1" />
                )}
                <span className={`text-sm ${
                  data.projectedCost > totalCurrentCost ? 'text-red-600' : 'text-green-600'
                }`}>
                  {Math.abs(((data.projectedCost - totalCurrentCost) / Math.max(totalCurrentCost, 1)) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Optimization</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {data.upsellOpportunities.length}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                opportunities
              </p>
            </div>
            <Zap className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Usage Breakdown */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
          Usage Breakdown
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data.currentUsage.map((usage) => {
            const Icon = getMeterIcon(usage.meter_code)
            const color = getMeterColor(usage.meter_code)
            const percentage = getUsagePercentage(usage.total_quantity, usage.included_quantity)
            const isOverage = usage.overage_quantity > 0

            return (
              <div key={usage.meter_code} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Icon className={`h-5 w-5 text-${color}-600`} />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                      {usage.meter_code.replace(/_/g, ' ')}
                    </span>
                  </div>
                  {isOverage && (
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Used</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {formatUsage(usage.total_quantity, usage.meter_code)}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Included</span>
                    <span className="text-gray-900 dark:text-gray-100">
                      {formatUsage(usage.included_quantity, usage.meter_code)}
                    </span>
                  </div>

                  {isOverage && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-orange-600 dark:text-orange-400">Overage</span>
                      <span className="font-medium text-orange-600 dark:text-orange-400">
                        {formatUsage(usage.overage_quantity, usage.meter_code)}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Cost</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(usage.total_cost)}
                    </span>
                  </div>

                  {/* Usage bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          percentage >= 100 
                            ? 'bg-red-500' 
                            : percentage >= 80 
                            ? 'bg-orange-500' 
                            : `bg-${color}-500`
                        }`}
                        style={{ width: `${Math.min(percentage, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {percentage.toFixed(1)}% used
                      </span>
                      {percentage >= 80 && (
                        <span className="text-xs text-orange-600 dark:text-orange-400">
                          Near limit
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Upsell Opportunities */}
      {data.upsellOpportunities.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Optimization Opportunities
          </h3>

          <div className="space-y-4">
            {data.upsellOpportunities.map((opportunity) => (
              <div key={opportunity.id} className="border border-blue-200 dark:border-blue-800 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Upgrade to {opportunity.recommended_plan.charAt(0).toUpperCase() + opportunity.recommended_plan.slice(1)}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      You're using {opportunity.trigger_value} {opportunity.trigger_metric}. 
                      Upgrading could save you {formatCurrency(opportunity.potential_savings)} monthly.
                    </p>
                    {opportunity.discount_percent && (
                      <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                        Limited time: {opportunity.discount_percent}% discount on upgrade
                      </p>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 ml-4">
                    <button
                      onClick={() => handleUpsellResponse(opportunity.id, 'declined')}
                      className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                    >
                      Not Now
                    </button>
                    <button
                      onClick={() => handleUpsellResponse(opportunity.id, 'accepted')}
                      className="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                    >
                      Upgrade Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Usage History Chart */}
      {showProjections && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Usage Trends
          </h3>
          
          <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Usage trend chart would be rendered here</p>
              <p className="text-sm">Integration with charting library needed</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
