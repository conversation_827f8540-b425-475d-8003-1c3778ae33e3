import React, { useState, useEffect } from 'react'
import { Building2, Save, AlertCircle, Check } from 'lucide-react'
import { LogoUpload } from '../components/organization/LogoUpload'
import { organizationService } from '../services/organizationService'
import type { Organization, OrganizationUpdateData } from '../services/organizationService'

export const OrganizationSettings: React.FC = () => {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [formData, setFormData] = useState<OrganizationUpdateData>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setSaving] = useState(false)
  const [canManage, setCanManage] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    loadOrganization()
  }, [])

  const loadOrganization = async () => {
    try {
      setIsLoading(true)
      const org = await organizationService.getCurrentUserOrganization()
      
      if (org) {
        setOrganization(org)
        setFormData({
          name: org.name,
          subdomain: org.subdomain || '',
          industry: org.industry || '',
          size: org.size,
          established_date: org.established_date || '',
          street_address: org.street_address || '',
          city: org.city || '',
          state: org.state || '',
          zip_code: org.zip_code || '',
          country: org.country || 'United States',
          primary_email: org.primary_email || '',
          primary_phone: org.primary_phone || '',
          website: org.website || '',
          primary_color: org.primary_color || '#3B82F6',
          secondary_color: org.secondary_color || '#1E40AF',
          accent_color: org.accent_color || '#10B981',
          timezone: org.timezone || 'UTC',
          date_format: org.date_format || 'MM/DD/YYYY',
          currency: org.currency || 'USD',
          language: org.language || 'en'
        })

        // Check permissions
        const canManageOrg = await organizationService.canManageOrganization(org.id)
        setCanManage(canManageOrg)
      }
    } catch (error) {
      console.error('Error loading organization:', error)
      setError('Failed to load organization settings')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: keyof OrganizationUpdateData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError(null)
    setSuccess(null)
  }

  const handleSave = async () => {
    if (!organization || !canManage) return

    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const result = await organizationService.updateOrganization(organization.id, formData)
      
      if (result.success) {
        setSuccess('Organization settings saved successfully!')
        // Reload organization data
        await loadOrganization()
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(result.error || 'Failed to save organization settings')
      }
    } catch (error) {
      console.error('Error saving organization:', error)
      setError('An unexpected error occurred')
    } finally {
      setSaving(false)
    }
  }

  const handleLogoUpdated = (logoUrl: string | null) => {
    if (organization) {
      setOrganization({ ...organization, logo_url: logoUrl })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!organization) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Organization Not Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Unable to load organization settings. Please contact support.
        </p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Building2 className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Organization Settings
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your organization's profile and branding
            </p>
          </div>
        </div>
        
        {canManage && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="flex items-center space-x-2 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md text-green-600 dark:text-green-400">
          <Check className="h-5 w-5" />
          <span>{success}</span>
        </div>
      )}

      {error && (
        <div className="flex items-center space-x-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-600 dark:text-red-400">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}

      {!canManage && (
        <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
          <p className="text-yellow-800 dark:text-yellow-200 text-sm">
            You don't have permission to modify organization settings. Contact your administrator.
          </p>
        </div>
      )}

      {/* Logo Upload Section */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <LogoUpload
          organization={organization}
          onLogoUpdated={handleLogoUpdated}
          canManage={canManage}
        />
      </div>

      {/* Basic Information */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Basic Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Organization Name *
            </label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              disabled={!canManage}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder="Enter organization name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Subdomain
            </label>
            <input
              type="text"
              value={formData.subdomain || ''}
              onChange={(e) => handleInputChange('subdomain', e.target.value)}
              disabled={!canManage}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder="your-company"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Industry
            </label>
            <input
              type="text"
              value={formData.industry || ''}
              onChange={(e) => handleInputChange('industry', e.target.value)}
              disabled={!canManage}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder="Asset Recovery"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Organization Size
            </label>
            <select
              value={formData.size || ''}
              onChange={(e) => handleInputChange('size', e.target.value)}
              disabled={!canManage}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="">Select size</option>
              <option value="small">Small (1-10 employees)</option>
              <option value="medium">Medium (11-50 employees)</option>
              <option value="large">Large (51-200 employees)</option>
              <option value="enterprise">Enterprise (200+ employees)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}
