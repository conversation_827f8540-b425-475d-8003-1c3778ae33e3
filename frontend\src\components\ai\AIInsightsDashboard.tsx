import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb, 
  Target, 
  Users,
  DollarSign,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  PieChart,
  Activity,
  Sparkles
} from 'lucide-react'
import { aiInsightsService, type AIInsight, type PredictiveModel, type MarketIntelligence } from '../../services/aiInsightsService'
import { useAuth } from '../../contexts/AuthContext'

interface AIInsightsDashboardData {
  insights: AIInsight[]
  models: PredictiveModel[]
  marketIntelligence: MarketIntelligence[]
  resourceOptimization: any
}

export const AIInsightsDashboard: React.FC = () => {
  const { user } = useAuth()
  const [data, setData] = useState<AIInsightsDashboardData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedInsight, setSelectedInsight] = useState<AIInsight | null>(null)

  useEffect(() => {
    if (user?.organization_id) {
      loadAIData()
    }
  }, [user?.organization_id, selectedCategory])

  const loadAIData = async () => {
    if (!user?.organization_id) return

    setIsLoading(true)
    try {
      const [insights, models, marketIntelligence, resourceOptimization] = await Promise.all([
        aiInsightsService.getAIInsights(user.organization_id, selectedCategory === 'all' ? undefined : selectedCategory),
        aiInsightsService.getPredictiveModels(user.organization_id),
        aiInsightsService.getMarketIntelligence(),
        aiInsightsService.optimizeResourceAllocation(user.organization_id)
      ])

      setData({
        insights,
        models,
        marketIntelligence,
        resourceOptimization
      })
    } catch (error) {
      console.error('Error loading AI data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return Target
      case 'risk': return AlertTriangle
      case 'optimization': return Zap
      case 'trend': return TrendingUp
      case 'anomaly': return Activity
      default: return Lightbulb
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'green'
      case 'risk': return 'red'
      case 'optimization': return 'blue'
      case 'trend': return 'purple'
      case 'anomaly': return 'orange'
      default: return 'gray'
    }
  }

  const getImpactColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
    if (score >= 60) return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400'
    if (score >= 40) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
    return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400'
  }

  const handleInsightAction = async (insightId: string, action: 'acknowledged' | 'implemented' | 'dismissed') => {
    const success = await aiInsightsService.updateInsightStatus(insightId, action)
    if (success) {
      loadAIData() // Refresh data
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">No AI insights available</p>
      </div>
    )
  }

  const categories = [
    { id: 'all', label: 'All Insights', icon: Brain },
    { id: 'operational_efficiency', label: 'Operations', icon: Users },
    { id: 'revenue_optimization', label: 'Revenue', icon: DollarSign },
    { id: 'customer_retention', label: 'Retention', icon: Target },
    { id: 'risk_management', label: 'Risk', icon: AlertTriangle }
  ]

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              AI Insights Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Intelligent recommendations powered by machine learning
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Sparkles className="h-4 w-4 text-yellow-500" />
            <span>{data.insights.length} active insights</span>
          </div>
        </div>
      </div>

      {/* AI Models Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {data.models.map((model) => (
          <div key={model.model_id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {model.model_name}
              </h3>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                model.status === 'active' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
              }`}>
                {model.status}
              </span>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Accuracy</span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {model.accuracy.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Training Data</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {model.training_data_size.toLocaleString()} records
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Last Trained</span>
                <span className="text-gray-900 dark:text-gray-100">
                  {new Date(model.last_trained).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Accuracy bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    model.accuracy >= 90 
                      ? 'bg-green-500' 
                      : model.accuracy >= 80 
                      ? 'bg-blue-500' 
                      : 'bg-yellow-500'
                  }`}
                  style={{ width: `${model.accuracy}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Category Filter */}
      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        {categories.map(category => {
          const Icon = category.icon
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{category.label}</span>
            </button>
          )
        })}
      </div>

      {/* AI Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
              Active Insights
            </h3>

            <div className="space-y-4">
              {data.insights.filter(insight => insight.status === 'active').map((insight) => {
                const Icon = getInsightIcon(insight.insight_type)
                const color = getInsightColor(insight.insight_type)

                return (
                  <div key={insight.insight_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-4">
                      <div className={`w-10 h-10 bg-${color}-100 dark:bg-${color}-900/20 rounded-lg flex items-center justify-center`}>
                        <Icon className={`h-5 w-5 text-${color}-600 dark:text-${color}-400`} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {insight.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getImpactColor(insight.impact_score)}`}>
                              Impact: {insight.impact_score}
                            </span>
                            <div className="flex items-center space-x-1">
                              <Sparkles className="h-3 w-3 text-yellow-500" />
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {insight.confidence}%
                              </span>
                            </div>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {insight.description}
                        </p>

                        {/* Recommendations */}
                        {insight.recommendations.length > 0 && (
                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
                            <h5 className="text-xs font-medium text-gray-900 dark:text-gray-100 mb-2">
                              Recommended Actions:
                            </h5>
                            {insight.recommendations.map((rec, index) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="text-gray-600 dark:text-gray-400">
                                  {rec.title}
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className={`px-2 py-0.5 rounded-full ${
                                    rec.priority === 'critical' 
                                      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                      : rec.priority === 'high'
                                      ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                                  }`}>
                                    {rec.priority}
                                  </span>
                                  {rec.estimated_impact.revenue_change && (
                                    <span className="text-green-600 dark:text-green-400 font-medium">
                                      {formatCurrency(rec.estimated_impact.revenue_change)}
                                    </span>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Actions */}
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleInsightAction(insight.insight_id, 'acknowledged')}
                            className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Acknowledge
                          </button>
                          
                          <button
                            onClick={() => handleInsightAction(insight.insight_id, 'implemented')}
                            className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30"
                          >
                            <Zap className="h-3 w-3 mr-1" />
                            Implement
                          </button>
                          
                          <button
                            onClick={() => handleInsightAction(insight.insight_id, 'dismissed')}
                            className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            Dismiss
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Resource Optimization & Market Intelligence */}
        <div className="space-y-6">
          {/* Resource Optimization */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Resource Optimization
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Current Efficiency</span>
                <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {data.resourceOptimization.current_efficiency}%
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Optimized Efficiency</span>
                <span className="text-lg font-bold text-green-600 dark:text-green-400">
                  {data.resourceOptimization.optimized_efficiency}%
                </span>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                <div className="text-sm font-medium text-green-800 dark:text-green-200">
                  Potential Improvement: +{(data.resourceOptimization.optimized_efficiency - data.resourceOptimization.current_efficiency).toFixed(1)}%
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Recommended Changes:
                </h4>
                {data.resourceOptimization.recommendations.slice(0, 3).map((rec: any, index: number) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="text-gray-600 dark:text-gray-400">
                      {rec.agent}: {rec.cases_to_transfer > 0 ? '+' : ''}{rec.cases_to_transfer} cases
                    </span>
                    <span className="text-green-600 dark:text-green-400">
                      +{rec.efficiency_gain.toFixed(1)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Market Intelligence */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Market Intelligence
            </h3>
            
            <div className="space-y-4">
              {data.marketIntelligence.slice(0, 2).map((intel, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {intel.market_trend}
                    </h4>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className={`h-4 w-4 ${
                        intel.trend_direction === 'up' ? 'text-green-500' : 
                        intel.trend_direction === 'down' ? 'text-red-500' : 'text-gray-500'
                      }`} />
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {intel.confidence}%
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    {intel.impact_on_business}
                  </p>
                  
                  <div className="text-xs text-blue-600 dark:text-blue-400">
                    {intel.recommended_actions.slice(0, 2).map((action, i) => (
                      <div key={i}>• {action}</div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
