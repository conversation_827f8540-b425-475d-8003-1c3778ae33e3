# Organization Logo Upload Feature

This document explains how to set up and use the new organization logo upload functionality in AssetHunterPro.

## Overview

The organization logo feature allows each company/tenant to upload their own custom logo, which will be displayed in the application header and throughout the interface. This enhances branding and provides a more personalized experience for each organization.

## Features

- **Custom Logo Upload**: Upload company logos in various formats (JPEG, PNG, GIF, WebP, SVG)
- **Automatic Resizing**: Logos are automatically displayed at appropriate sizes
- **Secure Storage**: Logos are stored securely in Supabase Storage with proper access controls
- **Fallback Display**: Graceful fallback to default branding when no logo is uploaded
- **Permission-Based Access**: Only admins and senior agents can upload/manage logos
- **Multi-tenant Support**: Each organization has its own logo storage space

## Setup Instructions

### 1. Database Migration

Run the migration script to add the organizations table and storage setup:

```sql
-- Run this in your Supabase SQL editor or psql
\i frontend/database/migrate-add-organizations.sql
```

### 2. Storage Bucket Setup

The migration script automatically creates the `company-logos` storage bucket, but you can also run the setup manually:

```sql
\i frontend/database/setup-logo-storage.sql
```

### 3. Environment Variables

Ensure your `.env` file has the required Supabase configuration:

```env
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## Usage

### Accessing Organization Settings

1. Log in as an admin or senior agent
2. Navigate to the sidebar and click "Organization"
3. You'll see the Organization Settings page with logo upload functionality

### Uploading a Logo

1. In the Organization Settings page, find the "Company Logo" section
2. Click "Upload Logo" or "Change Logo" if one already exists
3. Select an image file (max 5MB, supported formats: JPEG, PNG, GIF, WebP, SVG)
4. The logo will be uploaded and immediately displayed in the application

### Removing a Logo

1. In the Organization Settings page, click "Remove" next to the current logo
2. Confirm the removal when prompted
3. The application will revert to the default branding

## Technical Details

### Database Schema

The `organizations` table includes these logo-related fields:

```sql
-- Branding configuration
logo_url VARCHAR(500),           -- Public URL of the uploaded logo
logo_file_name VARCHAR(255),     -- Storage filename for management
primary_color VARCHAR(7),        -- Brand primary color
secondary_color VARCHAR(7),      -- Brand secondary color  
accent_color VARCHAR(7)          -- Brand accent color
```

### Storage Structure

Logos are stored in the `company-logos` bucket with this structure:

```
company-logos/
├── {organization-id}/
│   └── logo-{timestamp}.{ext}
```

### File Restrictions

- **Maximum file size**: 5MB
- **Supported formats**: JPEG, JPG, PNG, GIF, WebP, SVG
- **Recommended dimensions**: 200x60px (will be automatically resized)
- **Recommended format**: PNG with transparent background

### Security

- **Row Level Security (RLS)**: Enabled on storage objects
- **Upload permissions**: Only admins and senior agents can upload
- **View permissions**: All organization members can view their logo
- **Public access**: Logos are publicly viewable for branding display
- **Organization isolation**: Each organization can only access their own logos

## Components

### OrganizationLogo Component

Displays the organization logo with fallback to default branding:

```tsx
import { OrganizationLogo } from '../components/organization/OrganizationLogo'

// Full logo with text
<OrganizationLogo isExpanded={true} />

// Compact logo only
<OrganizationLogoCompact />
```

### LogoUpload Component

Handles logo upload functionality:

```tsx
import { LogoUpload } from '../components/organization/LogoUpload'

<LogoUpload
  organization={organization}
  onLogoUpdated={handleLogoUpdate}
  canManage={userCanManage}
/>
```

### OrganizationService

Service class for organization management:

```tsx
import { organizationService } from '../services/organizationService'

// Upload logo
const result = await organizationService.uploadLogo(orgId, file)

// Remove logo
const result = await organizationService.removeLogo(orgId)

// Get organization
const org = await organizationService.getCurrentUserOrganization()
```

## Troubleshooting

### Logo Not Displaying

1. Check that the file was uploaded successfully
2. Verify the user has permission to view the logo
3. Check browser console for any CORS or loading errors
4. Ensure the storage bucket policies are correctly configured

### Upload Failures

1. Verify file size is under 5MB
2. Check that file format is supported
3. Ensure user has admin or senior_agent role
4. Check Supabase storage quota and permissions

### Permission Issues

1. Verify the user belongs to the correct organization
2. Check that the user has admin or senior_agent role
3. Ensure RLS policies are properly configured
4. Check that the organization is active

## Migration Notes

For existing installations:

1. All existing users will be assigned to a default organization
2. The default organization will use standard AssetHunterPro branding
3. Admins can immediately start uploading custom logos
4. No existing functionality is affected

## Future Enhancements

Planned improvements include:

- **Multiple logo variants** (light/dark theme support)
- **Logo approval workflow** for enterprise accounts
- **Brand color customization** through the UI
- **Logo usage analytics** and reporting
- **Bulk organization management** for multi-tenant deployments

## Support

For issues or questions about the organization logo feature:

1. Check the browser console for error messages
2. Verify database migration completed successfully
3. Test with a small PNG file first
4. Contact support with specific error details
