import { useState, useEffect } from 'react'

interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface PWAState {
  isInstallable: boolean
  isInstalled: boolean
  isOnline: boolean
  isStandalone: boolean
  installPrompt: PWAInstallPrompt | null
  showInstallBanner: boolean
}

export function usePWA() {
  const [pwaState, setPWAState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    isStandalone: false,
    installPrompt: null,
    showInstallBanner: false
  })

  useEffect(() => {
    // Check if app is running in standalone mode
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://')

    // Check if app is already installed
    const isInstalled = isStandalone || localStorage.getItem('pwa-installed') === 'true'

    setPWAState(prev => ({
      ...prev,
      isStandalone,
      isInstalled
    }))

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      const installEvent = e as any
      
      setPWAState(prev => ({
        ...prev,
        isInstallable: true,
        installPrompt: installEvent,
        showInstallBanner: !isInstalled
      }))
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      console.log('PWA was installed')
      localStorage.setItem('pwa-installed', 'true')
      
      setPWAState(prev => ({
        ...prev,
        isInstalled: true,
        isInstallable: false,
        installPrompt: null,
        showInstallBanner: false
      }))
    }

    // Listen for online/offline events
    const handleOnline = () => {
      setPWAState(prev => ({ ...prev, isOnline: true }))
    }

    const handleOffline = () => {
      setPWAState(prev => ({ ...prev, isOnline: false }))
    }

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const installApp = async () => {
    if (!pwaState.installPrompt) return false

    try {
      await pwaState.installPrompt.prompt()
      const choiceResult = await pwaState.installPrompt.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt')
        return true
      } else {
        console.log('User dismissed the install prompt')
        return false
      }
    } catch (error) {
      console.error('Error installing PWA:', error)
      return false
    }
  }

  const dismissInstallBanner = () => {
    setPWAState(prev => ({ ...prev, showInstallBanner: false }))
    localStorage.setItem('pwa-install-dismissed', Date.now().toString())
  }

  const canShowInstallBanner = () => {
    const dismissed = localStorage.getItem('pwa-install-dismissed')
    if (!dismissed) return true
    
    // Show banner again after 7 days
    const dismissedTime = parseInt(dismissed)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
    
    return dismissedTime < sevenDaysAgo
  }

  return {
    ...pwaState,
    installApp,
    dismissInstallBanner,
    canShowInstallBanner: canShowInstallBanner()
  }
}

// Hook for managing service worker
export function useServiceWorker() {
  const [swState, setSwState] = useState({
    isSupported: 'serviceWorker' in navigator,
    isRegistered: false,
    isUpdating: false,
    hasUpdate: false,
    registration: null as ServiceWorkerRegistration | null
  })

  useEffect(() => {
    if (!swState.isSupported) return

    const registerSW = async () => {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        
        setSwState(prev => ({
          ...prev,
          isRegistered: true,
          registration
        }))

        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (!newWorker) return

          setSwState(prev => ({ ...prev, isUpdating: true }))

          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setSwState(prev => ({
                ...prev,
                hasUpdate: true,
                isUpdating: false
              }))
            }
          })
        })

        console.log('Service Worker registered successfully')
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }

    registerSW()
  }, [swState.isSupported])

  const updateServiceWorker = () => {
    if (swState.registration?.waiting) {
      swState.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }

  return {
    ...swState,
    updateServiceWorker
  }
}

// Hook for managing offline functionality
export function useOfflineSync() {
  const [offlineActions, setOfflineActions] = useState<any[]>([])
  const [isSyncing, setIsSyncing] = useState(false)

  useEffect(() => {
    // Listen for sync messages from service worker
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'SYNC_SUCCESS') {
        setOfflineActions(prev => 
          prev.filter(action => action.id !== event.data.action.id)
        )
      }
    }

    navigator.serviceWorker?.addEventListener('message', handleMessage)

    return () => {
      navigator.serviceWorker?.removeEventListener('message', handleMessage)
    }
  }, [])

  const addOfflineAction = (action: any) => {
    const actionWithId = { ...action, id: Date.now().toString() }
    setOfflineActions(prev => [...prev, actionWithId])
    
    // Store in IndexedDB for persistence
    // Implementation would use IndexedDB
    
    return actionWithId.id
  }

  const syncOfflineActions = async () => {
    if (!navigator.serviceWorker?.ready) return

    setIsSyncing(true)
    
    try {
      const registration = await navigator.serviceWorker.ready
      await registration.sync.register('background-sync-claims')
    } catch (error) {
      console.error('Background sync registration failed:', error)
    } finally {
      setIsSyncing(false)
    }
  }

  return {
    offlineActions,
    isSyncing,
    addOfflineAction,
    syncOfflineActions,
    hasOfflineActions: offlineActions.length > 0
  }
}

// Hook for push notifications
export function usePushNotifications() {
  const [notificationState, setNotificationState] = useState({
    isSupported: 'Notification' in window && 'serviceWorker' in navigator,
    permission: Notification.permission,
    isSubscribed: false,
    subscription: null as PushSubscription | null
  })

  useEffect(() => {
    checkSubscription()
  }, [])

  const checkSubscription = async () => {
    if (!notificationState.isSupported) return

    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      
      setNotificationState(prev => ({
        ...prev,
        isSubscribed: !!subscription,
        subscription
      }))
    } catch (error) {
      console.error('Error checking push subscription:', error)
    }
  }

  const requestPermission = async () => {
    if (!notificationState.isSupported) return false

    try {
      const permission = await Notification.requestPermission()
      setNotificationState(prev => ({ ...prev, permission }))
      return permission === 'granted'
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      return false
    }
  }

  const subscribeToPush = async () => {
    if (!notificationState.isSupported || notificationState.permission !== 'granted') {
      return false
    }

    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.VITE_VAPID_PUBLIC_KEY
      })

      setNotificationState(prev => ({
        ...prev,
        isSubscribed: true,
        subscription
      }))

      // Send subscription to server
      // Implementation would send to your backend
      console.log('Push subscription:', subscription)

      return true
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
      return false
    }
  }

  const unsubscribeFromPush = async () => {
    if (!notificationState.subscription) return false

    try {
      await notificationState.subscription.unsubscribe()
      
      setNotificationState(prev => ({
        ...prev,
        isSubscribed: false,
        subscription: null
      }))

      return true
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
      return false
    }
  }

  return {
    ...notificationState,
    requestPermission,
    subscribeToPush,
    unsubscribeFromPush
  }
}
