# 🛡️ SuperUser Administrator Dashboard Setup Guide

## Overview

This guide sets up a comprehensive SuperUser administrator dashboard that provides platform-level control over AssetHunterPro without accessing tenant data directly.

---

## 🔧 **SETUP INSTRUCTIONS**

### **1. Database Setup**

Run the SuperUser schema migration:

```sql
-- Execute in your Supabase SQL editor
\i frontend/database/superuser-schema.sql
```

This creates:
- `superusers` table for admin accounts
- `platform_stats` for system metrics
- `system_health` for monitoring
- `organization_admin` for tenant management
- `superuser_audit_logs` for compliance
- `system_alerts` for notifications
- `feature_flags` for extensions

### **2. Create Your SuperUser Account**

```sql
-- Update the password hash with your actual bcrypt hash
UPDATE superusers 
SET password_hash = '$2b$12$your_actual_bcrypt_hash_here'
WHERE email = '<EMAIL>';

-- Or create a new SuperUser account
INSERT INTO superusers (
    email,
    password_hash,
    name,
    permissions
) VALUES (
    '<EMAIL>',
    '$2b$12$your_bcrypt_hash',
    'Your Name',
    ARRAY[
        'platform:admin',
        'organizations:manage',
        'users:manage',
        'system:monitor',
        'billing:manage',
        'support:access',
        'security:audit',
        'extensions:manage'
    ]
);
```

### **3. Generate Password Hash**

Use bcrypt to generate your password hash:

```javascript
// Node.js example
const bcrypt = require('bcrypt');
const password = 'your-secure-password';
const hash = bcrypt.hashSync(password, 12);
console.log(hash);
```

### **4. Update RouteRenderer**

Add SuperUser routes to your RouteRenderer:

```tsx
// In RouteRenderer.tsx
import { SuperUserDashboard } from '../pages/SuperUserDashboard'
import { SuperUserGuard } from '../pages/SuperUserLogin'

// Add to View type
export type View = '...' | 'superuser-dashboard'

// Add to switch statement
case 'superuser-dashboard':
  return (
    <SuperUserGuard>
      <SuperUserDashboard />
    </SuperUserGuard>
  )
```

### **5. Add SuperUser Navigation**

Add a hidden route for SuperUser access:

```tsx
// Access via: /admin or special URL
// Add to your main navigation logic
if (window.location.pathname === '/admin') {
  setCurrentView('superuser-dashboard')
}
```

---

## 🎯 **FEATURES OVERVIEW**

### **Platform Administration**
- ✅ **Organization Management**: Suspend/reactivate accounts
- ✅ **User Limits**: Set storage, user, and API limits
- ✅ **Billing Control**: Manage subscriptions and plans
- ✅ **Support Priority**: Set support levels per organization

### **System Monitoring**
- ✅ **Health Checks**: Monitor all system services
- ✅ **Performance Metrics**: Response times, uptime, errors
- ✅ **Resource Usage**: CPU, memory, disk, database
- ✅ **Alert Management**: Critical system notifications

### **Security & Compliance**
- ✅ **Audit Logging**: All SuperUser actions logged
- ✅ **Access Control**: Permission-based operations
- ✅ **Security Events**: Failed logins, suspicious activity
- ✅ **Compliance Reports**: SOX, GDPR audit trails

### **Feature Management**
- ✅ **Feature Flags**: Enable/disable features per organization
- ✅ **Rollout Control**: Gradual feature deployment
- ✅ **A/B Testing**: Feature experimentation
- ✅ **Extension Management**: Plugin control

---

## 🔐 **SECURITY FEATURES**

### **Authentication**
- **Separate Auth**: Independent from tenant authentication
- **MFA Support**: Two-factor authentication ready
- **Session Management**: Secure session handling
- **Failed Login Protection**: Automatic lockout

### **Authorization**
- **Permission-Based**: Granular permission system
- **Audit Trail**: All actions logged with context
- **IP Tracking**: Source IP logging
- **Session Monitoring**: Active session tracking

### **Data Protection**
- **Tenant Isolation**: No direct access to tenant data
- **Encrypted Storage**: Sensitive data encryption
- **Secure Communications**: HTTPS/TLS only
- **Data Retention**: Configurable retention policies

---

## 📊 **MONITORING CAPABILITIES**

### **Platform Metrics**
```typescript
interface PlatformStats {
  total_organizations: number
  active_organizations: number
  new_organizations: number
  total_users: number
  active_users: number
  storage_used_gb: number
  avg_response_time_ms: number
  error_rate: number
  uptime_percentage: number
  total_revenue?: number
  mrr?: number // Monthly Recurring Revenue
}
```

### **System Health**
```typescript
interface SystemHealth {
  service_name: string
  status: 'healthy' | 'degraded' | 'down' | 'maintenance'
  response_time_ms?: number
  cpu_usage?: number
  memory_usage?: number
  disk_usage?: number
  error_count: number
}
```

### **Alert System**
```typescript
interface SystemAlert {
  alert_type: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  title: string
  message: string
  source_service?: string
  status: 'active' | 'acknowledged' | 'resolved'
}
```

---

## 🏢 **ORGANIZATION MANAGEMENT**

### **Suspension Controls**
```typescript
// Suspend organization
await superUserService.suspendOrganization(
  organizationId,
  "Billing issue - payment failed"
)

// Reactivate organization
await superUserService.reactivateOrganization(organizationId)
```

### **Limit Management**
```typescript
// Update organization limits
await superUserService.updateOrganizationLimits(organizationId, {
  user_limit: 50,
  storage_limit_gb: 100,
  api_calls_limit: 10000,
  subscription_plan: 'enterprise',
  support_priority: 'high'
})
```

### **Billing Integration**
- **Subscription Status**: Active, trial, suspended, cancelled
- **Usage Tracking**: Current vs. limits
- **Billing Alerts**: Overages and renewals
- **Revenue Reporting**: MRR and growth metrics

---

## 🚀 **FEATURE FLAGS**

### **Flag Management**
```typescript
interface FeatureFlag {
  flag_name: string
  display_name: string
  is_global: boolean
  target_organizations: string[]
  target_plans: string[]
  is_enabled: boolean
  rollout_percentage: number
  config: Record<string, any>
}
```

### **Usage Examples**
```typescript
// Enable feature for specific organizations
await superUserService.updateFeatureFlag(flagId, {
  is_enabled: true,
  target_organizations: ['org-1', 'org-2'],
  rollout_percentage: 50
})

// Global feature rollout
await superUserService.updateFeatureFlag(flagId, {
  is_global: true,
  is_enabled: true,
  rollout_percentage: 100
})
```

---

## 📋 **OPERATIONAL PROCEDURES**

### **Daily Tasks**
1. **Review System Health**: Check all services are healthy
2. **Monitor Alerts**: Address any critical alerts
3. **Check Growth Metrics**: Review new organizations/users
4. **Audit Security Events**: Review failed logins and suspicious activity

### **Weekly Tasks**
1. **Performance Review**: Analyze response times and errors
2. **Capacity Planning**: Review storage and user growth
3. **Feature Flag Review**: Assess rollout progress
4. **Support Priority Review**: Adjust organization priorities

### **Monthly Tasks**
1. **Compliance Audit**: Review audit logs for compliance
2. **Security Review**: Assess security events and patterns
3. **Revenue Analysis**: Review MRR and growth trends
4. **System Optimization**: Performance tuning and cleanup

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

**SuperUser Login Fails**
- Verify email exists in `superusers` table
- Check password hash is correct
- Ensure `is_active = true`
- Review audit logs for failed attempts

**Organization Suspension Not Working**
- Check SuperUser has `organizations:manage` permission
- Verify organization exists in `organization_admin` table
- Review function logs for errors

**System Health Not Updating**
- Check health monitoring service is running
- Verify database connections
- Review `system_health` table for recent entries

### **Debug Commands**
```sql
-- Check SuperUser status
SELECT * FROM superusers WHERE email = '<EMAIL>';

-- Review recent audit logs
SELECT * FROM superuser_audit_logs 
ORDER BY created_at DESC LIMIT 10;

-- Check system health
SELECT * FROM system_health 
ORDER BY checked_at DESC LIMIT 5;

-- Review active alerts
SELECT * FROM system_alerts 
WHERE status = 'active' 
ORDER BY severity DESC, created_at DESC;
```

---

## 🎉 **ACCESS YOUR DASHBOARD**

1. **Navigate to**: `https://your-domain.com/admin`
2. **Login with**: Your SuperUser credentials
3. **Start managing**: Organizations, system health, and features

**Your SuperUser dashboard is now ready for platform administration!** 🛡️

This gives you complete control over the AssetHunterPro platform while maintaining security and compliance standards.
